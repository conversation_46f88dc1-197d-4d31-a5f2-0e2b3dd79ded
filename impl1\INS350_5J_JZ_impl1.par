PAR: Place And Route Diamond (64-bit) 3.12.1.454.
Copyright (c) 1991-1994 by NeoCAD Inc. All rights reserved.
Copyright (c) 1995 AT&T Corp.   All rights reserved.
Copyright (c) 1995-2001 Lucent Technologies Inc.  All rights reserved.
Copyright (c) 2001 Agere Systems   All rights reserved.
Copyright (c) 2002-2020 Lattice Semiconductor Corporation,  All rights reserved.
Thu Sep 25 10:29:42 2025

D:/Software/lscc/diamond/3.12/ispfpga\bin\nt64\par -f INS350_5J_JZ_impl1.p2t
INS350_5J_JZ_impl1_map.ncd INS350_5J_JZ_impl1.dir INS350_5J_JZ_impl1.prf -gui
-msgset D:/Project/TLH50_03J_JZ_20250925/promote.xml


Preference file: INS350_5J_JZ_impl1.prf.

Level/       Number       Worst        Timing       Worst        Timing       Run          NCD
Cost [ncd]   Unrouted     Slack        Score        Slack(hold)  Score(hold)  Time         Status
----------   --------     -----        ------       -----------  -----------  ----         ------
5_1   *      0            1.227        0            0.174        0            22           Completed

* : Design saved.

Total (real) run time for 1-seed: 22 secs 

par done!

Note: user must run 'Trace' for timing closure signoff.

Lattice Place and Route Report for Design "INS350_5J_JZ_impl1_map.ncd"
Thu Sep 25 10:29:42 2025

PAR: Place And Route Diamond (64-bit) 3.12.1.454.
Command Line: par -w -l 5 -i 6 -t 1 -c 0 -e 0 -gui -msgset D:/Project/TLH50_03J_JZ_20250925/promote.xml -exp parUseNBR=1:parCDP=auto:parCDR=1:parPathBased=OFF:parASE=1 INS350_5J_JZ_impl1_map.ncd INS350_5J_JZ_impl1.dir/5_1.ncd INS350_5J_JZ_impl1.prf
Preference file: INS350_5J_JZ_impl1.prf.
Placement level-cost: 5-1.
Routing Iterations: 6

Loading design for application par from file INS350_5J_JZ_impl1_map.ncd.
Design name: INS350_5J_JZ
NCD version: 3.3
Vendor:      LATTICE
Device:      LFE5U-25F
Package:     CABGA256
Performance: 7
Loading device for application par from file 'sa5p25.nph' in environment: D:/Software/lscc/diamond/3.12/ispfpga.
Package Status:                     Final          Version 1.42.
Performance Hardware Data Status:   Final          Version 55.1.
License checked out.


Ignore Preference Error(s):  True
Device utilization summary:

   PIO (prelim)      32/197          16% used
                     32/197          16% bonded
   IOLOGIC           28/199          14% used

   SLICE            697/12144         5% used

   GSR                1/1           100% used
   EBR                2/56            3% used
   PLL                1/2            50% used
   MULT18             4/28           14% used
   ALU54              1/14            7% used


Number of Signals: 2184
Number of Connections: 4201

Pin Constraint Summary:
   32 out of 32 pins locked (100% locked).


The following 12 signals are selected to use the primary clock routing resources:
    clk120mhz (driver: CLK120/PLLInst_0, clk/ce/sr load #: 446/0/0)
    clk_AD (driver: CLK120/PLLInst_0, clk/ce/sr load #: 101/0/0)
    wendu.clk_us (driver: wendu/SLICE_241, clk/ce/sr load #: 36/0/0)
    signal_process/demodu/AD_validcnt7 (driver: signal_process/demodu/SLICE_285, clk/ce/sr load #: 0/0/33)
    signal_process/rs422/trans_state[5] (driver: signal_process/rs422/SLICE_482, clk/ce/sr load #: 0/0/31)
    signal_process/rs422/N_80_i (driver: signal_process/rs422/SLICE_627, clk/ce/sr load #: 0/31/0)
    signal_process/demodu/N_410_i (driver: signal_process/demodu/SLICE_594, clk/ce/sr load #: 0/29/0)
    signal_process/polarity (driver: signal_process/ctrl_signal/SLICE_405, clk/ce/sr load #: 0/28/0)
    signal_process/integ/DA_dout5 (driver: signal_process/integ/SLICE_641, clk/ce/sr load #: 0/28/0)
    signal_process/demodu/median_sum_n_1_sqmuxa (driver: signal_process/demodu/SLICE_624, clk/ce/sr load #: 0/28/0)
    signal_process/demodu/Latch_sum (driver: signal_process/demodu/SLICE_287, clk/ce/sr load #: 0/28/0)
    signal_process/demodu/N_417_i (driver: signal_process/demodu/SLICE_624, clk/ce/sr load #: 0/28/0)


Signal CLK120/locked_out is selected as Global Set/Reset.
Starting Placer Phase 0.
..............
Finished Placer Phase 0.  REAL time: 3 secs 


Starting Placer Phase 1.
......................
Placer score = 383024.
Finished Placer Phase 1.  REAL time: 9 secs 

Starting Placer Phase 2.
.
Placer score =  319973
Finished Placer Phase 2.  REAL time: 9 secs 


------------------ Clock Report ------------------

Global Clock Resources:
  CLK_PIN    : 0 out of 12 (0%)
  GR_PCLK    : 0 out of 12 (0%)
  PLL        : 1 out of 2 (50%)
  DCS        : 0 out of 2 (0%)
  DCC        : 0 out of 60 (0%)
  CLKDIV     : 0 out of 4 (0%)

Quadrant TL Clocks:
  PRIMARY "clk120mhz" from CLKOP on comp "CLK120/PLLInst_0" on PLL site "PLL_BR0", CLK/CE/SR load = 193
  PRIMARY "clk_AD" from CLKOS on comp "CLK120/PLLInst_0" on PLL site "PLL_BR0", CLK/CE/SR load = 8
  PRIMARY "signal_process/demodu/AD_validcnt7" from F0 on comp "signal_process/demodu/SLICE_285" on site "R22C23C", CLK/CE/SR load = 4
  PRIMARY "signal_process/rs422/trans_state[5]" from Q0 on comp "signal_process/rs422/SLICE_482" on site "R23C31D", CLK/CE/SR load = 31
  PRIMARY "signal_process/rs422/N_80_i" from F1 on comp "signal_process/rs422/SLICE_627" on site "R18C33D", CLK/CE/SR load = 31
  PRIMARY "signal_process/polarity" from Q0 on comp "signal_process/ctrl_signal/SLICE_405" on site "R19C21A", CLK/CE/SR load = 27

  PRIMARY  : 6 out of 16 (37%)

Quadrant TR Clocks:
  PRIMARY "clk120mhz" from CLKOP on comp "CLK120/PLLInst_0" on PLL site "PLL_BR0", CLK/CE/SR load = 33

  PRIMARY  : 1 out of 16 (6%)

Quadrant BL Clocks:
  PRIMARY "clk120mhz" from CLKOP on comp "CLK120/PLLInst_0" on PLL site "PLL_BR0", CLK/CE/SR load = 151
  PRIMARY "clk_AD" from CLKOS on comp "CLK120/PLLInst_0" on PLL site "PLL_BR0", CLK/CE/SR load = 93
  PRIMARY "wendu.clk_us" from Q0 on comp "wendu/SLICE_241" on site "R38C31A", CLK/CE/SR load = 19
  PRIMARY "signal_process/demodu/AD_validcnt7" from F0 on comp "signal_process/demodu/SLICE_285" on site "R22C23C", CLK/CE/SR load = 29
  PRIMARY "signal_process/demodu/N_410_i" from F0 on comp "signal_process/demodu/SLICE_594" on site "R23C24C", CLK/CE/SR load = 29
  PRIMARY "signal_process/polarity" from Q0 on comp "signal_process/ctrl_signal/SLICE_405" on site "R19C21A", CLK/CE/SR load = 1
  PRIMARY "signal_process/integ/DA_dout5" from F0 on comp "signal_process/integ/SLICE_641" on site "R17C21A", CLK/CE/SR load = 28
  PRIMARY "signal_process/demodu/median_sum_n_1_sqmuxa" from F1 on comp "signal_process/demodu/SLICE_624" on site "R19C20B", CLK/CE/SR load = 28
  PRIMARY "signal_process/demodu/Latch_sum" from Q0 on comp "signal_process/demodu/SLICE_287" on site "R23C24A", CLK/CE/SR load = 28
  PRIMARY "signal_process/demodu/N_417_i" from F0 on comp "signal_process/demodu/SLICE_624" on site "R19C20B", CLK/CE/SR load = 28

  PRIMARY  : 10 out of 16 (62%)

Quadrant BR Clocks:
  PRIMARY "clk120mhz" from CLKOP on comp "CLK120/PLLInst_0" on PLL site "PLL_BR0", CLK/CE/SR load = 69
  PRIMARY "wendu.clk_us" from Q0 on comp "wendu/SLICE_241" on site "R38C31A", CLK/CE/SR load = 17

  PRIMARY  : 2 out of 16 (12%)

Edge Clocks:

  No edge clock selected.


--------------- End of Clock Report ---------------


+
I/O Usage Summary (final):
   32 out of 197 (16.2%) PIO sites used.
   32 out of 197 (16.2%) bonded PIO sites used.
   Number of PIO comps: 32; differential: 0.
   Number of Vref pins used: 0.

I/O Bank Usage Summary:
+----------+----------------+------------+------------+------------+
| I/O Bank | Usage          | Bank Vccio | Bank Vref1 | Bank Vref2 |
+----------+----------------+------------+------------+------------+
| 0        | 0 / 24 (  0%)  | 3.3V       | -          | -          |
| 1        | 1 / 32 (  3%)  | 3.3V       | -          | -          |
| 2        | 0 / 32 (  0%)  | 3.3V       | -          | -          |
| 3        | 3 / 32 (  9%)  | 3.3V       | -          | -          |
| 6        | 13 / 32 ( 40%) | 3.3V       | -          | -          |
| 7        | 15 / 32 ( 46%) | 3.3V       | -          | -          |
| 8        | 0 / 13 (  0%)  | 3.3V       | -          | -          |
+----------+----------------+------------+------------+------------+

---------------------------------- DSP Report ----------------------------------

DSP Slice #:           1  2  3  4  5  6  7  8  9 10 11 12 13 14
# of MULT9                                                     
# of MULT18               1  2  1                              
# of ALU24                                                     
# of ALU54                   1                                 
# of PRADD9                                                    
# of PRADD18                                                   

DSP Slice  2         Component_Type       Physical_Type                    Instance_Name                  
  MULT18_R13C9         MULT18X18D             MULT18          signal_process/modu/un1_dout_mult[26:0]     

DSP Slice  3         Component_Type       Physical_Type                    Instance_Name                  
 MULT18_R13C13         MULT18X18D             MULT18            signal_process/rs422/un3_p_sum[0:35]      
 MULT18_R13C14         MULT18X18D             MULT18           signal_process/rs422/un3_p_sum[18:53]      
  ALU54_R13C16           ALU54B               ALU54           signal_process/rs422/un3_p_sum_add[0:53]    

DSP Slice  4         Component_Type       Physical_Type                    Instance_Name                  
 MULT18_R13C17         MULT18X18D             MULT18           signal_process/rs422/un3_p_sum[36:71]      

------------------------------ End of DSP Report -------------------------------
Total placer CPU time: 8 secs 

Dumping design to file INS350_5J_JZ_impl1.dir/5_1.ncd.

0 connections routed; 4201 unrouted.
Starting router resource preassignment
DSP info: No dsp pins have been swapped.

Completed router resource preassignment. Real time: 13 secs 

Start NBR router at 10:29:56 09/25/25

*****************************************************************
Info: NBR allows conflicts(one node used by more than one signal)
      in the earlier iterations. In each iteration, it tries to  
      solve the conflicts while keeping the critical connections 
      routed as short as possible. The routing process is said to
      be completed when no conflicts exist and all connections   
      are routed.                                                
Note: NBR uses a different method to calculate timing slacks. The
      worst slack and total negative slack may not be the same as
      that in TRCE report. You should always run TRCE to verify  
      your design.                                               
*****************************************************************

Start NBR special constraint process at 10:29:56 09/25/25

Start NBR section for initial routing at 10:29:56 09/25/25
Level 1, iteration 1
0(0.00%) conflict; 2539(60.44%) untouched conns; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.127ns/0.000ns; real time: 15 secs 
Level 2, iteration 1
5(0.00%) conflicts; 2516(59.89%) untouched conns; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 15 secs 
Level 3, iteration 1
18(0.00%) conflicts; 2233(53.15%) untouched conns; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 15 secs 
Level 4, iteration 1
147(0.01%) conflicts; 0(0.00%) untouched conn; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 16 secs 

Info: Initial congestion level at 75% usage is 0
Info: Initial congestion area  at 75% usage is 0 (0.00%)

Start NBR section for normal routing at 10:29:58 09/25/25
Level 1, iteration 1
5(0.00%) conflicts; 160(3.81%) untouched conns; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 16 secs 
Level 1, iteration 2
5(0.00%) conflicts; 160(3.81%) untouched conns; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 16 secs 
Level 2, iteration 1
5(0.00%) conflicts; 160(3.81%) untouched conns; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 16 secs 
Level 3, iteration 1
5(0.00%) conflicts; 156(3.71%) untouched conns; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 16 secs 
Level 4, iteration 1
72(0.01%) conflicts; 0(0.00%) untouched conn; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 16 secs 
Level 4, iteration 2
31(0.00%) conflicts; 0(0.00%) untouched conn; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 16 secs 
Level 4, iteration 3
15(0.00%) conflicts; 0(0.00%) untouched conn; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 16 secs 
Level 4, iteration 4
7(0.00%) conflicts; 0(0.00%) untouched conn; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 16 secs 
Level 4, iteration 5
4(0.00%) conflicts; 0(0.00%) untouched conn; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 17 secs 
Level 4, iteration 6
2(0.00%) conflicts; 0(0.00%) untouched conn; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 17 secs 
Level 4, iteration 7
0(0.00%) conflict; 0(0.00%) untouched conn; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 17 secs 

Start NBR section for setup/hold timing optimization with effort level 3 at 10:29:59 09/25/25

Start NBR section for re-routing at 10:30:00 09/25/25
Level 4, iteration 1
0(0.00%) conflict; 0(0.00%) untouched conn; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 18 secs 

Start NBR section for post-routing at 10:30:00 09/25/25

End NBR router with 0 unrouted connection

NBR Summary
-----------
  Number of unrouted connections : 0 (0.00%)
  Number of connections with timing violations : 0 (0.00%)
  Estimated worst slack<setup> : 1.227ns
  Timing score<setup> : 0
-----------
Notes: The timing info is calculated for SETUP only and all PAR_ADJs are ignored.



Total CPU time 21 secs 
Total REAL time: 22 secs 
Completely routed.
End of route.  4201 routed (100.00%); 0 unrouted.

Hold time timing score: 0, hold timing errors: 0

Timing score: 0 

Dumping design to file INS350_5J_JZ_impl1.dir/5_1.ncd.


All signals are completely routed.


PAR_SUMMARY::Run status = Completed
PAR_SUMMARY::Number of unrouted conns = 0
PAR_SUMMARY::Worst  slack<setup/<ns>> = 1.227
PAR_SUMMARY::Timing score<setup/<ns>> = 0.000
PAR_SUMMARY::Worst  slack<hold /<ns>> = 0.174
PAR_SUMMARY::Timing score<hold /<ns>> = 0.000
PAR_SUMMARY::Number of errors = 0

Total CPU  time to completion: 21 secs 
Total REAL time to completion: 22 secs 

par done!

Note: user must run 'Trace' for timing closure signoff.

Copyright (c) 1991-1994 by NeoCAD Inc. All rights reserved.
Copyright (c) 1995 AT&T Corp.   All rights reserved.
Copyright (c) 1995-2001 Lucent Technologies Inc.  All rights reserved.
Copyright (c) 2001 Agere Systems   All rights reserved.
Copyright (c) 2002-2020 Lattice Semiconductor Corporation,  All rights reserved.
