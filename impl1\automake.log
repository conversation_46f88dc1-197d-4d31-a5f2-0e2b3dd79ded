
synpwrap -msg -prj "INS350_5J_JZ_impl1_synplify.tcl" -log "INS350_5J_JZ_impl1.srf"
Copyright (C) 1992-2020 Lattice Semiconductor Corporation. All rights reserved.
Lattice Diamond Version 3.12.1.454
    <postMsg mid="2011000" type="Info"    dynamic="0" navigation="0"  />

==contents of INS350_5J_JZ_impl1.srf
#Build: Synplify Pro (R) R-2021.03L-SP1-1, Build 223R, Dec 22 2021
#install: D:\Software\lscc\diamond\3.12\synpbase
#OS: Windows 8 6.2
#Hostname: TLH-022

# Thu Sep 25 10:29:18 2025

#Implementation: impl1


Copyright (C) 1994-2021 Synopsys, Inc.
This Synopsys software and all associated documentation are proprietary to Synopsys, Inc.
and may only be used pursuant to the terms and conditions of a written license agreement
with Synopsys, Inc. All other use, reproduction, modification, or distribution of the
Synopsys software or the associated documentation is strictly prohibited.
Tool: Synplify Pro (R)
Build: R-2021.03L-SP1-1
Install: D:\Software\lscc\diamond\3.12\synpbase
OS: Windows 6.2

Hostname: TLH-022

Implementation : impl1
Synopsys HDL Compiler, Version comp202103synp2, Build 222R, Built Dec 22 2021 00:18:12, @

@N|Running in 64-bit mode
###########################################################[

Copyright (C) 1994-2021 Synopsys, Inc.
This Synopsys software and all associated documentation are proprietary to Synopsys, Inc.
and may only be used pursuant to the terms and conditions of a written license agreement
with Synopsys, Inc. All other use, reproduction, modification, or distribution of the
Synopsys software or the associated documentation is strictly prohibited.
Tool: Synplify Pro (R)
Build: R-2021.03L-SP1-1
Install: D:\Software\lscc\diamond\3.12\synpbase
OS: Windows 6.2

Hostname: TLH-022

Implementation : impl1
Synopsys Verilog Compiler, Version comp202103synp2, Build 222R, Built Dec 22 2021 00:18:12, @

@N|Running in 64-bit mode
@I::"D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v" (library work)
@I::"D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\pmi_def.v" (library work)
@I::"D:\Software\lscc\diamond\3.12\synpbase\lib\vlog\hypermods.v" (library __hyper__lib__)
@I::"D:\Software\lscc\diamond\3.12\synpbase\lib\vlog\umr_capim.v" (library snps_haps)
@I::"D:\Software\lscc\diamond\3.12\synpbase\lib\vlog\scemi_objects.v" (library snps_haps)
@I::"D:\Software\lscc\diamond\3.12\synpbase\lib\vlog\scemi_pipes.svh" (library snps_haps)
@I::"D:\Project\TLH50_03J_JZ_20250925\Src_al\INS350_5J_JZ.v" (library work)
@I::"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\SignalProcessing.v" (library work)
@I::"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\speed_select_Tx.v" (library work)
@I::"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\SquareWaveGenerator.v" (library work)
@I::"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\UART_Control.v" (library work)
@I::"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\uart_tx.v" (library work)
@I::"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\SignalGenerator.v" (library work)
@I::"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Ctrl_Data.v" (library work)
@I::"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\DS18B20.v" (library work)
@I::"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Integration.v" (library work)
@I::"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v" (library work)
@I::"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Rs422Output.v" (library work)
@W: CS141 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Rs422Output.v":55:34:55:37|Unrecognized synthesis directive keep. Verify the correct directive name.
@W: CS141 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Rs422Output.v":62:33:62:36|Unrecognized synthesis directive keep. Verify the correct directive name.
@I::"D:\Project\TLH50_03J_JZ_20250925\Src_al\Demodulation.v" (library work)
@I::"D:\Project\TLH50_03J_JZ_20250925\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v" (library work)
@I::"D:\Project\TLH50_03J_JZ_20250925\IP_al\global_clock1\global_clock\global_clock.v" (library work)
Verilog syntax check successful!
Options changed - recompiling
Selecting top level module INS350_5J_JZ
@N: CG364 :"D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v":757:7:757:9|Synthesizing module VHI in library work.
Running optimization stage 1 on VHI .......
Finished optimization stage 1 on VHI (CPU Time 0h:00m:00s, Memory Used current: 110MB peak: 110MB)
@N: CG364 :"D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v":761:7:761:9|Synthesizing module VLO in library work.
Running optimization stage 1 on VLO .......
Finished optimization stage 1 on VLO (CPU Time 0h:00m:00s, Memory Used current: 110MB peak: 110MB)
@N: CG364 :"D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v":1696:7:1696:13|Synthesizing module EHXPLLL in library work.
Running optimization stage 1 on EHXPLLL .......
Finished optimization stage 1 on EHXPLLL (CPU Time 0h:00m:00s, Memory Used current: 110MB peak: 111MB)
@N: CG364 :"D:\Project\TLH50_03J_JZ_20250925\IP_al\global_clock1\global_clock\global_clock.v":8:7:8:18|Synthesizing module global_clock in library work.
Running optimization stage 1 on global_clock .......
@W: CL168 :"D:\Project\TLH50_03J_JZ_20250925\IP_al\global_clock1\global_clock\global_clock.v":23:8:23:21|Removing instance scuba_vhi_inst because it does not drive other instances. To preserve this instance, use the syn_noprune synthesis directive.
Finished optimization stage 1 on global_clock (CPU Time 0h:00m:00s, Memory Used current: 110MB peak: 111MB)
@N: CG364 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\DS18B20.v":17:7:17:13|Synthesizing module DS18B20 in library work.
@N: CG179 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\DS18B20.v":85:12:85:17|Removing redundant assignment.
@N: CG179 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\DS18B20.v":189:20:189:27|Removing redundant assignment.
@N: CG179 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\DS18B20.v":248:20:248:27|Removing redundant assignment.
@N: CG179 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\DS18B20.v":291:15:291:18|Removing redundant assignment.
@N: CG179 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\DS18B20.v":307:21:307:29|Removing redundant assignment.
Running optimization stage 1 on DS18B20 .......
@A: CL282 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\DS18B20.v":157:0:157:5|Feedback mux created for signal rd_flag. It is possible a set/reset assignment for this is signal missing. To improve timing and area, specify a set/reset value.
@W: CL190 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\DS18B20.v":157:0:157:5|Optimizing register bit dq_out to a constant 0. To keep the instance, apply constraint syn_preserve=1 on the instance.
@N: CL189 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\DS18B20.v":157:0:157:5|Register bit rd_flag is always 1.
@W: CL169 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\DS18B20.v":157:0:157:5|Pruning unused register dq_out. Make sure that there are no unused intermediate registers.
Finished optimization stage 1 on DS18B20 (CPU Time 0h:00m:00s, Memory Used current: 112MB peak: 113MB)
@N: CG364 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\speed_select_Tx.v":17:7:17:21|Synthesizing module speed_select_Tx in library work.

	SYS_FREQ=32'b00000111001001110000111000000000
	UART_BAUD=32'b00000000000000011100001000000000
	BPS_PARA=32'b00000000000000000000010000010001
	BPS_PARA_2=32'b00000000000000000000001000001000
   Generated name = speed_select_Tx_120000000s_115200s_1041s_520s
@W: CG133 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\speed_select_Tx.v":34:16:34:24|Object uart_ctrl is declared but not assigned. Either assign a value or remove the declaration.
Running optimization stage 1 on speed_select_Tx_120000000s_115200s_1041s_520s .......
Finished optimization stage 1 on speed_select_Tx_120000000s_115200s_1041s_520s (CPU Time 0h:00m:00s, Memory Used current: 112MB peak: 113MB)
@N: CG364 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\uart_tx.v":16:7:16:13|Synthesizing module uart_tx in library work.
@N: CG179 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\uart_tx.v":53:12:53:16|Removing redundant assignment.
@N: CG179 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\uart_tx.v":54:13:54:19|Removing redundant assignment.
@N: CG179 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\uart_tx.v":55:17:55:27|Removing redundant assignment.
Running optimization stage 1 on uart_tx .......
@W: CL169 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\uart_tx.v":69:0:69:5|Pruning unused register odd_bit. Make sure that there are no unused intermediate registers.
Finished optimization stage 1 on uart_tx (CPU Time 0h:00m:00s, Memory Used current: 112MB peak: 113MB)
@N: CG364 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Ctrl_Data.v":15:7:15:15|Synthesizing module Ctrl_Data in library work.
@N: CG179 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Ctrl_Data.v":64:53:64:63|Removing redundant assignment.
@N: CG179 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Ctrl_Data.v":69:57:69:69|Removing redundant assignment.
@N: CG179 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Ctrl_Data.v":85:14:85:21|Removing redundant assignment.
@N: CG179 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Ctrl_Data.v":117:24:117:33|Removing redundant assignment.
@N: CG179 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Ctrl_Data.v":129:22:129:31|Removing redundant assignment.
@N: CG179 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Ctrl_Data.v":141:22:141:31|Removing redundant assignment.
@N: CG179 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Ctrl_Data.v":153:22:153:31|Removing redundant assignment.
@N: CG179 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Ctrl_Data.v":165:22:165:31|Removing redundant assignment.
@N: CG179 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Ctrl_Data.v":177:22:177:31|Removing redundant assignment.
@N: CG179 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Ctrl_Data.v":189:22:189:31|Removing redundant assignment.
@N: CG179 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Ctrl_Data.v":201:22:201:31|Removing redundant assignment.
Running optimization stage 1 on Ctrl_Data .......
Finished optimization stage 1 on Ctrl_Data (CPU Time 0h:00m:00s, Memory Used current: 112MB peak: 113MB)
@N: CG364 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\UART_Control.v":16:7:16:18|Synthesizing module UART_Control in library work.

	iWID_RS422=32'b00000000000000000000000000100000
	RX_SYS_FREQ=32'b00000111001001110000111000000000
	RX_UART_BAUD=32'b00000000000000011100001000000000
	TX_SYS_FREQ=32'b00000111001001110000111000000000
	TX_UART_BAUD=32'b00000000000000011100001000000000
   Generated name = UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1
@W: CG360 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\UART_Control.v":31:16:31:22|Removing wire rx_data, as there is no assignment to it.
Running optimization stage 1 on UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1 .......
@W: CL318 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\UART_Control.v":31:16:31:22|*Output rx_data has undriven bits; assigning undriven bits to 'Z'.  Simulation mismatch possible. Assign all bits of the output.
Finished optimization stage 1 on UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1 (CPU Time 0h:00m:00s, Memory Used current: 112MB peak: 113MB)
@N: CG364 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\SignalGenerator.v":42:7:42:21|Synthesizing module SignalGenerator in library work.

	iWID_TRANS=32'b00000000000000000000000000001101
	iTRANSIT_TIME=32'b00000000000000000000000010110110
	iAD_VALID_START=32'b00000000000000000000000001000110
	iDA=32'b00000000000000000000000000000011
	iAD_VALID_END=32'b00000000000000000000000010100110
	iDEMODU=32'b00000000000000000000000010101001
	iINTEGRATE=32'b00000000000000000000000010101101
	iMODU=32'b00000000000000000000000010110000
   Generated name = SignalGenerator_13s_182s_70s_3s_166s_169s_173s_176s
@N: CG179 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\SignalGenerator.v":80:15:80:25|Removing redundant assignment.
@W: CG133 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\SignalGenerator.v":56:16:56:27|Object output_drive is declared but not assigned. Either assign a value or remove the declaration.
Running optimization stage 1 on SignalGenerator_13s_182s_70s_3s_166s_169s_173s_176s .......
Finished optimization stage 1 on SignalGenerator_13s_182s_70s_3s_166s_169s_173s_176s (CPU Time 0h:00m:00s, Memory Used current: 112MB peak: 113MB)
@N: CG364 :"D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v":25:7:25:10|Synthesizing module AND2 in library work.
Running optimization stage 1 on AND2 .......
Finished optimization stage 1 on AND2 (CPU Time 0h:00m:00s, Memory Used current: 113MB peak: 113MB)
@N: CG364 :"D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v":367:7:367:9|Synthesizing module INV in library work.
Running optimization stage 1 on INV .......
Finished optimization stage 1 on INV (CPU Time 0h:00m:00s, Memory Used current: 113MB peak: 113MB)
@N: CG364 :"D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v":810:7:810:10|Synthesizing module XOR2 in library work.
Running optimization stage 1 on XOR2 .......
Finished optimization stage 1 on XOR2 (CPU Time 0h:00m:00s, Memory Used current: 113MB peak: 113MB)
@N: CG364 :"D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v":710:7:710:14|Synthesizing module ROM16X1A in library work.
Running optimization stage 1 on ROM16X1A .......
Finished optimization stage 1 on ROM16X1A (CPU Time 0h:00m:00s, Memory Used current: 113MB peak: 113MB)
@N: CG364 :"D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v":959:7:959:14|Synthesizing module PDPW16KD in library work.
Running optimization stage 1 on PDPW16KD .......
Finished optimization stage 1 on PDPW16KD (CPU Time 0h:00m:00s, Memory Used current: 113MB peak: 113MB)
@N: CG364 :"D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v":119:7:119:13|Synthesizing module FD1P3DX in library work.
Running optimization stage 1 on FD1P3DX .......
Finished optimization stage 1 on FD1P3DX (CPU Time 0h:00m:00s, Memory Used current: 113MB peak: 113MB)
@N: CG364 :"D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v":160:7:160:13|Synthesizing module FD1S3BX in library work.
Running optimization stage 1 on FD1S3BX .......
Finished optimization stage 1 on FD1S3BX (CPU Time 0h:00m:00s, Memory Used current: 113MB peak: 113MB)
@N: CG364 :"D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v":168:7:168:13|Synthesizing module FD1S3DX in library work.
Running optimization stage 1 on FD1S3DX .......
Finished optimization stage 1 on FD1S3DX (CPU Time 0h:00m:00s, Memory Used current: 113MB peak: 113MB)
@N: CG364 :"D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v":76:7:76:11|Synthesizing module CCU2C in library work.
Running optimization stage 1 on CCU2C .......
Finished optimization stage 1 on CCU2C (CPU Time 0h:00m:00s, Memory Used current: 113MB peak: 113MB)
@N: CG364 :"D:\Project\TLH50_03J_JZ_20250925\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":8:7:8:20|Synthesizing module Asys_fifo56X16 in library work.
Running optimization stage 1 on Asys_fifo56X16 .......
@W: CL168 :"D:\Project\TLH50_03J_JZ_20250925\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":177:8:177:12|Removing instance INV_1 because it does not drive other instances. To preserve this instance, use the syn_noprune synthesis directive.
@W: CL168 :"D:\Project\TLH50_03J_JZ_20250925\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":171:8:171:12|Removing instance INV_4 because it does not drive other instances. To preserve this instance, use the syn_noprune synthesis directive.
@W: CL168 :"D:\Project\TLH50_03J_JZ_20250925\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":169:9:169:15|Removing instance AND2_t0 because it does not drive other instances. To preserve this instance, use the syn_noprune synthesis directive.
Finished optimization stage 1 on Asys_fifo56X16 (CPU Time 0h:00m:00s, Memory Used current: 113MB peak: 114MB)
@N: CG364 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Demodulation.v":42:7:42:18|Synthesizing module Demodulation in library work.

	acum_cnt=32'b00000000000000000000000000101000
	iWID_IN=32'b00000000000000000000000000001100
	iWID_OUT=32'b00000000000000000000000000111000
   Generated name = Demodulation_40s_12s_56s
@N: CG179 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Demodulation.v":101:17:101:27|Removing redundant assignment.
@N: CG179 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Demodulation.v":118:16:118:25|Removing redundant assignment.
@N: CG179 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Demodulation.v":130:46:130:61|Removing redundant assignment.
@N: CG179 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Demodulation.v":159:59:159:72|Removing redundant assignment.
@N: CG179 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Demodulation.v":171:13:171:20|Removing redundant assignment.
@N: CG179 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Demodulation.v":175:12:175:19|Removing redundant assignment.
@N: CG179 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Demodulation.v":176:16:176:27|Removing redundant assignment.
Running optimization stage 1 on Demodulation_40s_12s_56s .......
@W: CL169 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Demodulation.v":158:0:158:5|Pruning unused register sample_sum_DY2[55:0]. Make sure that there are no unused intermediate registers.
@W: CL169 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Demodulation.v":139:0:139:5|Pruning unused register Read_enDY[1:0]. Make sure that there are no unused intermediate registers.
Finished optimization stage 1 on Demodulation_40s_12s_56s (CPU Time 0h:00m:00s, Memory Used current: 114MB peak: 119MB)
@N: CG364 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Integration.v":42:7:42:17|Synthesizing module Integration in library work.

	iWID_IN=32'b00000000000000000000000000111000
	iWID_OUT=32'b00000000000000000000000000111000
   Generated name = Integration_56s_56s
@N: CG179 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Integration.v":71:11:71:17|Removing redundant assignment.
Running optimization stage 1 on Integration_56s_56s .......
Finished optimization stage 1 on Integration_56s_56s (CPU Time 0h:00m:00s, Memory Used current: 114MB peak: 119MB)
@N: CG364 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v":42:7:42:16|Synthesizing module Modulation in library work.

	iWID_TRANS=32'b00000000000000000000000000001101
	iWID_IN=32'b00000000000000000000000000111000
	iWID_OUT=32'b00000000000000000000000000001110
	DA_CONSTANT=32'b00000000000000000010111111011010
	iFEEDBACK_SCALE=32'b00000000000000000000000000001010
	wCLOSED=1'b1
	pi_1_2=14'b00111111111111
	c_step=14'b00110110101100
   Generated name = Modulation_13s_56s_14s_12250s_10s_1_4095_3500
@N: CG179 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v":103:10:103:15|Removing redundant assignment.
@N: CG179 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v":117:9:117:13|Removing redundant assignment.
@N: CG179 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v":131:11:131:17|Removing redundant assignment.
@N: CG179 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v":138:13:138:21|Removing redundant assignment.
@N: CG179 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v":150:11:150:17|Removing redundant assignment.
@W: CG133 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v":64:19:64:22|Object step is declared but not assigned. Either assign a value or remove the declaration.
@W: CG133 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v":67:20:67:28|Object stair_dy1 is declared but not assigned. Either assign a value or remove the declaration.
Running optimization stage 1 on Modulation_13s_56s_14s_12250s_10s_1_4095_3500 .......
@W: CL169 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v":127:0:127:5|Pruning unused register c_stair[13:0]. Make sure that there are no unused intermediate registers.
@W: CL271 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v":142:0:142:5|Pruning unused bits 13 to 0 of DADY_dout[27:0]. If this is not the intended behavior, drive the inputs with valid values, or inputs from the top level.
@W: CL271 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v":134:0:134:5|Pruning unused bits 13 to 0 of dout_mult[27:0]. If this is not the intended behavior, drive the inputs with valid values, or inputs from the top level.
Finished optimization stage 1 on Modulation_13s_56s_14s_12250s_10s_1_4095_3500 (CPU Time 0h:00m:00s, Memory Used current: 114MB peak: 119MB)
@N: CG364 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Rs422Output.v":43:7:43:17|Synthesizing module Rs422Output in library work.

	bPOLAR=32'b00000000000000000000000000000001
	iWID_IN=32'b00000000000000000000000000111000
	iWID_OUT=32'b00000000000000000000000000100000
	iDELAYED=32'b00000000000000000000000001111000
	iOUTPUT_SCALE=32'b00000000000000000000010101000110
	idle_s=4'b0000
	wait_1us_s=4'b0001
	dalay_state=4'b0010
	check_data_stable_s=4'b0011
	transmit_data_s=4'b0100
	clear_data_s=4'b0101
   Generated name = Rs422Output_Z2
@N: CG179 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Rs422Output.v":180:14:180:21|Removing redundant assignment.
@N: CG179 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Rs422Output.v":190:9:190:11|Removing redundant assignment.
@N: CG179 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Rs422Output.v":195:38:195:43|Removing redundant assignment.
@N: CG179 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Rs422Output.v":204:11:204:15|Removing redundant assignment.
@N: CG179 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Rs422Output.v":221:13:221:19|Removing redundant assignment.
Running optimization stage 1 on Rs422Output_Z2 .......
@W: CL169 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Rs422Output.v":194:0:194:5|Pruning unused register sum_dy[55:0]. Make sure that there are no unused intermediate registers.
@W: CL271 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Rs422Output.v":208:0:208:5|Pruning unused bits 79 to 56 of p_sum_dy[79:0]. If this is not the intended behavior, drive the inputs with valid values, or inputs from the top level.
@W: CL271 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Rs422Output.v":208:0:208:5|Pruning unused bits 23 to 0 of p_sum_dy[79:0]. If this is not the intended behavior, drive the inputs with valid values, or inputs from the top level.
@W: CL271 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Rs422Output.v":198:0:198:5|Pruning unused bits 79 to 56 of p_sum[79:0]. If this is not the intended behavior, drive the inputs with valid values, or inputs from the top level.
@W: CL271 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Rs422Output.v":198:0:198:5|Pruning unused bits 23 to 0 of p_sum[79:0]. If this is not the intended behavior, drive the inputs with valid values, or inputs from the top level.
Finished optimization stage 1 on Rs422Output_Z2 (CPU Time 0h:00m:00s, Memory Used current: 114MB peak: 119MB)
@N: CG364 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\SquareWaveGenerator.v":16:7:16:25|Synthesizing module SquareWaveGenerator in library work.

	iTRANSMIT_COFF=32'b00000000000010010010011111000000
   Generated name = SquareWaveGenerator_600000s
@N: CG179 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\SquareWaveGenerator.v":46:11:46:17|Removing redundant assignment.
Running optimization stage 1 on SquareWaveGenerator_600000s .......
Finished optimization stage 1 on SquareWaveGenerator_600000s (CPU Time 0h:00m:00s, Memory Used current: 114MB peak: 119MB)
@N: CG364 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\SignalProcessing.v":41:7:41:22|Synthesizing module SignalProcessing in library work.

	acum_cnt=32'b00000000000000000000000000101000
	iWID_TRANS=32'b00000000000000000000000000001101
	iWID_AD=32'b00000000000000000000000000001100
	iWID_DA=32'b00000000000000000000000000001110
	iWID_RS422=32'b00000000000000000000000000100000
	iWID_SIGN=32'b00000000000000000000000000000101
	wCLOSED=1'b1
	iTRANSIT_TIME=32'b00000000000000000000000010110110
	iAD_VALID_START=32'b00000000000000000000000001000110
	iFEEDBACK_SCALE=32'b00000000000000000000000000001010
	bPOLAR=32'b00000000000000000000000000000001
	iOUTPUT_SCALE=32'b00000000000000000000010101000110
	iDELAYED=32'b00000000000000000000000001111000
	DA_CONSTANT=32'b00000000000000000010111111011010
	iTRANSMIT_COFF=32'b00000000000010010010011111000000
	iWID_PROC=32'b00000000000000000000000000111000
   Generated name = SignalProcessing_Z3
Running optimization stage 1 on SignalProcessing_Z3 .......
Finished optimization stage 1 on SignalProcessing_Z3 (CPU Time 0h:00m:00s, Memory Used current: 114MB peak: 119MB)
@N: CG364 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\INS350_5J_JZ.v":17:7:17:18|Synthesizing module INS350_5J_JZ in library work.
@W: CG360 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\INS350_5J_JZ.v":54:9:54:19|Removing wire TxTransmitt, as there is no assignment to it.
@W: CG360 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\INS350_5J_JZ.v":61:10:61:18|Removing wire CLKFX_OUT, as there is no assignment to it.
Running optimization stage 1 on INS350_5J_JZ .......
Finished optimization stage 1 on INS350_5J_JZ (CPU Time 0h:00m:00s, Memory Used current: 114MB peak: 119MB)
Running optimization stage 2 on INS350_5J_JZ .......
@N: CL159 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\INS350_5J_JZ.v":22:9:22:18|Input RxTransmit is unused.
Finished optimization stage 2 on INS350_5J_JZ (CPU Time 0h:00m:00s, Memory Used current: 113MB peak: 119MB)
Running optimization stage 2 on SignalProcessing_Z3 .......
Finished optimization stage 2 on SignalProcessing_Z3 (CPU Time 0h:00m:00s, Memory Used current: 113MB peak: 119MB)
Running optimization stage 2 on SquareWaveGenerator_600000s .......
Finished optimization stage 2 on SquareWaveGenerator_600000s (CPU Time 0h:00m:00s, Memory Used current: 114MB peak: 119MB)
Running optimization stage 2 on Rs422Output_Z2 .......
@N: CL201 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Rs422Output.v":93:0:93:5|Trying to extract state machine for register trans_state.
Extracted state machine for register trans_state
State machine has 6 reachable states with original encodings of:
   0000
   0001
   0010
   0011
   0100
   0101
@W: CL247 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Rs422Output.v":57:22:57:24|Input port bit 55 of din[55:0] is unused

Finished optimization stage 2 on Rs422Output_Z2 (CPU Time 0h:00m:00s, Memory Used current: 116MB peak: 119MB)
Running optimization stage 2 on Modulation_13s_56s_14s_12250s_10s_1_4095_3500 .......
@N: CL189 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v":96:0:96:5|Register bit square[12] is always 0.
@N: CL189 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v":96:0:96:5|Register bit square[13] is always 0.
@W: CL279 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v":96:0:96:5|Pruning register bits 13 to 12 of square[13:0]. If this is not the intended behavior, drive the inputs with valid values, or inputs from the top level.
@N: CL189 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v":107:0:107:5|Register bit square_dy[13] is always 0.
@N: CL189 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v":107:0:107:5|Register bit square_dy[12] is always 0.
@W: CL279 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v":107:0:107:5|Pruning register bits 13 to 12 of square_dy[13:0]. If this is not the intended behavior, drive the inputs with valid values, or inputs from the top level.
@W: CL279 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v":96:0:96:5|Pruning register bits 11 to 1 of square[11:0]. If this is not the intended behavior, drive the inputs with valid values, or inputs from the top level.
@W: CL279 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v":107:0:107:5|Pruning register bits 11 to 1 of square_dy[11:0]. If this is not the intended behavior, drive the inputs with valid values, or inputs from the top level.
@W: CL246 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v":56:28:56:30|Input port bits 55 to 24 of din[55:0] are unused. Assign logic for all port bits or change the input port size.
@N: CL159 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v":52:12:52:16|Input rst_n is unused.
Finished optimization stage 2 on Modulation_13s_56s_14s_12250s_10s_1_4095_3500 (CPU Time 0h:00m:00s, Memory Used current: 116MB peak: 119MB)
Running optimization stage 2 on Integration_56s_56s .......
@N: CL159 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Integration.v":48:14:48:18|Input rst_n is unused.
Finished optimization stage 2 on Integration_56s_56s (CPU Time 0h:00m:00s, Memory Used current: 116MB peak: 119MB)
Running optimization stage 2 on Demodulation_40s_12s_56s .......
Finished optimization stage 2 on Demodulation_40s_12s_56s (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)
Running optimization stage 2 on Asys_fifo56X16 .......
Finished optimization stage 2 on Asys_fifo56X16 (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)
Running optimization stage 2 on CCU2C .......
Finished optimization stage 2 on CCU2C (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)
Running optimization stage 2 on FD1S3DX .......
Finished optimization stage 2 on FD1S3DX (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)
Running optimization stage 2 on FD1S3BX .......
Finished optimization stage 2 on FD1S3BX (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)
Running optimization stage 2 on FD1P3DX .......
Finished optimization stage 2 on FD1P3DX (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)
Running optimization stage 2 on PDPW16KD .......
Finished optimization stage 2 on PDPW16KD (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)
Running optimization stage 2 on ROM16X1A .......
Finished optimization stage 2 on ROM16X1A (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)
Running optimization stage 2 on XOR2 .......
Finished optimization stage 2 on XOR2 (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)
Running optimization stage 2 on INV .......
Finished optimization stage 2 on INV (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)
Running optimization stage 2 on AND2 .......
Finished optimization stage 2 on AND2 (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)
Running optimization stage 2 on SignalGenerator_13s_182s_70s_3s_166s_169s_173s_176s .......
@A: CL153 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\SignalGenerator.v":56:16:56:27|*Unassigned bits of output_drive are referenced and tied to 0 -- simulation mismatch possible.
Finished optimization stage 2 on SignalGenerator_13s_182s_70s_3s_166s_169s_173s_176s (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)
Running optimization stage 2 on UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1 .......
@N: CL159 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\UART_Control.v":33:11:33:13|Input RXD is unused.
Finished optimization stage 2 on UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1 (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)
Running optimization stage 2 on Ctrl_Data .......
@N: CL201 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Ctrl_Data.v":89:0:89:5|Trying to extract state machine for register tx_state.
Extracted state machine for register tx_state
State machine has 11 reachable states with original encodings of:
   0000
   0001
   0010
   0011
   0100
   0101
   0110
   0111
   1000
   1001
   1010
@N: CL159 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Ctrl_Data.v":22:15:22:21|Input rd_done is unused.
Finished optimization stage 2 on Ctrl_Data (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)
Running optimization stage 2 on uart_tx .......
Finished optimization stage 2 on uart_tx (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)
Running optimization stage 2 on speed_select_Tx_120000000s_115200s_1041s_520s .......
Finished optimization stage 2 on speed_select_Tx_120000000s_115200s_1041s_520s (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)
Running optimization stage 2 on DS18B20 .......
@N: CL201 :"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\DS18B20.v":93:0:93:5|Trying to extract state machine for register cur_state.
Extracted state machine for register cur_state
State machine has 6 reachable states with original encodings of:
   000001
   000010
   000100
   001000
   010000
   100000
Finished optimization stage 2 on DS18B20 (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)
Running optimization stage 2 on global_clock .......
Finished optimization stage 2 on global_clock (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)
Running optimization stage 2 on EHXPLLL .......
Finished optimization stage 2 on EHXPLLL (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)
Running optimization stage 2 on VLO .......
Finished optimization stage 2 on VLO (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)
Running optimization stage 2 on VHI .......
Finished optimization stage 2 on VHI (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)

For a summary of runtime and memory usage per design unit, please see file:
==========================================================
@L: D:\Project\TLH50_03J_JZ_20250925\impl1\synwork\layer0.rt.csv


At c_ver Exit (Real Time elapsed 0h:00m:04s; CPU Time elapsed 0h:00m:04s; Memory used current: 117MB peak: 120MB)

Process took 0h:00m:04s realtime, 0h:00m:04s cputime

Process completed successfully.
# Thu Sep 25 10:29:22 2025

###########################################################]
###########################################################[

Copyright (C) 1994-2021 Synopsys, Inc.
This Synopsys software and all associated documentation are proprietary to Synopsys, Inc.
and may only be used pursuant to the terms and conditions of a written license agreement
with Synopsys, Inc. All other use, reproduction, modification, or distribution of the
Synopsys software or the associated documentation is strictly prohibited.
Tool: Synplify Pro (R)
Build: R-2021.03L-SP1-1
Install: D:\Software\lscc\diamond\3.12\synpbase
OS: Windows 6.2

Hostname: TLH-022

Implementation : impl1
Synopsys Synopsys Netlist Linker, Version comp202103synp2, Build 222R, Built Dec 22 2021 00:18:12, @

@N|Running in 64-bit mode

At syn_nfilter Exit (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 106MB peak: 107MB)

Process took 0h:00m:01s realtime, 0h:00m:01s cputime

Process completed successfully.
# Thu Sep 25 10:29:23 2025

###########################################################]

For a summary of runtime and memory usage for all design units, please see file:
==========================================================
@L: D:\Project\TLH50_03J_JZ_20250925\impl1\synwork\INS350_5J_JZ_impl1_comp.rt.csv

@END

At c_hdl Exit (Real Time elapsed 0h:00m:04s; CPU Time elapsed 0h:00m:04s; Memory used current: 38MB peak: 47MB)

Process took 0h:00m:04s realtime, 0h:00m:04s cputime

Process completed successfully.
# Thu Sep 25 10:29:23 2025

###########################################################]
###########################################################[

Copyright (C) 1994-2021 Synopsys, Inc.
This Synopsys software and all associated documentation are proprietary to Synopsys, Inc.
and may only be used pursuant to the terms and conditions of a written license agreement
with Synopsys, Inc. All other use, reproduction, modification, or distribution of the
Synopsys software or the associated documentation is strictly prohibited.
Tool: Synplify Pro (R)
Build: R-2021.03L-SP1-1
Install: D:\Software\lscc\diamond\3.12\synpbase
OS: Windows 6.2

Hostname: TLH-022

Implementation : impl1
Synopsys Synopsys Netlist Linker, Version comp202103synp2, Build 222R, Built Dec 22 2021 00:18:12, @

@N|Running in 64-bit mode
File D:\Project\TLH50_03J_JZ\impl1\synwork\INS350_5J_JZ_impl1_comp.srs changed - recompiling

At syn_nfilter Exit (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 107MB peak: 107MB)

Process took 0h:00m:01s realtime, 0h:00m:01s cputime

Process completed successfully.
# Thu Sep 25 10:29:24 2025

###########################################################]
# Thu Sep 25 10:29:25 2025


Copyright (C) 1994-2021 Synopsys, Inc.
This Synopsys software and all associated documentation are proprietary to Synopsys, Inc.
and may only be used pursuant to the terms and conditions of a written license agreement
with Synopsys, Inc. All other use, reproduction, modification, or distribution of the
Synopsys software or the associated documentation is strictly prohibited.
Tool: Synplify Pro (R)
Build: R-2021.03L-SP1-1
Install: D:\Software\lscc\diamond\3.12\synpbase
OS: Windows 6.2

Hostname: TLH-022

Implementation : impl1
Synopsys Lattice Technology Pre-mapping, Version map202103lat, Build 107R, Built Dec 22 2021 00:40:26, @


Mapper Startup Complete (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 132MB peak: 132MB)


Done reading skeleton netlist (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 137MB peak: 144MB)

@A: MF827 |No constraint file specified.
@L: D:\Project\TLH50_03J_JZ_20250925\impl1\INS350_5J_JZ_impl1_scck.rpt 
See clock summary report "D:\Project\TLH50_03J_JZ_20250925\impl1\INS350_5J_JZ_impl1_scck.rpt"
@N: MF916 |Option synthesis_strategy=base is enabled. 
@N: MF248 |Running in 64-bit mode.
@N: MF666 |Clock conversion enabled. (Command "set_option -fix_gated_and_generated_clocks 1" in the project file.)

Design Input Complete (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 143MB peak: 144MB)


Mapper Initialization Complete (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 143MB peak: 144MB)


Start loading timing files (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 155MB peak: 155MB)


Finished loading timing files (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 155MB peak: 157MB)

@W: BN132 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\uart_tx.v":33:0:33:5|Removing sequential instance u_uart.U1.bps_start_r because it is equivalent to instance u_uart.U1.tx_en. To keep the instance, apply constraint syn_preserve=1 on the instance.
@N: MO111 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\uart_control.v":31:16:31:22|Tristate driver rx_data_1 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) on net rx_data_1 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) has its enable tied to GND.
@N: MO111 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\uart_control.v":31:16:31:22|Tristate driver rx_data_2 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) on net rx_data_2 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) has its enable tied to GND.
@N: MO111 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\uart_control.v":31:16:31:22|Tristate driver rx_data_3 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) on net rx_data_3 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) has its enable tied to GND.
@N: MO111 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\uart_control.v":31:16:31:22|Tristate driver rx_data_4 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) on net rx_data_4 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) has its enable tied to GND.
@N: MO111 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\uart_control.v":31:16:31:22|Tristate driver rx_data_5 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) on net rx_data_5 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) has its enable tied to GND.
@N: MO111 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\uart_control.v":31:16:31:22|Tristate driver rx_data_6 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) on net rx_data_6 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) has its enable tied to GND.
@N: MO111 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\uart_control.v":31:16:31:22|Tristate driver rx_data_7 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) on net rx_data_7 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) has its enable tied to GND.
@N: MO111 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\uart_control.v":31:16:31:22|Tristate driver rx_data_8 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) on net rx_data_8 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) has its enable tied to GND.
@N: BN362 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\ds18b20.v":157:0:157:5|Removing sequential instance rd_done (in view: work.DS18B20(verilog)) of type view:PrimLib.dffre(prim) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":182:13:182:18|Removing instance LUT4_1 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.ROM16X1A(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":186:13:186:18|Removing instance LUT4_0 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.ROM16X1A(PRIM) because it does not drive other instances.
@N: BN362 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":350:12:350:15|Removing sequential instance FF_1 (in view: work.Asys_fifo56X16(verilog)) of type view:LUCENT.FD1S3BX(PRIM) because it does not drive other instances.
@N: BN362 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":353:12:353:15|Removing sequential instance FF_0 (in view: work.Asys_fifo56X16(verilog)) of type view:LUCENT.FD1S3DX(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":576:10:576:24|Removing instance ae_set_cmp_ci_a (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":584:10:584:21|Removing instance ae_set_cmp_0 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":592:10:592:21|Removing instance ae_set_cmp_1 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":600:10:600:21|Removing instance ae_set_cmp_2 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":608:10:608:21|Removing instance ae_set_cmp_3 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":616:10:616:11|Removing instance a2 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":624:10:624:24|Removing instance ae_clr_cmp_ci_a (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":632:10:632:21|Removing instance ae_clr_cmp_0 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":640:10:640:21|Removing instance ae_clr_cmp_1 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":648:10:648:21|Removing instance ae_clr_cmp_2 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":656:10:656:21|Removing instance ae_clr_cmp_3 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":664:10:664:11|Removing instance a3 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":672:10:672:24|Removing instance af_set_cmp_ci_a (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":680:10:680:21|Removing instance af_set_cmp_0 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":688:10:688:21|Removing instance af_set_cmp_1 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":696:10:696:21|Removing instance af_set_cmp_2 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":704:10:704:21|Removing instance af_set_cmp_3 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":712:10:712:11|Removing instance a4 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":720:10:720:24|Removing instance af_clr_cmp_ci_a (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":728:10:728:21|Removing instance af_clr_cmp_0 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":736:10:736:21|Removing instance af_clr_cmp_1 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":744:10:744:21|Removing instance af_clr_cmp_2 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":752:10:752:21|Removing instance af_clr_cmp_3 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":764:10:764:11|Removing instance a5 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
Encoding state machine cur_state[5:0] (in view: work.DS18B20(verilog))
original code -> new code
   000001 -> 000001
   000010 -> 000010
   000100 -> 000100
   001000 -> 001000
   010000 -> 010000
   100000 -> 100000
Encoding state machine tx_state[10:0] (in view: work.Ctrl_Data(verilog))
original code -> new code
   0000 -> 00000000001
   0001 -> 00000000010
   0010 -> 00000000100
   0011 -> 00000001000
   0100 -> 00000010000
   0101 -> 00000100000
   0110 -> 00001000000
   0111 -> 00010000000
   1000 -> 00100000000
   1001 -> 01000000000
   1010 -> 10000000000
Encoding state machine trans_state[5:0] (in view: work.Rs422Output_Z2(verilog))
original code -> new code
   0000 -> 000001
   0001 -> 000010
   0010 -> 000100
   0011 -> 001000
   0100 -> 010000
   0101 -> 100000

Starting clock optimization phase (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 191MB peak: 191MB)

@N: MF578 |Incompatible asynchronous control logic preventing generated clock conversion.

Finished clock optimization phase (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 191MB peak: 192MB)


Starting clock optimization report phase (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 191MB peak: 192MB)


Finished clock optimization report phase (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 191MB peak: 192MB)

@N: FX1184 |Applying syn_allowed_resources blockrams=56 on top level netlist INS350_5J_JZ 

Finished netlist restructuring (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 191MB peak: 192MB)



Clock Summary
******************

          Start                                 Requested     Requested     Clock                                                Clock                   Clock
Level     Clock                                 Frequency     Period        Type                                                 Group                   Load 
--------------------------------------------------------------------------------------------------------------------------------------------------------------
0 <USER>       <GROUP>                                200.0 MHz     5.000         system                                               system_clkgroup         0    
                                                                                                                                                              
0 -       global_clock|CLKOP_inferred_clock     200.0 MHz     5.000         inferred                                             Inferred_clkgroup_0     855  
1 .         DS18B20|clk_us_derived_clock        200.0 MHz     5.000         derived (from global_clock|CLKOP_inferred_clock)     Inferred_clkgroup_0     64   
                                                                                                                                                              
0 -       global_clock|CLKOS_inferred_clock     200.0 MHz     5.000         inferred                                             Inferred_clkgroup_1     178  
==============================================================================================================================================================



Clock Load Summary
***********************

                                      Clock     Source                              Clock Pin                                  Non-clock Pin     Non-clock Pin
Clock                                 Load      Pin                                 Seq Example                                Seq Example       Comb Example 
--------------------------------------------------------------------------------------------------------------------------------------------------------------
System                                0         -                                   -                                          -                 -            
                                                                                                                                                              
global_clock|CLKOP_inferred_clock     855       CLK120.PLLInst_0.CLKOP(EHXPLLL)     signal_process.trans.clk_out.C             -                 -            
DS18B20|clk_us_derived_clock          64        wendu.clk_us.Q[0](dffre)            wendu.data_temp[15:0].C                    -                 -            
                                                                                                                                                              
global_clock|CLKOS_inferred_clock     178       CLK120.PLLInst_0.CLKOS(EHXPLLL)     signal_process.demodu.din_reg0[11:0].C     -                 -            
==============================================================================================================================================================

@W: MT529 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\ds18b20.v":69:0:69:5|Found inferred clock global_clock|CLKOP_inferred_clock which controls 855 sequential elements including wendu.cnt[7:0]. This clock has no specified timing constraint which may prevent conversion of gated or generated clocks and may adversely impact design performance. 
@W: MT529 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":198:13:198:25|Found inferred clock global_clock|CLKOS_inferred_clock which controls 178 sequential elements including signal_process.demodu.fifo.pdp_ram_0_0_1. This clock has no specified timing constraint which may prevent conversion of gated or generated clocks and may adversely impact design performance. 

ICG Latch Removal Summary:
Number of ICG latches removed: 0
Number of ICG latches not removed:	0
For details review file gcc_ICG_report.rpt


@S |Clock Optimization Summary



#### START OF PREMAP CLOCK OPTIMIZATION REPORT #####[

0 non-gated/non-generated clock tree(s) driving 0 clock pin(s) of sequential element(s)
3 gated/generated clock tree(s) driving 1097 clock pin(s) of sequential element(s)
0 instances converted, 1097 sequential instances remain driven by gated/generated clocks

============================================================================ Gated/Generated Clocks ============================================================================
Clock Tree ID     Driving Element            Drive Element Type     Unconverted Fanout     Sample Instance                            Explanation                               
--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
@KP:ckid0_1       CLK120.PLLInst_0.CLKOP     EHXPLLL                855                    wendu.cnt[7:0]                             Clock source is invalid for GCC           
@KP:ckid0_4       CLK120.PLLInst_0.CLKOS     EHXPLLL                178                    signal_process.demodu.AD_validcnt[7:0]     Clock source is invalid for GCC           
@KP:ckid0_6       wendu.clk_us.Q[0]          dffre                  64                     wendu.cur_state[5]                         Derived clock on input (not legal for GCC)
================================================================================================================================================================================


##### END OF CLOCK OPTIMIZATION REPORT ######

@N: FX1143 |Skipping assigning INTERNAL_VREF to iobanks, because the table of mapping from pin to iobank is not initialized.
Finished Pre Mapping Phase.

Starting constraint checker (Real Time elapsed 0h:00m:01s; CPU Time elapsed 0h:00m:01s; Memory used current: 193MB peak: 193MB)


Finished constraint checker preprocessing (Real Time elapsed 0h:00m:01s; CPU Time elapsed 0h:00m:01s; Memory used current: 193MB peak: 193MB)


Finished constraint checker (Real Time elapsed 0h:00m:01s; CPU Time elapsed 0h:00m:01s; Memory used current: 194MB peak: 194MB)

Pre-mapping successful!

At Mapper Exit (Real Time elapsed 0h:00m:01s; CPU Time elapsed 0h:00m:01s; Memory used current: 112MB peak: 195MB)

Process took 0h:00m:01s realtime, 0h:00m:01s cputime
# Thu Sep 25 10:29:26 2025

###########################################################]
# Thu Sep 25 10:29:26 2025


Copyright (C) 1994-2021 Synopsys, Inc.
This Synopsys software and all associated documentation are proprietary to Synopsys, Inc.
and may only be used pursuant to the terms and conditions of a written license agreement
with Synopsys, Inc. All other use, reproduction, modification, or distribution of the
Synopsys software or the associated documentation is strictly prohibited.
Tool: Synplify Pro (R)
Build: R-2021.03L-SP1-1
Install: D:\Software\lscc\diamond\3.12\synpbase
OS: Windows 6.2

Hostname: TLH-022

Implementation : impl1
Synopsys Lattice Technology Mapper, Version map202103lat, Build 107R, Built Dec 22 2021 00:40:26, @


Mapper Startup Complete (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 132MB peak: 132MB)

@N: MF916 |Option synthesis_strategy=base is enabled. 
@N: MF248 |Running in 64-bit mode.
@N: MF666 |Clock conversion enabled. (Command "set_option -fix_gated_and_generated_clocks 1" in the project file.)

Design Input Complete (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 134MB peak: 144MB)


Mapper Initialization Complete (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 134MB peak: 144MB)


Start loading timing files (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 148MB peak: 148MB)


Finished loading timing files (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 150MB peak: 151MB)



Starting Optimization and Mapping (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 185MB peak: 185MB)

@W: FA239 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\ds18b20.v":269:11:269:33|ROM RD_CMD_DATA_pmux (in view: work.DS18B20(verilog)) mapped in logic. To map to a technology ROM, apply attribute syn_romstyle on this instance.
@N: MO106 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\ds18b20.v":269:11:269:33|Found ROM RD_CMD_DATA_pmux (in view: work.DS18B20(verilog)) with 16 words by 1 bit.
@W: FA239 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\ds18b20.v":211:11:211:33|ROM WR_CMD_DATA_pmux (in view: work.DS18B20(verilog)) mapped in logic. To map to a technology ROM, apply attribute syn_romstyle on this instance.
@N: MO106 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\ds18b20.v":211:11:211:33|Found ROM WR_CMD_DATA_pmux (in view: work.DS18B20(verilog)) with 16 words by 1 bit.

Finished RTL optimizations (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 190MB peak: 190MB)

@N: MO231 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\ds18b20.v":157:0:157:5|Found counter in view:work.DS18B20(verilog) instance bit_cnt[3:0] 
@N: MO231 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\speed_select_tx.v":37:0:37:5|Found counter in view:work.speed_select_Tx_120000000s_115200s_1041s_520s(verilog) instance cnt[12:0] 
@N: MO231 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\rs422output.v":150:0:150:5|Found counter in view:work.SignalProcessing_Z3(verilog) instance rs422.count_pos[7:0] 
@N: BN362 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\integration.v":76:0:76:5|Removing sequential instance integ.dout[55] (in view: work.SignalProcessing_Z3(verilog)) of type view:PrimLib.dff(prim) because it does not drive other instances.
@W: BN132 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\rs422output.v":88:0:88:5|Removing instance signal_process.rs422.output_dy[0] because it is equivalent to instance signal_process.modu.mudu_dy[0]. To keep the instance, apply constraint syn_preserve=1 on the instance.
@W: BN132 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\rs422output.v":88:0:88:5|Removing instance signal_process.rs422.output_dy[1] because it is equivalent to instance signal_process.modu.mudu_dy[1]. To keep the instance, apply constraint syn_preserve=1 on the instance.

Starting factoring (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 194MB peak: 194MB)


Finished factoring (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 195MB peak: 195MB)


Available hyper_sources - for debug and ip models
	None Found


Finished generic timing optimizations - Pass 1 (Real Time elapsed 0h:00m:01s; CPU Time elapsed 0h:00m:01s; Memory used current: 200MB peak: 200MB)

@N: BN362 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\integration.v":66:0:66:5|Removing sequential instance signal_process.integ.DA_dout[55] (in view: work.INS350_5J_JZ(verilog)) because it does not drive other instances.
@N: BN362 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":181:0:181:5|Removing sequential instance signal_process.demodu.dout[55] (in view: work.INS350_5J_JZ(verilog)) because it does not drive other instances.
@N: BN362 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":163:0:163:5|Removing sequential instance signal_process.demodu.median_sum_n[55] (in view: work.INS350_5J_JZ(verilog)) because it does not drive other instances.
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":163:0:163:5|Boundary register signal_process.demodu.median_sum_n[55] (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@N: BN362 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":163:0:163:5|Removing sequential instance signal_process.demodu.INS_dout[55] (in view: work.INS350_5J_JZ(verilog)) because it does not drive other instances.
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":163:0:163:5|Boundary register signal_process.demodu.INS_dout[55] (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 

Starting Early Timing Optimization (Real Time elapsed 0h:00m:03s; CPU Time elapsed 0h:00m:03s; Memory used current: 205MB peak: 205MB)


Finished Early Timing Optimization (Real Time elapsed 0h:00m:03s; CPU Time elapsed 0h:00m:03s; Memory used current: 205MB peak: 206MB)


Finished generic timing optimizations - Pass 2 (Real Time elapsed 0h:00m:03s; CPU Time elapsed 0h:00m:03s; Memory used current: 205MB peak: 206MB)


Finished preparing to map (Real Time elapsed 0h:00m:03s; CPU Time elapsed 0h:00m:03s; Memory used current: 206MB peak: 206MB)


Finished technology mapping (Real Time elapsed 0h:00m:03s; CPU Time elapsed 0h:00m:03s; Memory used current: 210MB peak: 210MB)

Pass		 CPU time		Worst Slack		Luts / Registers
------------------------------------------------------------
   1		0h:00m:03s		     0.80ns		 285 /      1018

Finished technology timing optimizations and critical path resynthesis (Real Time elapsed 0h:00m:04s; CPU Time elapsed 0h:00m:04s; Memory used current: 211MB peak: 211MB)

@N: FX164 |The option to pack registers in the IOB has not been specified. Please set syn_useioff attribute.  
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_55_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_54_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_53_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_52_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_51_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_50_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_49_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_48_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_47_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_46_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_45_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_44_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_43_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_42_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_41_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_40_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_39_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_38_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_37_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_36_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_35_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_34_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_33_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_32_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_31_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_30_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_29_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_28_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_27_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_26_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_25_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_24_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_23_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_22_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_21_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_20_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_19_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_18_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_17_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_16_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_15_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_14_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_13_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_12_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_11_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_10_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_9_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_8_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_7_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_6_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_5_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_4_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_3_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_2_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_1_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_0_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 

Finished restoring hierarchy (Real Time elapsed 0h:00m:04s; CPU Time elapsed 0h:00m:04s; Memory used current: 211MB peak: 211MB)


Start Writing Netlists (Real Time elapsed 0h:00m:04s; CPU Time elapsed 0h:00m:04s; Memory used current: 172MB peak: 212MB)

Writing Analyst data base D:\Project\TLH50_03J_JZ_20250925\impl1\synwork\INS350_5J_JZ_impl1_m.srm

Finished Writing Netlist Databases (Real Time elapsed 0h:00m:07s; CPU Time elapsed 0h:00m:06s; Memory used current: 214MB peak: 214MB)

Writing EDIF Netlist and constraint files
@N: FX1056 |Writing EDF file: D:\Project\TLH50_03J_JZ_20250925\impl1\INS350_5J_JZ_impl1.edi
@N: BW106 |Synplicity Constraint File capacitance units using default value of 1pF 

Finished Writing EDIF Netlist and constraint files (Real Time elapsed 0h:00m:07s; CPU Time elapsed 0h:00m:07s; Memory used current: 219MB peak: 219MB)


Finished Writing Netlists (Real Time elapsed 0h:00m:07s; CPU Time elapsed 0h:00m:07s; Memory used current: 219MB peak: 220MB)


Start final timing analysis (Real Time elapsed 0h:00m:07s; CPU Time elapsed 0h:00m:07s; Memory used current: 212MB peak: 220MB)

@W: MT246 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\global_clock\global_clock.v":59:12:59:20|Blackbox EHXPLLL is missing a user supplied timing model. This may have a negative effect on timing analysis and optimizations (Quality of Results)
@W: MT420 |Found inferred clock global_clock|CLKOP_inferred_clock with period 5.00ns. Please declare a user-defined clock on net CLK120.clk120mhz.
@W: MT420 |Found inferred clock global_clock|CLKOS_inferred_clock with period 5.00ns. Please declare a user-defined clock on net CLK120.clk_AD.
@N: MT615 |Found clock DS18B20|clk_us_derived_clock with period 5.00ns 


##### START OF TIMING REPORT #####[
# Timing report written on Thu Sep 25 10:29:34 2025
#


Top view:               INS350_5J_JZ
Requested Frequency:    200.0 MHz
Wire load mode:         top
Paths requested:        5
Constraint File(s):    
@N: MT320 |This timing report is an estimate of place and route data. For final timing results, use the FPGA vendor place and route report.

@N: MT322 |Clock constraints include only register-to-register paths associated with each individual clock.



Performance Summary
*******************


Worst slack in design: 0.058

                                      Requested     Estimated      Requested     Estimated               Clock                                                Clock              
Starting Clock                        Frequency     Frequency      Period        Period        Slack     Type                                                 Group              
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
DS18B20|clk_us_derived_clock          200.0 MHz     238.7 MHz      5.000         4.189         1.621     derived (from global_clock|CLKOP_inferred_clock)     Inferred_clkgroup_0
global_clock|CLKOP_inferred_clock     200.0 MHz     202.3 MHz      5.000         4.942         0.058     inferred                                             Inferred_clkgroup_0
global_clock|CLKOS_inferred_clock     200.0 MHz     233.5 MHz      5.000         4.284         0.717     inferred                                             Inferred_clkgroup_1
System                                200.0 MHz     2347.4 MHz     5.000         0.426         4.574     system                                               system_clkgroup    
=================================================================================================================================================================================





Clock Relationships
*******************

Clocks                                                                |    rise  to  rise   |    fall  to  fall   |    rise  to  fall   |    fall  to  rise 
------------------------------------------------------------------------------------------------------------------------------------------------------------
Starting                           Ending                             |  constraint  slack  |  constraint  slack  |  constraint  slack  |  constraint  slack
------------------------------------------------------------------------------------------------------------------------------------------------------------
System                             System                             |  5.000       4.574  |  No paths    -      |  No paths    -      |  No paths    -    
System                             global_clock|CLKOP_inferred_clock  |  5.000       2.861  |  No paths    -      |  No paths    -      |  No paths    -    
System                             global_clock|CLKOS_inferred_clock  |  5.000       2.317  |  No paths    -      |  No paths    -      |  No paths    -    
global_clock|CLKOP_inferred_clock  System                             |  5.000       0.856  |  No paths    -      |  No paths    -      |  No paths    -    
global_clock|CLKOP_inferred_clock  global_clock|CLKOP_inferred_clock  |  5.000       0.058  |  No paths    -      |  No paths    -      |  No paths    -    
global_clock|CLKOP_inferred_clock  global_clock|CLKOS_inferred_clock  |  Diff grp    -      |  No paths    -      |  No paths    -      |  No paths    -    
global_clock|CLKOS_inferred_clock  System                             |  5.000       3.619  |  No paths    -      |  No paths    -      |  No paths    -    
global_clock|CLKOS_inferred_clock  global_clock|CLKOP_inferred_clock  |  Diff grp    -      |  No paths    -      |  No paths    -      |  No paths    -    
global_clock|CLKOS_inferred_clock  global_clock|CLKOS_inferred_clock  |  5.000       0.717  |  No paths    -      |  No paths    -      |  No paths    -    
DS18B20|clk_us_derived_clock       global_clock|CLKOP_inferred_clock  |  5.000       4.093  |  No paths    -      |  No paths    -      |  No paths    -    
DS18B20|clk_us_derived_clock       DS18B20|clk_us_derived_clock       |  5.000       1.622  |  No paths    -      |  No paths    -      |  No paths    -    
============================================================================================================================================================
 Note: 'No paths' indicates there are no paths in the design for that pair of clock edges.
       'Diff grp' indicates that paths exist but the starting clock and ending clock are in different clock groups.



Interface Information 
*********************

No IO constraint found



====================================
Detailed Report for Clock: DS18B20|clk_us_derived_clock
====================================



Starting Points with Worst Slack
********************************

                     Starting                                                            Arrival          
Instance             Reference                        Type        Pin     Net            Time        Slack
                     Clock                                                                                
----------------------------------------------------------------------------------------------------------
wendu.cnt_us[10]     DS18B20|clk_us_derived_clock     FD1S3DX     Q       cnt_us[10]     0.955       1.621
wendu.cnt_us[13]     DS18B20|clk_us_derived_clock     FD1S3DX     Q       cnt_us[13]     0.955       1.621
wendu.cnt_us[16]     DS18B20|clk_us_derived_clock     FD1S3DX     Q       cnt_us[16]     0.955       1.621
wendu.cnt_us[19]     DS18B20|clk_us_derived_clock     FD1S3DX     Q       cnt_us[19]     0.955       1.621
wendu.cnt_us[14]     DS18B20|clk_us_derived_clock     FD1S3DX     Q       cnt_us[14]     0.955       2.228
wendu.cnt_us[15]     DS18B20|clk_us_derived_clock     FD1S3DX     Q       cnt_us[15]     0.955       2.228
wendu.cnt_us[17]     DS18B20|clk_us_derived_clock     FD1S3DX     Q       cnt_us[17]     0.955       2.228
wendu.cnt_us[0]      DS18B20|clk_us_derived_clock     FD1S3DX     Q       cnt_us[0]      0.955       2.797
wendu.cnt_us[11]     DS18B20|clk_us_derived_clock     FD1S3DX     Q       cnt_us[11]     0.955       2.797
wendu.cnt_us[12]     DS18B20|clk_us_derived_clock     FD1S3DX     Q       cnt_us[12]     0.955       2.797
==========================================================================================================


Ending Points with Worst Slack
******************************

                     Starting                                                               Required          
Instance             Reference                        Type        Pin     Net               Time         Slack
                     Clock                                                                                    
--------------------------------------------------------------------------------------------------------------
wendu.cnt_us[19]     DS18B20|clk_us_derived_clock     FD1S3DX     D       cnt_us_12[19]     9.946        1.621
wendu.cnt_us[17]     DS18B20|clk_us_derived_clock     FD1S3DX     D       cnt_us_12[17]     9.946        1.683
wendu.cnt_us[15]     DS18B20|clk_us_derived_clock     FD1S3DX     D       cnt_us_12[15]     9.946        1.744
wendu.cnt_us[16]     DS18B20|clk_us_derived_clock     FD1S3DX     D       cnt_us_12[16]     9.946        1.744
wendu.cnt_us[13]     DS18B20|clk_us_derived_clock     FD1S3DX     D       cnt_us_12[13]     9.946        1.804
wendu.cnt_us[14]     DS18B20|clk_us_derived_clock     FD1S3DX     D       cnt_us_12[14]     9.946        1.804
wendu.cnt_us[9]      DS18B20|clk_us_derived_clock     FD1S3DX     D       cnt_us_12[9]      9.946        1.927
wendu.cnt_us[10]     DS18B20|clk_us_derived_clock     FD1S3DX     D       cnt_us_12[10]     9.946        1.927
wendu.cnt_us[7]      DS18B20|clk_us_derived_clock     FD1S3DX     D       cnt_us_12[7]      9.946        1.988
wendu.cnt_us[8]      DS18B20|clk_us_derived_clock     FD1S3DX     D       cnt_us_12[8]      9.946        1.988
==============================================================================================================



Worst Path Information
***********************


Path information for path number 1: 
      Requested Period:                      10.000
    - Setup time:                            0.054
    + Clock delay at ending point:           0.000 (ideal)
    = Required time:                         9.946

    - Propagation time:                      8.325
    - Clock delay at starting point:         0.000 (ideal)
    = Slack (non-critical) :                 1.621

    Number of logic level(s):                19
    Starting point:                          wendu.cnt_us[10] / Q
    Ending point:                            wendu.cnt_us[19] / D
    The start point is clocked by            DS18B20|clk_us_derived_clock [rising] (rise=0.000 fall=2.500 period=5.000) on pin CK
    The end   point is clocked by            DS18B20|clk_us_derived_clock [rising] (rise=0.000 fall=2.500 period=5.000) on pin CK
    -Timing constraint applied as multi cycle path with factor 2 (from c:DS18B20|clk_us_derived_clock to c:DS18B20|clk_us_derived_clock)

Instance / Net                                           Pin      Pin               Arrival     No. of    
Name                                        Type         Name     Dir     Delay     Time        Fan Out(s)
----------------------------------------------------------------------------------------------------------
wendu.cnt_us[10]                            FD1S3DX      Q        Out     0.955     0.955 r     -         
cnt_us[10]                                  Net          -        -       -         -           3         
wendu.un1_cur_state_3_i_a2_3_o3_5_4         ORCALUT4     A        In      0.000     0.955 r     -         
wendu.un1_cur_state_3_i_a2_3_o3_5_4         ORCALUT4     Z        Out     0.606     1.561 r     -         
un1_cur_state_3_i_a2_3_o3_5_4               Net          -        -       -         -           1         
wendu.un1_cur_state_3_i_a2_3_o3_5           ORCALUT4     D        In      0.000     1.561 r     -         
wendu.un1_cur_state_3_i_a2_3_o3_5           ORCALUT4     Z        Out     0.708     2.269 r     -         
un1_cur_state_3_i_a2_3_o3_5                 Net          -        -       -         -           3         
wendu.un1_cur_state_3_i_a2_3_o3_0           ORCALUT4     A        In      0.000     2.269 r     -         
wendu.un1_cur_state_3_i_a2_3_o3_0           ORCALUT4     Z        Out     0.660     2.929 r     -         
un1_cur_state_3_i_a2_3_o3_0                 Net          -        -       -         -           2         
wendu.data_temp_1_sqmuxa_0_o3_0             ORCALUT4     B        In      0.000     2.929 r     -         
wendu.data_temp_1_sqmuxa_0_o3_0             ORCALUT4     Z        Out     0.708     3.637 r     -         
data_temp_1_sqmuxa_0_o3_0                   Net          -        -       -         -           3         
wendu.data_temp_1_sqmuxa_0_o3_0_RNIT8UQ     ORCALUT4     A        In      0.000     3.637 r     -         
wendu.data_temp_1_sqmuxa_0_o3_0_RNIT8UQ     ORCALUT4     Z        Out     0.708     4.345 r     -         
data_temp_1_sqmuxa_0_o3_0_RNIT8UQ           Net          -        -       -         -           3         
wendu.cur_state_RNIG5RS[5]                  ORCALUT4     B        In      0.000     4.345 r     -         
wendu.cur_state_RNIG5RS[5]                  ORCALUT4     Z        Out     0.837     5.182 f     -         
N_93_i                                      Net          -        -       -         -           20        
wendu.un1_cnt_us_18_cry_0_0_RNO             ORCALUT4     A        In      0.000     5.182 f     -         
wendu.un1_cnt_us_18_cry_0_0_RNO             ORCALUT4     Z        Out     0.606     5.788 r     -         
un1_cnt_us_0_sqmuxa_2_i                     Net          -        -       -         -           1         
wendu.un1_cnt_us_18_cry_0_0                 CCU2C        B0       In      0.000     5.788 r     -         
wendu.un1_cnt_us_18_cry_0_0                 CCU2C        COUT     Out     0.900     6.688 r     -         
un1_cnt_us_18_cry_0                         Net          -        -       -         -           1         
wendu.un1_cnt_us_18_cry_1_0                 CCU2C        CIN      In      0.000     6.688 r     -         
wendu.un1_cnt_us_18_cry_1_0                 CCU2C        COUT     Out     0.061     6.749 r     -         
un1_cnt_us_18_cry_2                         Net          -        -       -         -           1         
wendu.un1_cnt_us_18_cry_3_0                 CCU2C        CIN      In      0.000     6.749 r     -         
wendu.un1_cnt_us_18_cry_3_0                 CCU2C        COUT     Out     0.061     6.810 r     -         
un1_cnt_us_18_cry_4                         Net          -        -       -         -           1         
wendu.un1_cnt_us_18_cry_5_0                 CCU2C        CIN      In      0.000     6.810 r     -         
wendu.un1_cnt_us_18_cry_5_0                 CCU2C        COUT     Out     0.061     6.871 r     -         
un1_cnt_us_18_cry_6                         Net          -        -       -         -           1         
wendu.un1_cnt_us_18_cry_7_0                 CCU2C        CIN      In      0.000     6.871 r     -         
wendu.un1_cnt_us_18_cry_7_0                 CCU2C        COUT     Out     0.061     6.932 r     -         
un1_cnt_us_18_cry_8                         Net          -        -       -         -           1         
wendu.un1_cnt_us_18_cry_9_0                 CCU2C        CIN      In      0.000     6.932 r     -         
wendu.un1_cnt_us_18_cry_9_0                 CCU2C        COUT     Out     0.061     6.993 r     -         
un1_cnt_us_18_cry_10                        Net          -        -       -         -           1         
wendu.un1_cnt_us_18_cry_11_0                CCU2C        CIN      In      0.000     6.993 r     -         
wendu.un1_cnt_us_18_cry_11_0                CCU2C        COUT     Out     0.061     7.054 r     -         
un1_cnt_us_18_cry_12                        Net          -        -       -         -           1         
wendu.un1_cnt_us_18_cry_13_0                CCU2C        CIN      In      0.000     7.054 r     -         
wendu.un1_cnt_us_18_cry_13_0                CCU2C        COUT     Out     0.061     7.115 r     -         
un1_cnt_us_18_cry_14                        Net          -        -       -         -           1         
wendu.un1_cnt_us_18_cry_15_0                CCU2C        CIN      In      0.000     7.115 r     -         
wendu.un1_cnt_us_18_cry_15_0                CCU2C        COUT     Out     0.061     7.176 r     -         
un1_cnt_us_18_cry_16                        Net          -        -       -         -           1         
wendu.un1_cnt_us_18_cry_17_0                CCU2C        CIN      In      0.000     7.176 r     -         
wendu.un1_cnt_us_18_cry_17_0                CCU2C        COUT     Out     0.061     7.237 r     -         
un1_cnt_us_18_cry_18                        Net          -        -       -         -           1         
wendu.un1_cnt_us_18_s_19_0                  CCU2C        CIN      In      0.000     7.237 r     -         
wendu.un1_cnt_us_18_s_19_0                  CCU2C        S0       Out     0.698     7.934 r     -         
un1_cnt_us_18_s_19_0_S0                     Net          -        -       -         -           1         
wendu.cnt_us_12[19]                         ORCALUT4     C        In      0.000     7.934 r     -         
wendu.cnt_us_12[19]                         ORCALUT4     Z        Out     0.390     8.325 r     -         
cnt_us_12[19]                               Net          -        -       -         -           1         
wendu.cnt_us[19]                            FD1S3DX      D        In      0.000     8.325 r     -         
==========================================================================================================




====================================
Detailed Report for Clock: global_clock|CLKOP_inferred_clock
====================================



Starting Points with Worst Slack
********************************

                      Starting                                                              Arrival          
Instance              Reference                             Type        Pin     Net         Time        Slack
                      Clock                                                                                  
-------------------------------------------------------------------------------------------------------------
u_uart.U0.cnt[9]      global_clock|CLKOP_inferred_clock     FD1S3DX     Q       cnt[9]      0.955       0.058
u_uart.U0.cnt[10]     global_clock|CLKOP_inferred_clock     FD1S3DX     Q       cnt[10]     0.955       0.058
u_uart.U0.cnt[1]      global_clock|CLKOP_inferred_clock     FD1S3DX     Q       cnt[1]      0.907       0.657
u_uart.U0.cnt[2]      global_clock|CLKOP_inferred_clock     FD1S3DX     Q       cnt[2]      0.907       0.657
u_uart.U0.cnt[5]      global_clock|CLKOP_inferred_clock     FD1S3DX     Q       cnt[5]      0.907       0.657
u_uart.U0.cnt[6]      global_clock|CLKOP_inferred_clock     FD1S3DX     Q       cnt[6]      0.907       0.657
u_uart.U0.cnt[7]      global_clock|CLKOP_inferred_clock     FD1S3DX     Q       cnt[7]      0.907       0.657
u_uart.U0.cnt[8]      global_clock|CLKOP_inferred_clock     FD1S3DX     Q       cnt[8]      0.907       0.657
u_uart.U0.cnt[11]     global_clock|CLKOP_inferred_clock     FD1S3DX     Q       cnt[11]     0.907       0.657
u_uart.U0.cnt[12]     global_clock|CLKOP_inferred_clock     FD1S3DX     Q       cnt[12]     0.907       0.657
=============================================================================================================


Ending Points with Worst Slack
******************************

                      Starting                                                                Required          
Instance              Reference                             Type        Pin     Net           Time         Slack
                      Clock                                                                                     
----------------------------------------------------------------------------------------------------------------
u_uart.U0.cnt[11]     global_clock|CLKOP_inferred_clock     FD1S3DX     D       cnt_s[11]     4.946        0.058
u_uart.U0.cnt[12]     global_clock|CLKOP_inferred_clock     FD1S3DX     D       cnt_s[12]     4.946        0.058
u_uart.U0.cnt[9]      global_clock|CLKOP_inferred_clock     FD1S3DX     D       cnt_s[9]      4.946        0.118
u_uart.U0.cnt[10]     global_clock|CLKOP_inferred_clock     FD1S3DX     D       cnt_s[10]     4.946        0.118
u_uart.U0.cnt[7]      global_clock|CLKOP_inferred_clock     FD1S3DX     D       cnt_s[7]      4.946        0.179
u_uart.U0.cnt[8]      global_clock|CLKOP_inferred_clock     FD1S3DX     D       cnt_s[8]      4.946        0.179
u_uart.U0.cnt[5]      global_clock|CLKOP_inferred_clock     FD1S3DX     D       cnt_s[5]      4.946        0.240
u_uart.U0.cnt[6]      global_clock|CLKOP_inferred_clock     FD1S3DX     D       cnt_s[6]      4.946        0.240
u_uart.U0.cnt[3]      global_clock|CLKOP_inferred_clock     FD1S3DX     D       cnt_s[3]      4.946        0.301
u_uart.U0.cnt[4]      global_clock|CLKOP_inferred_clock     FD1S3DX     D       cnt_s[4]      4.946        0.301
================================================================================================================



Worst Path Information
***********************


Path information for path number 1: 
      Requested Period:                      5.000
    - Setup time:                            0.054
    + Clock delay at ending point:           0.000 (ideal)
    = Required time:                         4.946

    - Propagation time:                      4.888
    - Clock delay at starting point:         0.000 (ideal)
    = Slack (critical) :                     0.058

    Number of logic level(s):                10
    Starting point:                          u_uart.U0.cnt[9] / Q
    Ending point:                            u_uart.U0.cnt[12] / D
    The start point is clocked by            global_clock|CLKOP_inferred_clock [rising] (rise=0.000 fall=2.500 period=5.000) on pin CK
    The end   point is clocked by            global_clock|CLKOP_inferred_clock [rising] (rise=0.000 fall=2.500 period=5.000) on pin CK

Instance / Net                           Pin      Pin               Arrival     No. of    
Name                        Type         Name     Dir     Delay     Time        Fan Out(s)
------------------------------------------------------------------------------------------
u_uart.U0.cnt[9]            FD1S3DX      Q        Out     0.955     0.955 r     -         
cnt[9]                      Net          -        -       -         -           3         
u_uart.U0.cnt9_i_RNO_0      ORCALUT4     A        In      0.000     0.955 r     -         
u_uart.U0.cnt9_i_RNO_0      ORCALUT4     Z        Out     0.606     1.561 f     -         
m16_1                       Net          -        -       -         -           1         
u_uart.U0.cnt9_i_RNO        ORCALUT4     D        In      0.000     1.561 f     -         
u_uart.U0.cnt9_i_RNO        ORCALUT4     Z        Out     0.606     2.167 f     -         
m16_3                       Net          -        -       -         -           1         
u_uart.U0.cnt9_i            ORCALUT4     D        In      0.000     2.167 f     -         
u_uart.U0.cnt9_i            ORCALUT4     Z        Out     0.819     2.986 r     -         
cnt                         Net          -        -       -         -           14        
u_uart.U0.cnt_cry_0[0]      CCU2C        A1       In      0.000     2.986 r     -         
u_uart.U0.cnt_cry_0[0]      CCU2C        COUT     Out     0.900     3.886 r     -         
cnt_cry[0]                  Net          -        -       -         -           1         
u_uart.U0.cnt_cry_0[1]      CCU2C        CIN      In      0.000     3.886 r     -         
u_uart.U0.cnt_cry_0[1]      CCU2C        COUT     Out     0.061     3.947 r     -         
cnt_cry[2]                  Net          -        -       -         -           1         
u_uart.U0.cnt_cry_0[3]      CCU2C        CIN      In      0.000     3.947 r     -         
u_uart.U0.cnt_cry_0[3]      CCU2C        COUT     Out     0.061     4.008 r     -         
cnt_cry[4]                  Net          -        -       -         -           1         
u_uart.U0.cnt_cry_0[5]      CCU2C        CIN      In      0.000     4.008 r     -         
u_uart.U0.cnt_cry_0[5]      CCU2C        COUT     Out     0.061     4.069 r     -         
cnt_cry[6]                  Net          -        -       -         -           1         
u_uart.U0.cnt_cry_0[7]      CCU2C        CIN      In      0.000     4.069 r     -         
u_uart.U0.cnt_cry_0[7]      CCU2C        COUT     Out     0.061     4.130 r     -         
cnt_cry[8]                  Net          -        -       -         -           1         
u_uart.U0.cnt_cry_0[9]      CCU2C        CIN      In      0.000     4.130 r     -         
u_uart.U0.cnt_cry_0[9]      CCU2C        COUT     Out     0.061     4.191 r     -         
cnt_cry[10]                 Net          -        -       -         -           1         
u_uart.U0.cnt_cry_0[11]     CCU2C        CIN      In      0.000     4.191 r     -         
u_uart.U0.cnt_cry_0[11]     CCU2C        S1       Out     0.698     4.888 r     -         
cnt_s[12]                   Net          -        -       -         -           1         
u_uart.U0.cnt[12]           FD1S3DX      D        In      0.000     4.888 r     -         
==========================================================================================




====================================
Detailed Report for Clock: global_clock|CLKOS_inferred_clock
====================================



Starting Points with Worst Slack
********************************

                                         Starting                                                                     Arrival          
Instance                                 Reference                             Type        Pin     Net                Time        Slack
                                         Clock                                                                                         
---------------------------------------------------------------------------------------------------------------------------------------
signal_process.demodu.sample_sum[0]      global_clock|CLKOS_inferred_clock     FD1P3IX     Q       sample_sum[0]      0.985       0.717
signal_process.demodu.din_reg1[0]        global_clock|CLKOS_inferred_clock     FD1S3AX     Q       din_reg1[0]        0.907       0.794
signal_process.demodu.sample_sum[1]      global_clock|CLKOS_inferred_clock     FD1P3IX     Q       sample_sum[1]      0.955       0.807
signal_process.demodu.sample_sum[2]      global_clock|CLKOS_inferred_clock     FD1P3IX     Q       sample_sum[2]      0.955       0.807
signal_process.demodu.sample_sum[3]      global_clock|CLKOS_inferred_clock     FD1P3IX     Q       sample_sum[3]      0.955       0.869
signal_process.demodu.sample_sum[4]      global_clock|CLKOS_inferred_clock     FD1P3IX     Q       sample_sum[4]      0.955       0.869
signal_process.demodu.AD_validcnt[3]     global_clock|CLKOS_inferred_clock     FD1S3IX     Q       AD_validcnt[3]     0.955       0.896
signal_process.demodu.AD_validcnt[5]     global_clock|CLKOS_inferred_clock     FD1S3IX     Q       AD_validcnt[5]     0.955       0.896
signal_process.demodu.din_reg1[1]        global_clock|CLKOS_inferred_clock     FD1S3AX     Q       din_reg1[1]        0.853       0.909
signal_process.demodu.din_reg1[2]        global_clock|CLKOS_inferred_clock     FD1S3AX     Q       din_reg1[2]        0.853       0.909
=======================================================================================================================================


Ending Points with Worst Slack
******************************

                                         Starting                                                                                 Required          
Instance                                 Reference                             Type        Pin     Net                            Time         Slack
                                         Clock                                                                                                      
----------------------------------------------------------------------------------------------------------------------------------------------------
signal_process.demodu.sample_sum[55]     global_clock|CLKOS_inferred_clock     FD1P3IX     D       un3_sample_sum_s_55_0_S0       4.946        0.717
signal_process.demodu.sample_sum[53]     global_clock|CLKOS_inferred_clock     FD1P3IX     D       un3_sample_sum_cry_53_0_S0     4.946        0.777
signal_process.demodu.sample_sum[54]     global_clock|CLKOS_inferred_clock     FD1P3IX     D       un3_sample_sum_cry_53_0_S1     4.946        0.777
signal_process.demodu.sample_sum[51]     global_clock|CLKOS_inferred_clock     FD1P3IX     D       un3_sample_sum_cry_51_0_S0     4.946        0.839
signal_process.demodu.sample_sum[52]     global_clock|CLKOS_inferred_clock     FD1P3IX     D       un3_sample_sum_cry_51_0_S1     4.946        0.839
signal_process.demodu.AD_validcnt[7]     global_clock|CLKOS_inferred_clock     FD1S3IX     D       un1_AD_validcnt_1[7]           4.946        0.896
signal_process.demodu.sample_sum[49]     global_clock|CLKOS_inferred_clock     FD1P3IX     D       un3_sample_sum_cry_49_0_S0     4.946        0.899
signal_process.demodu.sample_sum[50]     global_clock|CLKOS_inferred_clock     FD1P3IX     D       un3_sample_sum_cry_49_0_S1     4.946        0.899
signal_process.demodu.AD_validcnt[5]     global_clock|CLKOS_inferred_clock     FD1S3IX     D       un1_AD_validcnt_1[5]           4.946        0.958
signal_process.demodu.AD_validcnt[6]     global_clock|CLKOS_inferred_clock     FD1S3IX     D       un1_AD_validcnt_1[6]           4.946        0.958
====================================================================================================================================================



Worst Path Information
***********************


Path information for path number 1: 
      Requested Period:                      5.000
    - Setup time:                            0.054
    + Clock delay at ending point:           0.000 (ideal)
    = Required time:                         4.946

    - Propagation time:                      4.229
    - Clock delay at starting point:         0.000 (ideal)
    = Slack (non-critical) :                 0.716

    Number of logic level(s):                29
    Starting point:                          signal_process.demodu.sample_sum[0] / Q
    Ending point:                            signal_process.demodu.sample_sum[55] / D
    The start point is clocked by            global_clock|CLKOS_inferred_clock [rising] (rise=0.000 fall=2.500 period=5.000) on pin CK
    The end   point is clocked by            global_clock|CLKOS_inferred_clock [rising] (rise=0.000 fall=2.500 period=5.000) on pin CK

Instance / Net                                                Pin      Pin               Arrival     No. of    
Name                                              Type        Name     Dir     Delay     Time        Fan Out(s)
---------------------------------------------------------------------------------------------------------------
signal_process.demodu.sample_sum[0]               FD1P3IX     Q        Out     0.985     0.985 r     -         
sample_sum[0]                                     Net         -        -       -         -           4         
signal_process.demodu.un3_sample_sum_cry_0_0      CCU2C       A1       In      0.000     0.985 r     -         
signal_process.demodu.un3_sample_sum_cry_0_0      CCU2C       COUT     Out     0.900     1.885 r     -         
un3_sample_sum_cry_0                              Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_1_0      CCU2C       CIN      In      0.000     1.885 r     -         
signal_process.demodu.un3_sample_sum_cry_1_0      CCU2C       COUT     Out     0.061     1.946 r     -         
un3_sample_sum_cry_2                              Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_3_0      CCU2C       CIN      In      0.000     1.946 r     -         
signal_process.demodu.un3_sample_sum_cry_3_0      CCU2C       COUT     Out     0.061     2.007 r     -         
un3_sample_sum_cry_4                              Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_5_0      CCU2C       CIN      In      0.000     2.007 r     -         
signal_process.demodu.un3_sample_sum_cry_5_0      CCU2C       COUT     Out     0.061     2.068 r     -         
un3_sample_sum_cry_6                              Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_7_0      CCU2C       CIN      In      0.000     2.068 r     -         
signal_process.demodu.un3_sample_sum_cry_7_0      CCU2C       COUT     Out     0.061     2.129 r     -         
un3_sample_sum_cry_8                              Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_9_0      CCU2C       CIN      In      0.000     2.129 r     -         
signal_process.demodu.un3_sample_sum_cry_9_0      CCU2C       COUT     Out     0.061     2.190 r     -         
un3_sample_sum_cry_10                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_11_0     CCU2C       CIN      In      0.000     2.190 r     -         
signal_process.demodu.un3_sample_sum_cry_11_0     CCU2C       COUT     Out     0.061     2.251 r     -         
un3_sample_sum_cry_12                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_13_0     CCU2C       CIN      In      0.000     2.251 r     -         
signal_process.demodu.un3_sample_sum_cry_13_0     CCU2C       COUT     Out     0.061     2.312 r     -         
un3_sample_sum_cry_14                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_15_0     CCU2C       CIN      In      0.000     2.312 r     -         
signal_process.demodu.un3_sample_sum_cry_15_0     CCU2C       COUT     Out     0.061     2.373 r     -         
un3_sample_sum_cry_16                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_17_0     CCU2C       CIN      In      0.000     2.373 r     -         
signal_process.demodu.un3_sample_sum_cry_17_0     CCU2C       COUT     Out     0.061     2.434 r     -         
un3_sample_sum_cry_18                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_19_0     CCU2C       CIN      In      0.000     2.434 r     -         
signal_process.demodu.un3_sample_sum_cry_19_0     CCU2C       COUT     Out     0.061     2.495 r     -         
un3_sample_sum_cry_20                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_21_0     CCU2C       CIN      In      0.000     2.495 r     -         
signal_process.demodu.un3_sample_sum_cry_21_0     CCU2C       COUT     Out     0.061     2.556 r     -         
un3_sample_sum_cry_22                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_23_0     CCU2C       CIN      In      0.000     2.556 r     -         
signal_process.demodu.un3_sample_sum_cry_23_0     CCU2C       COUT     Out     0.061     2.617 r     -         
un3_sample_sum_cry_24                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_25_0     CCU2C       CIN      In      0.000     2.617 r     -         
signal_process.demodu.un3_sample_sum_cry_25_0     CCU2C       COUT     Out     0.061     2.678 r     -         
un3_sample_sum_cry_26                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_27_0     CCU2C       CIN      In      0.000     2.678 r     -         
signal_process.demodu.un3_sample_sum_cry_27_0     CCU2C       COUT     Out     0.061     2.739 r     -         
un3_sample_sum_cry_28                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_29_0     CCU2C       CIN      In      0.000     2.739 r     -         
signal_process.demodu.un3_sample_sum_cry_29_0     CCU2C       COUT     Out     0.061     2.800 r     -         
un3_sample_sum_cry_30                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_31_0     CCU2C       CIN      In      0.000     2.800 r     -         
signal_process.demodu.un3_sample_sum_cry_31_0     CCU2C       COUT     Out     0.061     2.861 r     -         
un3_sample_sum_cry_32                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_33_0     CCU2C       CIN      In      0.000     2.861 r     -         
signal_process.demodu.un3_sample_sum_cry_33_0     CCU2C       COUT     Out     0.061     2.922 r     -         
un3_sample_sum_cry_34                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_35_0     CCU2C       CIN      In      0.000     2.922 r     -         
signal_process.demodu.un3_sample_sum_cry_35_0     CCU2C       COUT     Out     0.061     2.983 r     -         
un3_sample_sum_cry_36                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_37_0     CCU2C       CIN      In      0.000     2.983 r     -         
signal_process.demodu.un3_sample_sum_cry_37_0     CCU2C       COUT     Out     0.061     3.044 r     -         
un3_sample_sum_cry_38                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_39_0     CCU2C       CIN      In      0.000     3.044 r     -         
signal_process.demodu.un3_sample_sum_cry_39_0     CCU2C       COUT     Out     0.061     3.105 r     -         
un3_sample_sum_cry_40                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_41_0     CCU2C       CIN      In      0.000     3.105 r     -         
signal_process.demodu.un3_sample_sum_cry_41_0     CCU2C       COUT     Out     0.061     3.166 r     -         
un3_sample_sum_cry_42                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_43_0     CCU2C       CIN      In      0.000     3.166 r     -         
signal_process.demodu.un3_sample_sum_cry_43_0     CCU2C       COUT     Out     0.061     3.227 r     -         
un3_sample_sum_cry_44                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_45_0     CCU2C       CIN      In      0.000     3.227 r     -         
signal_process.demodu.un3_sample_sum_cry_45_0     CCU2C       COUT     Out     0.061     3.288 r     -         
un3_sample_sum_cry_46                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_47_0     CCU2C       CIN      In      0.000     3.288 r     -         
signal_process.demodu.un3_sample_sum_cry_47_0     CCU2C       COUT     Out     0.061     3.349 r     -         
un3_sample_sum_cry_48                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_49_0     CCU2C       CIN      In      0.000     3.349 r     -         
signal_process.demodu.un3_sample_sum_cry_49_0     CCU2C       COUT     Out     0.061     3.410 r     -         
un3_sample_sum_cry_50                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_51_0     CCU2C       CIN      In      0.000     3.410 r     -         
signal_process.demodu.un3_sample_sum_cry_51_0     CCU2C       COUT     Out     0.061     3.471 r     -         
un3_sample_sum_cry_52                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_53_0     CCU2C       CIN      In      0.000     3.471 r     -         
signal_process.demodu.un3_sample_sum_cry_53_0     CCU2C       COUT     Out     0.061     3.532 r     -         
un3_sample_sum_cry_54                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_s_55_0       CCU2C       CIN      In      0.000     3.532 r     -         
signal_process.demodu.un3_sample_sum_s_55_0       CCU2C       S0       Out     0.698     4.229 r     -         
un3_sample_sum_s_55_0_S0                          Net         -        -       -         -           1         
signal_process.demodu.sample_sum[55]              FD1P3IX     D        In      0.000     4.229 r     -         
===============================================================================================================




====================================
Detailed Report for Clock: System
====================================



Starting Points with Worst Slack
********************************

                                             Starting                                                   Arrival          
Instance                                     Reference     Type           Pin     Net                   Time        Slack
                                             Clock                                                                       
-------------------------------------------------------------------------------------------------------------------------
signal_process.demodu.fifo.AND2_t4           System        AND2           Z       wren_i                0.000       2.317
signal_process.demodu.fifo.AND2_t3           System        AND2           Z       rden_i                0.000       2.559
signal_process.rs422.un3_p_sum[36:71]        System        MULT18X18D     P0      un3_p_sum_add         0.000       2.861
signal_process.rs422.un3_p_sum[36:71]        System        MULT18X18D     P0      un3_p_sum_add         0.000       2.861
signal_process.rs422.un3_p_sum_add[0:53]     System        ALU54B         R36     un3_p_sum_add[36]     0.000       2.861
signal_process.rs422.un3_p_sum[36:71]        System        MULT18X18D     P1      un3_p_sum_2[1]        0.000       2.921
signal_process.rs422.un3_p_sum[36:71]        System        MULT18X18D     P1      un3_p_sum_2[1]        0.000       2.921
signal_process.rs422.un3_p_sum[36:71]        System        MULT18X18D     P2      un3_p_sum_2[2]        0.000       2.921
signal_process.rs422.un3_p_sum[36:71]        System        MULT18X18D     P2      un3_p_sum_2[2]        0.000       2.921
signal_process.rs422.un3_p_sum_add[0:53]     System        ALU54B         R37     un3_p_sum_add[37]     0.000       2.921
=========================================================================================================================


Ending Points with Worst Slack
******************************

                                     Starting                                            Required          
Instance                             Reference     Type        Pin     Net               Time         Slack
                                     Clock                                                                 
-----------------------------------------------------------------------------------------------------------
signal_process.demodu.fifo.FF_18     System        FD1S3DX     D       full_d            4.946        2.317
signal_process.demodu.fifo.FF_19     System        FD1S3BX     D       empty_d           4.946        2.559
signal_process.rs422.p_sum[54]       System        FD1P3DX     D       un3_p_sum[53]     4.946        2.861
signal_process.rs422.p_sum[55]       System        FD1P3DX     D       un3_p_sum[54]     4.946        2.861
signal_process.rs422.p_sum[52]       System        FD1P3DX     D       un3_p_sum[51]     4.946        2.921
signal_process.rs422.p_sum[53]       System        FD1P3DX     D       un3_p_sum[52]     4.946        2.921
signal_process.rs422.p_sum[50]       System        FD1P3DX     D       un3_p_sum[49]     4.946        2.982
signal_process.rs422.p_sum[51]       System        FD1P3DX     D       un3_p_sum[50]     4.946        2.982
signal_process.rs422.p_sum[48]       System        FD1P3DX     D       un3_p_sum[47]     4.946        3.043
signal_process.rs422.p_sum[49]       System        FD1P3DX     D       un3_p_sum[48]     4.946        3.043
===========================================================================================================



Worst Path Information
***********************


Path information for path number 1: 
      Requested Period:                      5.000
    - Setup time:                            0.054
    + Clock delay at ending point:           0.000 (ideal)
    = Required time:                         4.946

    - Propagation time:                      2.630
    - Clock delay at starting point:         0.000 (ideal)
    - Estimated clock delay at start point:  -0.000
    = Slack (non-critical) :                 2.316

    Number of logic level(s):                4
    Starting point:                          signal_process.demodu.fifo.AND2_t4 / Z
    Ending point:                            signal_process.demodu.fifo.FF_18 / D
    The start point is clocked by            System [rising]
    The end   point is clocked by            global_clock|CLKOS_inferred_clock [rising] (rise=0.000 fall=2.500 period=5.000) on pin CK

Instance / Net                                      Pin      Pin               Arrival     No. of    
Name                                   Type         Name     Dir     Delay     Time        Fan Out(s)
-----------------------------------------------------------------------------------------------------
signal_process.demodu.fifo.AND2_t4     AND2         Z        Out     0.000     0.000 r     -         
wren_i                                 Net          -        -       -         -           21        
signal_process.demodu.fifo.INV_5       INV          A        In      0.000     0.000 r     -         
signal_process.demodu.fifo.INV_5       INV          Z        Out     0.426     0.426 f     -         
wren_i_inv                             Net          -        -       -         -           1         
signal_process.demodu.fifo.g_cmp_3     CCU2C        B1       In      0.000     0.426 f     -         
signal_process.demodu.fifo.g_cmp_3     CCU2C        COUT     Out     0.900     1.326 r     -         
cmp_ge_d1_c                            Net          -        -       -         -           1         
signal_process.demodu.fifo.a1          CCU2C        CIN      In      0.000     1.326 r     -         
signal_process.demodu.fifo.a1          CCU2C        S0       Out     0.698     2.023 r     -         
cmp_ge_d1                              Net          -        -       -         -           1         
signal_process.demodu.fifo.LUT4_2      ROM16X1A     AD2      In      0.000     2.023 r     -         
signal_process.demodu.fifo.LUT4_2      ROM16X1A     DO0      Out     0.606     2.630 r     -         
full_d                                 Net          -        -       -         -           1         
signal_process.demodu.fifo.FF_18       FD1S3DX      D        In      0.000     2.630 r     -         
=====================================================================================================



##### END OF TIMING REPORT #####]

Timing exceptions that could not be applied

Finished final timing analysis (Real Time elapsed 0h:00m:08s; CPU Time elapsed 0h:00m:07s; Memory used current: 213MB peak: 220MB)


Finished timing report (Real Time elapsed 0h:00m:08s; CPU Time elapsed 0h:00m:07s; Memory used current: 213MB peak: 220MB)

---------------------------------------
Resource Usage Report
Part: lfe5u_25f-7

Register bits: 1044 of 24288 (4%)
PIC Latch:       0
I/O cells:       32
Block Rams : 2 of 56 (3%)

DSP primitives:       5 of 42 (11%)

Details:
ALU54B:         1
AND2:           3
BB:             1
CCU2C:          241
EHXPLLL:        1
FD1P3AX:        371
FD1P3BX:        3
FD1P3DX:        154
FD1P3IX:        111
FD1S3AX:        250
FD1S3BX:        3
FD1S3DX:        109
FD1S3IX:        9
FD1S3JX:        6
GSR:            1
IB:             13
IFS1P3DX:       13
INV:            9
MULT18X18D:     4
OB:             18
OFS1P3DX:       15
ORCALUT4:       281
PDPW16KD:       2
PFUMX:          3
PUR:            1
ROM16X1A:       2
VHI:            15
VLO:            15
XOR2:           1
Mapper successful!

At Mapper Exit (Real Time elapsed 0h:00m:08s; CPU Time elapsed 0h:00m:07s; Memory used current: 89MB peak: 220MB)

Process took 0h:00m:08s realtime, 0h:00m:08s cputime
# Thu Sep 25 10:29:35 2025

###########################################################]


Synthesis exit by 0.

edif2ngd  -l "ECP5U" -d LFE5U-25F -path "D:/Project/TLH50_03J_JZ_20250925/impl1" -path "D:/Project/TLH50_03J_JZ_20250925"   "D:/Project/TLH50_03J_JZ_20250925/impl1/INS350_5J_JZ_impl1.edi" "INS350_5J_JZ_impl1.ngo"   
edif2ngd:  version Diamond (64-bit) 3.12.1.454

Copyright (c) 1991-1994 by NeoCAD Inc. All rights reserved.
Copyright (c) 1995 AT&T Corp.   All rights reserved.
Copyright (c) 1995-2001 Lucent Technologies Inc.  All rights reserved.
Copyright (c) 2001 Agere Systems   All rights reserved.
Copyright (c) 2002-2020 Lattice Semiconductor Corporation,  All rights reserved.
    <postMsg mid="1121027" type="Warning" dynamic="1" navigation="0" arg0="MEM_INIT_FILE"  />
    <postMsg mid="1121027" type="Warning" dynamic="1" navigation="0" arg0="MEM_INIT_FILE"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iTRANSMIT_COFF"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iOUTPUT_SCALE"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iDELAYED"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iWID_OUT"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iWID_IN"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="bPOLAR"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="wCLOSED"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iFEEDBACK_SCALE"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="DA_CONSTANT"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iWID_OUT"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iWID_IN"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iWID_TRANS"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iWID_OUT"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iWID_IN"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iWID_OUT"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iWID_IN"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="acum_cnt"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iAD_VALID_START"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iTRANSIT_TIME"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iWID_TRANS"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iWID_RS422"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="UART_BAUD"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="SYS_FREQ"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iWID_TRANS"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iTRANSIT_TIME"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iAD_VALID_START"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="acum_cnt"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iWID_IN"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iWID_OUT"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iWID_IN"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iWID_OUT"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iWID_IN"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iWID_OUT"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="wCLOSED"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iWID_TRANS"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="DA_CONSTANT"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iFEEDBACK_SCALE"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iWID_IN"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iWID_OUT"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iDELAYED"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="bPOLAR"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iOUTPUT_SCALE"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iTRANSMIT_COFF"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iTRANSMIT_COFF"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="DA_CONSTANT"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iDELAYED"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iOUTPUT_SCALE"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="bPOLAR"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iFEEDBACK_SCALE"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iAD_VALID_START"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iTRANSIT_TIME"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="wCLOSED"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iWID_SIGN"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iWID_RS422"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iWID_DA"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iWID_AD"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iWID_TRANS"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="acum_cnt"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="SYS_FREQ"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="UART_BAUD"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="TX_UART_BAUD"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="TX_SYS_FREQ"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="RX_UART_BAUD"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="RX_SYS_FREQ"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iWID_RS422"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iWID_RS422"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="RX_SYS_FREQ"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="RX_UART_BAUD"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="TX_SYS_FREQ"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="TX_UART_BAUD"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="acum_cnt"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iWID_TRANS"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iWID_AD"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iWID_DA"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iWID_RS422"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iWID_SIGN"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="wCLOSED"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iTRANSIT_TIME"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iAD_VALID_START"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iFEEDBACK_SCALE"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iOUTPUT_SCALE"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="bPOLAR"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iDELAYED"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="DA_CONSTANT"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iTRANSMIT_COFF"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="RX_TX"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iTRANSMIT_COFF"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iDELAYED"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iOUTPUT_SCALE"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="bPOLAR"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iFEEDBACK_SCALE"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="acum_cnt"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iAD_VALID_START"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iTRANSIT_TIME"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="wCLOSED"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iWID_SIGN"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iWID_TRANS"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="TX_UART_BAUD"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="TX_SYS_FREQ"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="RX_UART_BAUD"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="RX_SYS_FREQ"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iWID_RS422"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="DA_CONSTANT"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iWID_DA"  />
    <postMsg mid="1121028" type="Warning" dynamic="1" navigation="0" arg0="iWID_AD"  />
Writing the design to INS350_5J_JZ_impl1.ngo...

Total CPU Time: 0 secs  
Total REAL Time: 0 secs  
Peak Memory Usage: 31 MB


ngdbuild  -a "ECP5U" -d LFE5U-25F  -p "D:/Software/lscc/diamond/3.12/ispfpga/sa5p00/data"  -p "D:/Project/TLH50_03J_JZ_20250925/impl1" -p "D:/Project/TLH50_03J_JZ_20250925"  "INS350_5J_JZ_impl1.ngo" "INS350_5J_JZ_impl1.ngd"  	
ngdbuild:  version Diamond (64-bit) 3.12.1.454

Copyright (c) 1991-1994 by NeoCAD Inc. All rights reserved.
Copyright (c) 1995 AT&T Corp.   All rights reserved.
Copyright (c) 1995-2001 Lucent Technologies Inc.  All rights reserved.
Copyright (c) 2001 Agere Systems   All rights reserved.
Copyright (c) 2002-2020 Lattice Semiconductor Corporation,  All rights reserved.
Reading 'INS350_5J_JZ_impl1.ngo' ...
Loading NGL library 'D:/Software/lscc/diamond/3.12/ispfpga/sa5p00/data/sa5plib.ngl'...
Loading NGL library 'D:/Software/lscc/diamond/3.12/ispfpga/xo2c00/data/xo2clib.ngl'...
Loading NGL library 'D:/Software/lscc/diamond/3.12/ispfpga/mg5g00/data/mg5glib.ngl'...
Loading NGL library 'D:/Software/lscc/diamond/3.12/ispfpga/or5g00/data/orc5glib.ngl'...


Running DRC...

    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_SIGNEDR" arg2="signal_process/rs422/un3_p_sum_add_SIGNEDR"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_R22" arg2="signal_process/rs422/un3_p_sum_add_R22"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_R21" arg2="signal_process/rs422/un3_p_sum_add_R21"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_R20" arg2="signal_process/rs422/un3_p_sum_add_R20"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_R19" arg2="signal_process/rs422/un3_p_sum_add_R19"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_R18" arg2="signal_process/rs422/un3_p_sum_add_R18"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_R17" arg2="signal_process/rs422/un3_p_sum_add_R17"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_R16" arg2="signal_process/rs422/un3_p_sum_add_R16"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_R15" arg2="signal_process/rs422/un3_p_sum_add_R15"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_R14" arg2="signal_process/rs422/un3_p_sum_add_R14"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_R13" arg2="signal_process/rs422/un3_p_sum_add_R13"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_R12" arg2="signal_process/rs422/un3_p_sum_add_R12"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_R11" arg2="signal_process/rs422/un3_p_sum_add_R11"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_R10" arg2="signal_process/rs422/un3_p_sum_add_R10"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_R9" arg2="signal_process/rs422/un3_p_sum_add_R9"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_R8" arg2="signal_process/rs422/un3_p_sum_add_R8"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_R7" arg2="signal_process/rs422/un3_p_sum_add_R7"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_R6" arg2="signal_process/rs422/un3_p_sum_add_R6"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_R5" arg2="signal_process/rs422/un3_p_sum_add_R5"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_R4" arg2="signal_process/rs422/un3_p_sum_add_R4"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_R3" arg2="signal_process/rs422/un3_p_sum_add_R3"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_R2" arg2="signal_process/rs422/un3_p_sum_add_R2"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_R1" arg2="signal_process/rs422/un3_p_sum_add_R1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_R0" arg2="signal_process/rs422/un3_p_sum_add_R0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO53" arg2="signal_process/rs422/un3_p_sum_add_CO53"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO52" arg2="signal_process/rs422/un3_p_sum_add_CO52"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO51" arg2="signal_process/rs422/un3_p_sum_add_CO51"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO50" arg2="signal_process/rs422/un3_p_sum_add_CO50"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO49" arg2="signal_process/rs422/un3_p_sum_add_CO49"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO48" arg2="signal_process/rs422/un3_p_sum_add_CO48"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO47" arg2="signal_process/rs422/un3_p_sum_add_CO47"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO46" arg2="signal_process/rs422/un3_p_sum_add_CO46"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO45" arg2="signal_process/rs422/un3_p_sum_add_CO45"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO44" arg2="signal_process/rs422/un3_p_sum_add_CO44"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO43" arg2="signal_process/rs422/un3_p_sum_add_CO43"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO42" arg2="signal_process/rs422/un3_p_sum_add_CO42"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO41" arg2="signal_process/rs422/un3_p_sum_add_CO41"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO40" arg2="signal_process/rs422/un3_p_sum_add_CO40"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO39" arg2="signal_process/rs422/un3_p_sum_add_CO39"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO38" arg2="signal_process/rs422/un3_p_sum_add_CO38"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO37" arg2="signal_process/rs422/un3_p_sum_add_CO37"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO36" arg2="signal_process/rs422/un3_p_sum_add_CO36"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO35" arg2="signal_process/rs422/un3_p_sum_add_CO35"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO34" arg2="signal_process/rs422/un3_p_sum_add_CO34"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO33" arg2="signal_process/rs422/un3_p_sum_add_CO33"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO32" arg2="signal_process/rs422/un3_p_sum_add_CO32"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO31" arg2="signal_process/rs422/un3_p_sum_add_CO31"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO30" arg2="signal_process/rs422/un3_p_sum_add_CO30"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO29" arg2="signal_process/rs422/un3_p_sum_add_CO29"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO28" arg2="signal_process/rs422/un3_p_sum_add_CO28"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO27" arg2="signal_process/rs422/un3_p_sum_add_CO27"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO26" arg2="signal_process/rs422/un3_p_sum_add_CO26"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO25" arg2="signal_process/rs422/un3_p_sum_add_CO25"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO24" arg2="signal_process/rs422/un3_p_sum_add_CO24"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO23" arg2="signal_process/rs422/un3_p_sum_add_CO23"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO22" arg2="signal_process/rs422/un3_p_sum_add_CO22"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO21" arg2="signal_process/rs422/un3_p_sum_add_CO21"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO20" arg2="signal_process/rs422/un3_p_sum_add_CO20"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO19" arg2="signal_process/rs422/un3_p_sum_add_CO19"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO18" arg2="signal_process/rs422/un3_p_sum_add_CO18"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO17" arg2="signal_process/rs422/un3_p_sum_add_CO17"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO16" arg2="signal_process/rs422/un3_p_sum_add_CO16"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO15" arg2="signal_process/rs422/un3_p_sum_add_CO15"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO14" arg2="signal_process/rs422/un3_p_sum_add_CO14"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO13" arg2="signal_process/rs422/un3_p_sum_add_CO13"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO12" arg2="signal_process/rs422/un3_p_sum_add_CO12"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO11" arg2="signal_process/rs422/un3_p_sum_add_CO11"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO10" arg2="signal_process/rs422/un3_p_sum_add_CO10"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO9" arg2="signal_process/rs422/un3_p_sum_add_CO9"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO8" arg2="signal_process/rs422/un3_p_sum_add_CO8"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO7" arg2="signal_process/rs422/un3_p_sum_add_CO7"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO6" arg2="signal_process/rs422/un3_p_sum_add_CO6"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO5" arg2="signal_process/rs422/un3_p_sum_add_CO5"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO4" arg2="signal_process/rs422/un3_p_sum_add_CO4"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO3" arg2="signal_process/rs422/un3_p_sum_add_CO3"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO2" arg2="signal_process/rs422/un3_p_sum_add_CO2"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO1" arg2="signal_process/rs422/un3_p_sum_add_CO1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_CO0" arg2="signal_process/rs422/un3_p_sum_add_CO0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_EQZ" arg2="signal_process/rs422/un3_p_sum_add_EQZ"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_EQZM" arg2="signal_process/rs422/un3_p_sum_add_EQZM"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_EQOM" arg2="signal_process/rs422/un3_p_sum_add_EQOM"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_EQPAT" arg2="signal_process/rs422/un3_p_sum_add_EQPAT"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_EQPATB" arg2="signal_process/rs422/un3_p_sum_add_EQPATB"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_OVER" arg2="signal_process/rs422/un3_p_sum_add_OVER"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_UNDER" arg2="signal_process/rs422/un3_p_sum_add_UNDER"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_add_OVERUNDER" arg2="signal_process/rs422/un3_p_sum_add_OVERUNDER"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA17" arg2="signal_process/rs422/un3_p_sum_SROA17"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA16" arg2="signal_process/rs422/un3_p_sum_SROA16"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA15" arg2="signal_process/rs422/un3_p_sum_SROA15"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA14" arg2="signal_process/rs422/un3_p_sum_SROA14"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA13" arg2="signal_process/rs422/un3_p_sum_SROA13"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA12" arg2="signal_process/rs422/un3_p_sum_SROA12"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA11" arg2="signal_process/rs422/un3_p_sum_SROA11"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA10" arg2="signal_process/rs422/un3_p_sum_SROA10"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA9" arg2="signal_process/rs422/un3_p_sum_SROA9"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA8" arg2="signal_process/rs422/un3_p_sum_SROA8"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA7" arg2="signal_process/rs422/un3_p_sum_SROA7"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA6" arg2="signal_process/rs422/un3_p_sum_SROA6"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA5" arg2="signal_process/rs422/un3_p_sum_SROA5"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA4" arg2="signal_process/rs422/un3_p_sum_SROA4"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA3" arg2="signal_process/rs422/un3_p_sum_SROA3"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA2" arg2="signal_process/rs422/un3_p_sum_SROA2"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA1" arg2="signal_process/rs422/un3_p_sum_SROA1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA0" arg2="signal_process/rs422/un3_p_sum_SROA0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB17" arg2="signal_process/rs422/un3_p_sum_SROB17"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB16" arg2="signal_process/rs422/un3_p_sum_SROB16"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB15" arg2="signal_process/rs422/un3_p_sum_SROB15"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB14" arg2="signal_process/rs422/un3_p_sum_SROB14"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB13" arg2="signal_process/rs422/un3_p_sum_SROB13"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB12" arg2="signal_process/rs422/un3_p_sum_SROB12"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB11" arg2="signal_process/rs422/un3_p_sum_SROB11"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB10" arg2="signal_process/rs422/un3_p_sum_SROB10"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB9" arg2="signal_process/rs422/un3_p_sum_SROB9"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB8" arg2="signal_process/rs422/un3_p_sum_SROB8"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB7" arg2="signal_process/rs422/un3_p_sum_SROB7"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB6" arg2="signal_process/rs422/un3_p_sum_SROB6"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB5" arg2="signal_process/rs422/un3_p_sum_SROB5"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB4" arg2="signal_process/rs422/un3_p_sum_SROB4"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB3" arg2="signal_process/rs422/un3_p_sum_SROB3"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB2" arg2="signal_process/rs422/un3_p_sum_SROB2"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB1" arg2="signal_process/rs422/un3_p_sum_SROB1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB0" arg2="signal_process/rs422/un3_p_sum_SROB0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC17" arg2="signal_process/rs422/un3_p_sum_ROC17"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC16" arg2="signal_process/rs422/un3_p_sum_ROC16"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC15" arg2="signal_process/rs422/un3_p_sum_ROC15"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC14" arg2="signal_process/rs422/un3_p_sum_ROC14"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC13" arg2="signal_process/rs422/un3_p_sum_ROC13"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC12" arg2="signal_process/rs422/un3_p_sum_ROC12"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC11" arg2="signal_process/rs422/un3_p_sum_ROC11"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC10" arg2="signal_process/rs422/un3_p_sum_ROC10"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC9" arg2="signal_process/rs422/un3_p_sum_ROC9"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC8" arg2="signal_process/rs422/un3_p_sum_ROC8"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC7" arg2="signal_process/rs422/un3_p_sum_ROC7"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC6" arg2="signal_process/rs422/un3_p_sum_ROC6"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC5" arg2="signal_process/rs422/un3_p_sum_ROC5"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC4" arg2="signal_process/rs422/un3_p_sum_ROC4"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC3" arg2="signal_process/rs422/un3_p_sum_ROC3"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC2" arg2="signal_process/rs422/un3_p_sum_ROC2"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC1" arg2="signal_process/rs422/un3_p_sum_ROC1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC0" arg2="signal_process/rs422/un3_p_sum_ROC0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA17_0" arg2="signal_process/rs422/un3_p_sum_SROA17_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA16_0" arg2="signal_process/rs422/un3_p_sum_SROA16_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA15_0" arg2="signal_process/rs422/un3_p_sum_SROA15_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA14_0" arg2="signal_process/rs422/un3_p_sum_SROA14_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA13_0" arg2="signal_process/rs422/un3_p_sum_SROA13_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA12_0" arg2="signal_process/rs422/un3_p_sum_SROA12_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA11_0" arg2="signal_process/rs422/un3_p_sum_SROA11_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA10_0" arg2="signal_process/rs422/un3_p_sum_SROA10_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA9_0" arg2="signal_process/rs422/un3_p_sum_SROA9_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA8_0" arg2="signal_process/rs422/un3_p_sum_SROA8_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA7_0" arg2="signal_process/rs422/un3_p_sum_SROA7_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA6_0" arg2="signal_process/rs422/un3_p_sum_SROA6_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA5_0" arg2="signal_process/rs422/un3_p_sum_SROA5_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA4_0" arg2="signal_process/rs422/un3_p_sum_SROA4_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA3_0" arg2="signal_process/rs422/un3_p_sum_SROA3_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA2_0" arg2="signal_process/rs422/un3_p_sum_SROA2_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA1_0" arg2="signal_process/rs422/un3_p_sum_SROA1_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA0_0" arg2="signal_process/rs422/un3_p_sum_SROA0_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB17_0" arg2="signal_process/rs422/un3_p_sum_SROB17_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB16_0" arg2="signal_process/rs422/un3_p_sum_SROB16_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB15_0" arg2="signal_process/rs422/un3_p_sum_SROB15_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB14_0" arg2="signal_process/rs422/un3_p_sum_SROB14_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB13_0" arg2="signal_process/rs422/un3_p_sum_SROB13_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB12_0" arg2="signal_process/rs422/un3_p_sum_SROB12_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB11_0" arg2="signal_process/rs422/un3_p_sum_SROB11_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB10_0" arg2="signal_process/rs422/un3_p_sum_SROB10_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB9_0" arg2="signal_process/rs422/un3_p_sum_SROB9_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB8_0" arg2="signal_process/rs422/un3_p_sum_SROB8_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB7_0" arg2="signal_process/rs422/un3_p_sum_SROB7_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB6_0" arg2="signal_process/rs422/un3_p_sum_SROB6_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB5_0" arg2="signal_process/rs422/un3_p_sum_SROB5_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB4_0" arg2="signal_process/rs422/un3_p_sum_SROB4_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB3_0" arg2="signal_process/rs422/un3_p_sum_SROB3_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB2_0" arg2="signal_process/rs422/un3_p_sum_SROB2_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB1_0" arg2="signal_process/rs422/un3_p_sum_SROB1_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB0_0" arg2="signal_process/rs422/un3_p_sum_SROB0_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC17_0" arg2="signal_process/rs422/un3_p_sum_ROC17_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC16_0" arg2="signal_process/rs422/un3_p_sum_ROC16_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC15_0" arg2="signal_process/rs422/un3_p_sum_ROC15_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC14_0" arg2="signal_process/rs422/un3_p_sum_ROC14_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC13_0" arg2="signal_process/rs422/un3_p_sum_ROC13_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC12_0" arg2="signal_process/rs422/un3_p_sum_ROC12_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC11_0" arg2="signal_process/rs422/un3_p_sum_ROC11_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC10_0" arg2="signal_process/rs422/un3_p_sum_ROC10_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC9_0" arg2="signal_process/rs422/un3_p_sum_ROC9_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC8_0" arg2="signal_process/rs422/un3_p_sum_ROC8_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC7_0" arg2="signal_process/rs422/un3_p_sum_ROC7_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC6_0" arg2="signal_process/rs422/un3_p_sum_ROC6_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC5_0" arg2="signal_process/rs422/un3_p_sum_ROC5_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC4_0" arg2="signal_process/rs422/un3_p_sum_ROC4_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC3_0" arg2="signal_process/rs422/un3_p_sum_ROC3_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC2_0" arg2="signal_process/rs422/un3_p_sum_ROC2_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC1_0" arg2="signal_process/rs422/un3_p_sum_ROC1_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC0_0" arg2="signal_process/rs422/un3_p_sum_ROC0_0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SIGNEDP_1" arg2="signal_process/rs422/un3_p_sum_SIGNEDP_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_2[35]" arg2="signal_process/rs422/un3_p_sum_2[35]"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_2[34]" arg2="signal_process/rs422/un3_p_sum_2[34]"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_2[33]" arg2="signal_process/rs422/un3_p_sum_2[33]"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_2[32]" arg2="signal_process/rs422/un3_p_sum_2[32]"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_2[31]" arg2="signal_process/rs422/un3_p_sum_2[31]"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_2[30]" arg2="signal_process/rs422/un3_p_sum_2[30]"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_2[29]" arg2="signal_process/rs422/un3_p_sum_2[29]"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_2[28]" arg2="signal_process/rs422/un3_p_sum_2[28]"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_2[27]" arg2="signal_process/rs422/un3_p_sum_2[27]"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_2[26]" arg2="signal_process/rs422/un3_p_sum_2[26]"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_2[25]" arg2="signal_process/rs422/un3_p_sum_2[25]"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_2[24]" arg2="signal_process/rs422/un3_p_sum_2[24]"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_2[23]" arg2="signal_process/rs422/un3_p_sum_2[23]"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_2[22]" arg2="signal_process/rs422/un3_p_sum_2[22]"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_2[21]" arg2="signal_process/rs422/un3_p_sum_2[21]"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_2[20]" arg2="signal_process/rs422/un3_p_sum_2[20]"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_2[19]" arg2="signal_process/rs422/un3_p_sum_2[19]"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA17_1" arg2="signal_process/rs422/un3_p_sum_SROA17_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA16_1" arg2="signal_process/rs422/un3_p_sum_SROA16_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA15_1" arg2="signal_process/rs422/un3_p_sum_SROA15_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA14_1" arg2="signal_process/rs422/un3_p_sum_SROA14_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA13_1" arg2="signal_process/rs422/un3_p_sum_SROA13_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA12_1" arg2="signal_process/rs422/un3_p_sum_SROA12_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA11_1" arg2="signal_process/rs422/un3_p_sum_SROA11_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA10_1" arg2="signal_process/rs422/un3_p_sum_SROA10_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA9_1" arg2="signal_process/rs422/un3_p_sum_SROA9_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA8_1" arg2="signal_process/rs422/un3_p_sum_SROA8_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA7_1" arg2="signal_process/rs422/un3_p_sum_SROA7_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA6_1" arg2="signal_process/rs422/un3_p_sum_SROA6_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA5_1" arg2="signal_process/rs422/un3_p_sum_SROA5_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA4_1" arg2="signal_process/rs422/un3_p_sum_SROA4_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA3_1" arg2="signal_process/rs422/un3_p_sum_SROA3_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA2_1" arg2="signal_process/rs422/un3_p_sum_SROA2_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA1_1" arg2="signal_process/rs422/un3_p_sum_SROA1_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROA0_1" arg2="signal_process/rs422/un3_p_sum_SROA0_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB17_1" arg2="signal_process/rs422/un3_p_sum_SROB17_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB16_1" arg2="signal_process/rs422/un3_p_sum_SROB16_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB15_1" arg2="signal_process/rs422/un3_p_sum_SROB15_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB14_1" arg2="signal_process/rs422/un3_p_sum_SROB14_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB13_1" arg2="signal_process/rs422/un3_p_sum_SROB13_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB12_1" arg2="signal_process/rs422/un3_p_sum_SROB12_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB11_1" arg2="signal_process/rs422/un3_p_sum_SROB11_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB10_1" arg2="signal_process/rs422/un3_p_sum_SROB10_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB9_1" arg2="signal_process/rs422/un3_p_sum_SROB9_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB8_1" arg2="signal_process/rs422/un3_p_sum_SROB8_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB7_1" arg2="signal_process/rs422/un3_p_sum_SROB7_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB6_1" arg2="signal_process/rs422/un3_p_sum_SROB6_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB5_1" arg2="signal_process/rs422/un3_p_sum_SROB5_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB4_1" arg2="signal_process/rs422/un3_p_sum_SROB4_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB3_1" arg2="signal_process/rs422/un3_p_sum_SROB3_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB2_1" arg2="signal_process/rs422/un3_p_sum_SROB2_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB1_1" arg2="signal_process/rs422/un3_p_sum_SROB1_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_SROB0_1" arg2="signal_process/rs422/un3_p_sum_SROB0_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROA17_1" arg2="signal_process/rs422/un3_p_sum_ROA17_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROA16_1" arg2="signal_process/rs422/un3_p_sum_ROA16_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROA15_1" arg2="signal_process/rs422/un3_p_sum_ROA15_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROA14_1" arg2="signal_process/rs422/un3_p_sum_ROA14_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROA13_1" arg2="signal_process/rs422/un3_p_sum_ROA13_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROA12_1" arg2="signal_process/rs422/un3_p_sum_ROA12_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROA11_1" arg2="signal_process/rs422/un3_p_sum_ROA11_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROA10_1" arg2="signal_process/rs422/un3_p_sum_ROA10_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROA9_1" arg2="signal_process/rs422/un3_p_sum_ROA9_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROA8_1" arg2="signal_process/rs422/un3_p_sum_ROA8_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROA7_1" arg2="signal_process/rs422/un3_p_sum_ROA7_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROA6_1" arg2="signal_process/rs422/un3_p_sum_ROA6_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROA5_1" arg2="signal_process/rs422/un3_p_sum_ROA5_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROA4_1" arg2="signal_process/rs422/un3_p_sum_ROA4_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROA3_1" arg2="signal_process/rs422/un3_p_sum_ROA3_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROA2_1" arg2="signal_process/rs422/un3_p_sum_ROA2_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROA1_1" arg2="signal_process/rs422/un3_p_sum_ROA1_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROA0_1" arg2="signal_process/rs422/un3_p_sum_ROA0_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROB17_1" arg2="signal_process/rs422/un3_p_sum_ROB17_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROB16_1" arg2="signal_process/rs422/un3_p_sum_ROB16_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROB15_1" arg2="signal_process/rs422/un3_p_sum_ROB15_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROB14_1" arg2="signal_process/rs422/un3_p_sum_ROB14_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROB13_1" arg2="signal_process/rs422/un3_p_sum_ROB13_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROB12_1" arg2="signal_process/rs422/un3_p_sum_ROB12_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROB11_1" arg2="signal_process/rs422/un3_p_sum_ROB11_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROB10_1" arg2="signal_process/rs422/un3_p_sum_ROB10_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROB9_1" arg2="signal_process/rs422/un3_p_sum_ROB9_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROB8_1" arg2="signal_process/rs422/un3_p_sum_ROB8_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROB7_1" arg2="signal_process/rs422/un3_p_sum_ROB7_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROB6_1" arg2="signal_process/rs422/un3_p_sum_ROB6_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROB5_1" arg2="signal_process/rs422/un3_p_sum_ROB5_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROB4_1" arg2="signal_process/rs422/un3_p_sum_ROB4_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROB3_1" arg2="signal_process/rs422/un3_p_sum_ROB3_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROB2_1" arg2="signal_process/rs422/un3_p_sum_ROB2_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROB1_1" arg2="signal_process/rs422/un3_p_sum_ROB1_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROB0_1" arg2="signal_process/rs422/un3_p_sum_ROB0_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC17_1" arg2="signal_process/rs422/un3_p_sum_ROC17_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC16_1" arg2="signal_process/rs422/un3_p_sum_ROC16_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC15_1" arg2="signal_process/rs422/un3_p_sum_ROC15_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC14_1" arg2="signal_process/rs422/un3_p_sum_ROC14_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC13_1" arg2="signal_process/rs422/un3_p_sum_ROC13_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC12_1" arg2="signal_process/rs422/un3_p_sum_ROC12_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC11_1" arg2="signal_process/rs422/un3_p_sum_ROC11_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC10_1" arg2="signal_process/rs422/un3_p_sum_ROC10_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC9_1" arg2="signal_process/rs422/un3_p_sum_ROC9_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC8_1" arg2="signal_process/rs422/un3_p_sum_ROC8_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC7_1" arg2="signal_process/rs422/un3_p_sum_ROC7_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC6_1" arg2="signal_process/rs422/un3_p_sum_ROC6_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC5_1" arg2="signal_process/rs422/un3_p_sum_ROC5_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC4_1" arg2="signal_process/rs422/un3_p_sum_ROC4_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC3_1" arg2="signal_process/rs422/un3_p_sum_ROC3_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC2_1" arg2="signal_process/rs422/un3_p_sum_ROC2_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC1_1" arg2="signal_process/rs422/un3_p_sum_ROC1_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/rs422/un3_p_sum_ROC0_1" arg2="signal_process/rs422/un3_p_sum_ROC0_1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_SIGNEDP" arg2="signal_process/modu/un1_dout_mult_SIGNEDP"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_P35" arg2="signal_process/modu/un1_dout_mult_P35"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_P34" arg2="signal_process/modu/un1_dout_mult_P34"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_P33" arg2="signal_process/modu/un1_dout_mult_P33"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_P32" arg2="signal_process/modu/un1_dout_mult_P32"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_P31" arg2="signal_process/modu/un1_dout_mult_P31"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_P30" arg2="signal_process/modu/un1_dout_mult_P30"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_P29" arg2="signal_process/modu/un1_dout_mult_P29"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_P28" arg2="signal_process/modu/un1_dout_mult_P28"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_P27" arg2="signal_process/modu/un1_dout_mult_P27"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_P12" arg2="signal_process/modu/un1_dout_mult_P12"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_P11" arg2="signal_process/modu/un1_dout_mult_P11"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_P10" arg2="signal_process/modu/un1_dout_mult_P10"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_P9" arg2="signal_process/modu/un1_dout_mult_P9"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_P8" arg2="signal_process/modu/un1_dout_mult_P8"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_P7" arg2="signal_process/modu/un1_dout_mult_P7"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_P6" arg2="signal_process/modu/un1_dout_mult_P6"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_P5" arg2="signal_process/modu/un1_dout_mult_P5"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_P4" arg2="signal_process/modu/un1_dout_mult_P4"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_P3" arg2="signal_process/modu/un1_dout_mult_P3"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_P2" arg2="signal_process/modu/un1_dout_mult_P2"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_P1" arg2="signal_process/modu/un1_dout_mult_P1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_P0" arg2="signal_process/modu/un1_dout_mult_P0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_SROA17" arg2="signal_process/modu/un1_dout_mult_SROA17"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_SROA16" arg2="signal_process/modu/un1_dout_mult_SROA16"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_SROA15" arg2="signal_process/modu/un1_dout_mult_SROA15"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_SROA14" arg2="signal_process/modu/un1_dout_mult_SROA14"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_SROA13" arg2="signal_process/modu/un1_dout_mult_SROA13"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_SROA12" arg2="signal_process/modu/un1_dout_mult_SROA12"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_SROA11" arg2="signal_process/modu/un1_dout_mult_SROA11"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_SROA10" arg2="signal_process/modu/un1_dout_mult_SROA10"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_SROA9" arg2="signal_process/modu/un1_dout_mult_SROA9"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_SROA8" arg2="signal_process/modu/un1_dout_mult_SROA8"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_SROA7" arg2="signal_process/modu/un1_dout_mult_SROA7"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_SROA6" arg2="signal_process/modu/un1_dout_mult_SROA6"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_SROA5" arg2="signal_process/modu/un1_dout_mult_SROA5"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_SROA4" arg2="signal_process/modu/un1_dout_mult_SROA4"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_SROA3" arg2="signal_process/modu/un1_dout_mult_SROA3"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_SROA2" arg2="signal_process/modu/un1_dout_mult_SROA2"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_SROA1" arg2="signal_process/modu/un1_dout_mult_SROA1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_SROA0" arg2="signal_process/modu/un1_dout_mult_SROA0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_SROB17" arg2="signal_process/modu/un1_dout_mult_SROB17"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_SROB16" arg2="signal_process/modu/un1_dout_mult_SROB16"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_SROB15" arg2="signal_process/modu/un1_dout_mult_SROB15"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_SROB14" arg2="signal_process/modu/un1_dout_mult_SROB14"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_SROB13" arg2="signal_process/modu/un1_dout_mult_SROB13"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_SROB12" arg2="signal_process/modu/un1_dout_mult_SROB12"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_SROB11" arg2="signal_process/modu/un1_dout_mult_SROB11"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_SROB10" arg2="signal_process/modu/un1_dout_mult_SROB10"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_SROB9" arg2="signal_process/modu/un1_dout_mult_SROB9"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_SROB8" arg2="signal_process/modu/un1_dout_mult_SROB8"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_SROB7" arg2="signal_process/modu/un1_dout_mult_SROB7"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_SROB6" arg2="signal_process/modu/un1_dout_mult_SROB6"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_SROB5" arg2="signal_process/modu/un1_dout_mult_SROB5"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_SROB4" arg2="signal_process/modu/un1_dout_mult_SROB4"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_SROB3" arg2="signal_process/modu/un1_dout_mult_SROB3"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_SROB2" arg2="signal_process/modu/un1_dout_mult_SROB2"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_SROB1" arg2="signal_process/modu/un1_dout_mult_SROB1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_SROB0" arg2="signal_process/modu/un1_dout_mult_SROB0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROA17" arg2="signal_process/modu/un1_dout_mult_ROA17"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROA16" arg2="signal_process/modu/un1_dout_mult_ROA16"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROA15" arg2="signal_process/modu/un1_dout_mult_ROA15"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROA14" arg2="signal_process/modu/un1_dout_mult_ROA14"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROA13" arg2="signal_process/modu/un1_dout_mult_ROA13"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROA12" arg2="signal_process/modu/un1_dout_mult_ROA12"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROA11" arg2="signal_process/modu/un1_dout_mult_ROA11"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROA10" arg2="signal_process/modu/un1_dout_mult_ROA10"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROA9" arg2="signal_process/modu/un1_dout_mult_ROA9"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROA8" arg2="signal_process/modu/un1_dout_mult_ROA8"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROA7" arg2="signal_process/modu/un1_dout_mult_ROA7"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROA6" arg2="signal_process/modu/un1_dout_mult_ROA6"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROA5" arg2="signal_process/modu/un1_dout_mult_ROA5"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROA4" arg2="signal_process/modu/un1_dout_mult_ROA4"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROA3" arg2="signal_process/modu/un1_dout_mult_ROA3"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROA2" arg2="signal_process/modu/un1_dout_mult_ROA2"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROA1" arg2="signal_process/modu/un1_dout_mult_ROA1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROA0" arg2="signal_process/modu/un1_dout_mult_ROA0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROB17" arg2="signal_process/modu/un1_dout_mult_ROB17"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROB16" arg2="signal_process/modu/un1_dout_mult_ROB16"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROB15" arg2="signal_process/modu/un1_dout_mult_ROB15"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROB14" arg2="signal_process/modu/un1_dout_mult_ROB14"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROB13" arg2="signal_process/modu/un1_dout_mult_ROB13"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROB12" arg2="signal_process/modu/un1_dout_mult_ROB12"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROB11" arg2="signal_process/modu/un1_dout_mult_ROB11"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROB10" arg2="signal_process/modu/un1_dout_mult_ROB10"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROB9" arg2="signal_process/modu/un1_dout_mult_ROB9"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROB8" arg2="signal_process/modu/un1_dout_mult_ROB8"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROB7" arg2="signal_process/modu/un1_dout_mult_ROB7"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROB6" arg2="signal_process/modu/un1_dout_mult_ROB6"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROB5" arg2="signal_process/modu/un1_dout_mult_ROB5"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROB4" arg2="signal_process/modu/un1_dout_mult_ROB4"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROB3" arg2="signal_process/modu/un1_dout_mult_ROB3"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROB2" arg2="signal_process/modu/un1_dout_mult_ROB2"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROB1" arg2="signal_process/modu/un1_dout_mult_ROB1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROB0" arg2="signal_process/modu/un1_dout_mult_ROB0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROC17" arg2="signal_process/modu/un1_dout_mult_ROC17"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROC16" arg2="signal_process/modu/un1_dout_mult_ROC16"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROC15" arg2="signal_process/modu/un1_dout_mult_ROC15"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROC14" arg2="signal_process/modu/un1_dout_mult_ROC14"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROC13" arg2="signal_process/modu/un1_dout_mult_ROC13"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROC12" arg2="signal_process/modu/un1_dout_mult_ROC12"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROC11" arg2="signal_process/modu/un1_dout_mult_ROC11"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROC10" arg2="signal_process/modu/un1_dout_mult_ROC10"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROC9" arg2="signal_process/modu/un1_dout_mult_ROC9"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROC8" arg2="signal_process/modu/un1_dout_mult_ROC8"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROC7" arg2="signal_process/modu/un1_dout_mult_ROC7"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROC6" arg2="signal_process/modu/un1_dout_mult_ROC6"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROC5" arg2="signal_process/modu/un1_dout_mult_ROC5"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROC4" arg2="signal_process/modu/un1_dout_mult_ROC4"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROC3" arg2="signal_process/modu/un1_dout_mult_ROC3"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROC2" arg2="signal_process/modu/un1_dout_mult_ROC2"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROC1" arg2="signal_process/modu/un1_dout_mult_ROC1"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="signal_process/modu/un1_dout_mult_ROC0" arg2="signal_process/modu/un1_dout_mult_ROC0"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="RXD" arg2="RXD"  />
    <postMsg mid="1166052" type="Warning" dynamic="2" navigation="1" arg0="logical" arg1="RxTransmit" arg2="RxTransmit"  />
    <postMsg mid="1163101" type="Warning" dynamic="1" navigation="0" arg0="417"  />

Design Results:
   1655 blocks expanded
Complete the first expansion.
Writing 'INS350_5J_JZ_impl1.ngd' ...
Total CPU Time: 0 secs 
Total REAL Time: 0 secs 
Peak Memory Usage: 41 MB


map -a "ECP5U" -p LFE5U-25F -t CABGA256 -s 7 -oc Commercial   "INS350_5J_JZ_impl1.ngd" -o "INS350_5J_JZ_impl1_map.ncd" -pr "INS350_5J_JZ_impl1.prf" -mp "INS350_5J_JZ_impl1.mrp" -lpf "D:/Project/TLH50_03J_JZ_20250925/impl1/INS350_5J_JZ_impl1_synplify.lpf" -lpf "D:/Project/TLH50_03J_JZ_20250925/INS350_5J_JZ.lpf"             
map:  version Diamond (64-bit) 3.12.1.454

Copyright (c) 1991-1994 by NeoCAD Inc. All rights reserved.
Copyright (c) 1995 AT&T Corp.   All rights reserved.
Copyright (c) 1995-2001 Lucent Technologies Inc.  All rights reserved.
Copyright (c) 2001 Agere Systems   All rights reserved.
Copyright (c) 2002-2020 Lattice Semiconductor Corporation,  All rights reserved.
   Process the file: INS350_5J_JZ_impl1.ngd
   Picdevice="LFE5U-25F"

   Pictype="CABGA256"

   Picspeed=7

   Remove unused logic

   Do not produce over sized NCDs.

Part used: LFE5U-25FCABGA256, Performance used: 7.

Loading device for application baspr from file 'sa5p25.nph' in environment: D:/Software/lscc/diamond/3.12/ispfpga.
Package Status:                     Final          Version 1.42.

Running general design DRC...

Removing unused logic...

    <postMsg mid="52131473" type="Warning" dynamic="1" navigation="0" arg0="signal_process/rs422/un3_p_sum_add[0:53]"  />
Optimizing...

37 CCU2 constant inputs absorbed.

    <postMsg mid="51001030" type="Warning" dynamic="1" navigation="0" arg0="CLK120/locked_out"  />
    <postMsg mid="52131250" type="Warning" dynamic="1" navigation="0" arg0="signal_process/rs422/un3_p_sum_add[0:53]"  />
    <postMsg mid="52131253" type="Warning" dynamic="1" navigation="0" arg0="signal_process/rs422/un3_p_sum_add[0:53]"  />
    <postMsg mid="52131256" type="Warning" dynamic="1" navigation="0" arg0="signal_process/rs422/un3_p_sum_add[0:53]"  />
    <postMsg mid="52131342" type="Warning" dynamic="2" navigation="0" arg0="signal_process/rs422/un3_p_sum_add[0:53]" arg1="53"  />
    <postMsg mid="52131342" type="Warning" dynamic="2" navigation="0" arg0="signal_process/rs422/un3_p_sum_add[0:53]" arg1="52"  />
    <postMsg mid="52131342" type="Warning" dynamic="2" navigation="0" arg0="signal_process/rs422/un3_p_sum_add[0:53]" arg1="51"  />
    <postMsg mid="52131342" type="Warning" dynamic="2" navigation="0" arg0="signal_process/rs422/un3_p_sum_add[0:53]" arg1="50"  />
    <postMsg mid="52131342" type="Warning" dynamic="2" navigation="0" arg0="signal_process/rs422/un3_p_sum_add[0:53]" arg1="49"  />
    <postMsg mid="52131342" type="Warning" dynamic="2" navigation="0" arg0="signal_process/rs422/un3_p_sum_add[0:53]" arg1="48"  />
    <postMsg mid="52131342" type="Warning" dynamic="2" navigation="0" arg0="signal_process/rs422/un3_p_sum_add[0:53]" arg1="47"  />
    <postMsg mid="52131342" type="Warning" dynamic="2" navigation="0" arg0="signal_process/rs422/un3_p_sum_add[0:53]" arg1="46"  />
    <postMsg mid="52131342" type="Warning" dynamic="2" navigation="0" arg0="signal_process/rs422/un3_p_sum_add[0:53]" arg1="45"  />
    <postMsg mid="52131342" type="Warning" dynamic="2" navigation="0" arg0="signal_process/rs422/un3_p_sum_add[0:53]" arg1="44"  />
    <postMsg mid="52131342" type="Warning" dynamic="2" navigation="0" arg0="signal_process/rs422/un3_p_sum_add[0:53]" arg1="43"  />
    <postMsg mid="52131342" type="Warning" dynamic="2" navigation="0" arg0="signal_process/rs422/un3_p_sum_add[0:53]" arg1="42"  />
    <postMsg mid="52131342" type="Warning" dynamic="2" navigation="0" arg0="signal_process/rs422/un3_p_sum_add[0:53]" arg1="41"  />
    <postMsg mid="52131342" type="Warning" dynamic="2" navigation="0" arg0="signal_process/rs422/un3_p_sum_add[0:53]" arg1="40"  />
    <postMsg mid="52131342" type="Warning" dynamic="2" navigation="0" arg0="signal_process/rs422/un3_p_sum_add[0:53]" arg1="39"  />
    <postMsg mid="52131342" type="Warning" dynamic="2" navigation="0" arg0="signal_process/rs422/un3_p_sum_add[0:53]" arg1="38"  />
    <postMsg mid="52131342" type="Warning" dynamic="2" navigation="0" arg0="signal_process/rs422/un3_p_sum_add[0:53]" arg1="37"  />
    <postMsg mid="52131342" type="Warning" dynamic="2" navigation="0" arg0="signal_process/rs422/un3_p_sum_add[0:53]" arg1="36"  />
    <postMsg mid="51001046" type="Warning" dynamic="1" navigation="0" arg0="RXD"  />
    <postMsg mid="51001046" type="Warning" dynamic="1" navigation="0" arg0="RxTransmit"  />



Design Summary:
   Number of registers:   1044 out of 24879 (4%)
      PFU registers:         1016 out of 24288 (4%)
      PIO registers:           28 out of   591 (5%)
   Number of SLICEs:       697 out of 12144 (6%)
      SLICEs as Logic/ROM:    697 out of 12144 (6%)
      SLICEs as RAM:            0 out of  9108 (0%)
      SLICEs as Carry:        241 out of 12144 (2%)
   Number of LUT4s:        775 out of 24288 (3%)
      Number used as logic LUTs:        293
      Number used as distributed RAM:     0
      Number used as ripple logic:      482
      Number used as shift registers:     0
   Number of PIO sites used: 32 out of 197 (16%)
   Number of block RAMs:  2 out of 56 (4%)
   Number of GSRs:  1 out of 1 (100%)
   JTAG used :      No
   Readback used :  No
   Oscillator used :  No
   Startup used :   No
   DTR used :   No
   Number of Dynamic Bank Controller (BCINRD):  0 out of 4 (0%)
   Number of Dynamic Bank Controller (BCLVDSOB):  0 out of 4 (0%)
   Number of DCC:  0 out of 60 (0%)
   Number of DCS:  0 out of 2 (0%)
   Number of PLLs:  1 out of 2 (50%)
   Number of DDRDLLs:  0 out of 4 (0%)
   Number of CLKDIV:  0 out of 4 (0%)
   Number of ECLKSYNC:  0 out of 10 (0%)
   Number of ECLKBRIDGECS:  0 out of 2 (0%)
   Notes:-
      1. Total number of LUT4s = (Number of logic LUT4s) + 2*(Number of distributed RAMs) + 2*(Number of ripple logic)
      2. Number of logic LUT4s does not include count of distributed RAM and ripple logic.

   Number Of Mapped DSP Components:
   --------------------------------
   MULT18X18D          4
   MULT9X9D            0
   ALU54B              1
   ALU24B              0
   PRADD18A            0
   PRADD9A             0
   --------------------------------
   Number of Used DSP MULT Sites:  8 out of 56 (14 %)
   Number of Used DSP ALU Sites:  2 out of 28 (7 %)
   Number of Used DSP PRADD Sites:  0 out of 56 (0 %)
   Number of clocks:  4
     Net clk_in_c: 1 loads, 1 rising, 0 falling (Driver: PIO clk_in )
     Net clk120mhz: 446 loads, 446 rising, 0 falling (Driver: CLK120/PLLInst_0 )
     Net clk_AD: 101 loads, 101 rising, 0 falling (Driver: CLK120/PLLInst_0 )
     Net wendu.clk_us: 36 loads, 36 rising, 0 falling (Driver: wendu/clk_us )
   Number of Clock Enables:  29
     Net signal_process/trans/un1_clk_out6: 1 loads, 1 LSLICEs
     Net signal_process/rs422/trans_state[4]: 17 loads, 17 LSLICEs
     Net signal_process/rs422/N_80_i: 31 loads, 28 LSLICEs
     Net signal_process/rs422/p_sum13: 17 loads, 17 LSLICEs
     Net signal_process/polarity: 28 loads, 28 LSLICEs
     Net signal_process/modu/dout_mult13: 1 loads, 0 LSLICEs
     Net signal_process/modu/square10: 14 loads, 14 LSLICEs
     Net signal_process/modu/DA_dout3: 7 loads, 7 LSLICEs
     Net signal_process/integ/DA_dout5: 28 loads, 28 LSLICEs
     Net signal_process/demodu/N_410_i: 29 loads, 29 LSLICEs
     Net signal_process/demodu/fifo/wren_i: 6 loads, 4 LSLICEs
     Net signal_process/demodu/fifo/rden_i: 8 loads, 4 LSLICEs
     Net signal_process/demodu/fifo/fcnt_en: 4 loads, 4 LSLICEs
     Net signal_process/demodu/median_sum_n_1_sqmuxa: 28 loads, 28 LSLICEs
     Net signal_process/demodu/Latch_sum: 28 loads, 28 LSLICEs
     Net signal_process/demodu/N_417_i: 28 loads, 28 LSLICEs
     Net u_uart/U2/crc_data60: 4 loads, 4 LSLICEs
     Net u_uart/U2/N_52_i: 4 loads, 4 LSLICEs
     Net u_uart/tx_wr: 4 loads, 4 LSLICEs
     Net u_uart/Tx_done: 4 loads, 4 LSLICEs
     Net u_uart/U2/un3_temp_datady: 24 loads, 24 LSLICEs
     Net u_uart/U1/tx_en: 2 loads, 2 LSLICEs
     Net u_uart/U1/un1_tx_wr_1: 1 loads, 1 LSLICEs
     Net wendu/N_93_i: 2 loads, 2 LSLICEs
     Net wendu/N_167_i: 3 loads, 3 LSLICEs
     Net wendu/next_state_0_sqmuxa_1: 8 loads, 8 LSLICEs
     Net wendu/un1_cnt_us_0_sqmuxa_i: 1 loads, 1 LSLICEs
     Net wendu/N_117: 1 loads, 1 LSLICEs
     Net wendu.data_temp_1_sqmuxa: 9 loads, 8 LSLICEs
   Number of local set/reset loads for net CLK120/locked_out merged into GSR:  270
   Number of LSRs:  10
     Net signal_process/rs422/trans_state[5]: 31 loads, 28 LSLICEs
     Net signal_process/demodu/AD_validcnt7: 33 loads, 33 LSLICEs
     Net signal_process/demodu/un1_AD_validcntlto7_i_o4: 2 loads, 2 LSLICEs
     Net signal_process/demodu/un1_AD_validcnt_1[0]: 1 loads, 1 LSLICEs
     Net signal_process/ctrl_signal/fb: 1 loads, 1 LSLICEs
     Net signal_process/ctrl_signal/fb_0: 1 loads, 1 LSLICEs
     Net signal_process/ctrl_signal/fb_1: 1 loads, 1 LSLICEs
     Net signal_process/ctrl_signal/fb_2: 1 loads, 1 LSLICEs
     Net signal_process/ctrl_signal/fb_3: 1 loads, 1 LSLICEs
     Net CLK120/locked_out: 2 loads, 0 LSLICEs
   Number of nets driven by tri-state buffers:  0
   Top 10 highest fanout non-clock nets:
     Net signal_process/polarity: 35 loads
     Net signal_process/demodu/AD_validcnt7: 34 loads
     Net signal_process/rs422/trans_state[5]: 33 loads
     Net signal_process/rs422/N_80_i: 31 loads
     Net signal_process/demodu/N_410_i: 29 loads
     Net signal_process/demodu/Latch_sum: 28 loads
     Net signal_process/demodu/median_sum_n_1_sqmuxa: 28 loads
     Net signal_process/demodu/N_417_i: 28 loads
     Net signal_process/integ/DA_dout5: 28 loads
     Net u_uart/U2/un3_temp_datady: 24 loads
    <postMsg mid="1103694" type="Warning" dynamic="2" navigation="0" arg0="Semantic error in &quot;LOCATE COMP &quot;wendu/SLICE_241&quot; SITE &quot;CLKOS3&quot; ;&quot;: " arg1="CLKOS3"  />
 

   Number of warnings:  26
   Number of errors:    0


. ALU54B  signal_process/rs422/un3_p_sum_add[0:53]:

54-Bit ALU
	Opcode  0		1
	Opcode  1		0
	Opcode  2		0
	Opcode  3		0
	Opcode  4		0
	Opcode  5		0
	Opcode  6		0
	Opcode  7		0
	Opcode  8		0
	Opcode  9		1
	Opcode 10		0

	OpcodeOP0 Registers	CLK	CE	RST
	--------------------------------------------
		Input		    	   	    
		Pipeline	    	   	    

	OpcodeOP1 Registers	CLK	CE	RST
	--------------------------------------------
		Input		    	--	--
		Pipeline	    	--	--

	OpcodeIN Registers	CLK	CE	RST
	--------------------------------------------
		Input		    	   	    
		Pipeline	    	   	    
Data
	Input Registers		CLK	CE	RST
	--------------------------------------------
		C0		    	   	    
		C1		    	   	    
		CFB		    	   	    

	Output Register		CLK	CE	RST
	--------------------------------------------
		Output0		    	   	    
		Output1		    	   	    

	Flag Register		CLK	CE	RST
	--------------------------------------------
		Flag		    	   	    
Other
	MCPAT_SOURCE	STATIC
	MASKPAT_SOURCE	STATIC
	MASK01		0x00000000000000
	MCPAT		0x00000000000000
	MASKPAT		0x00000000000000
	RNDPAT		0x00000000000000
	PSE17		0b00000000000000000
	PSE44		0b00000000000000000000000000
	PSE53		0b00000000
	GSR		DISABLED
	RESETMODE	ASYNC
	MULT9_MODE	DISABLED

	LEGACY  	DISABLED

	CLK0_DIV	ENABLED
	CLK1_DIV	ENABLED
	CLK2_DIV	ENABLED
	CLK3_DIV	ENABLED

. MULT18X18D  signal_process/rs422/un3_p_sum[0:35]:

Multiplier
	Operation A		Unsigned
	Operation A Registers	CLK	CE	RST
	--------------------------------------------
		Input		CLK0	CE0	RST0
		Pipeline	    	   	    

	Operation B		Unsigned
	Operation B Registers	CLK	CE	RST
	--------------------------------------------
		Input		    	   	    
		Pipeline	    	   	    
Data
	Input Registers		CLK	CE	RST	Source
	-------------------------------------------------------
		A		CLK0	CE0	RST0	A Data In
		B		    	   	    	B Data In
		C		    	   	    	N/A
	Cascaded Match

	Pipeline Registers	CLK	CE	RST
	--------------------------------------------
		Pipe		    	   	    

	Output Register		CLK	CE	RST
	--------------------------------------------
		Output		    	   	    
Other
	SOURCEB_MODE	B_SHIFT
	MULT_BYPASS	DISABLED
	CAS_MATCH_REG	FALSE
	GSR		DISABLED
	RESETMODE	SYNC
	CLK0_DIV	ENABLED
	CLK1_DIV	ENABLED
	CLK2_DIV	ENABLED
	CLK3_DIV	ENABLED
	HIGHSPEED_CLK	NONE
	PSE17		0b00000000000000000
	PSE35		0b00000000000000000

	C_PSE17		0b00000000000000000

. MULT18X18D  signal_process/rs422/un3_p_sum[18:53]:

Multiplier
	Operation A		Unsigned
	Operation A Registers	CLK	CE	RST
	--------------------------------------------
		Input		CLK0	CE0	RST0
		Pipeline	    	   	    

	Operation B		Unsigned
	Operation B Registers	CLK	CE	RST
	--------------------------------------------
		Input		    	   	    
		Pipeline	    	   	    
Data
	Input Registers		CLK	CE	RST	Source
	-------------------------------------------------------
		A		CLK0	CE0	RST0	A Data In
		B		    	   	    	B Data In
		C		    	   	    	N/A
	Cascaded Match

	Pipeline Registers	CLK	CE	RST
	--------------------------------------------
		Pipe		    	   	    

	Output Register		CLK	CE	RST
	--------------------------------------------
		Output		    	   	    
Other
	SOURCEB_MODE	B_SHIFT
	MULT_BYPASS	DISABLED
	CAS_MATCH_REG	FALSE
	GSR		DISABLED
	RESETMODE	SYNC
	CLK0_DIV	ENABLED
	CLK1_DIV	ENABLED
	CLK2_DIV	ENABLED
	CLK3_DIV	ENABLED
	HIGHSPEED_CLK	NONE
	PSE17		0b00000000000000000
	PSE35		0b00000000000000000

	C_PSE17		0b00000000000000000

. MULT18X18D  signal_process/rs422/un3_p_sum[36:71]:

Multiplier
	Operation A		Unsigned
	Operation A Registers	CLK	CE	RST
	--------------------------------------------
		Input		CLK0	CE0	RST0
		Pipeline	    	   	    

	Operation B		Unsigned
	Operation B Registers	CLK	CE	RST
	--------------------------------------------
		Input		    	   	    
		Pipeline	    	   	    
Data
	Input Registers		CLK	CE	RST	Source
	-------------------------------------------------------
		A		CLK0	CE0	RST0	A Data In
		B		    	   	    	B Data In
		C		    	   	    	N/A
	Cascaded Match

	Pipeline Registers	CLK	CE	RST
	--------------------------------------------
		Pipe		    	   	    

	Output Register		CLK	CE	RST
	--------------------------------------------
		Output		    	   	    
Other
	SOURCEB_MODE	B_SHIFT
	MULT_BYPASS	DISABLED
	CAS_MATCH_REG	FALSE
	GSR		DISABLED
	RESETMODE	SYNC
	CLK0_DIV	ENABLED
	CLK1_DIV	ENABLED
	CLK2_DIV	ENABLED
	CLK3_DIV	ENABLED
	HIGHSPEED_CLK	NONE
	PSE17		0b00000000000000000
	PSE35		0b00000000000000000

	C_PSE17		0b00000000000000000

. MULT18X18D  signal_process/modu/un1_dout_mult[26:0]:

Multiplier
	Operation A		Unsigned
	Operation A Registers	CLK	CE	RST
	--------------------------------------------
		Input		CLK0	CE0	RST0
		Pipeline	CLK0	CE1	RST0

	Operation B		Unsigned
	Operation B Registers	CLK	CE	RST
	--------------------------------------------
		Input		    	   	    
		Pipeline	CLK0	CE1	RST0
Data
	Input Registers		CLK	CE	RST	Source
	-------------------------------------------------------
		A		CLK0	CE0	RST0	A Data In
		B		    	   	    	B Data In
		C		    	   	    	N/A
	Cascaded Match

	Pipeline Registers	CLK	CE	RST
	--------------------------------------------
		Pipe		CLK0	CE1	RST0

	Output Register		CLK	CE	RST
	--------------------------------------------
		Output		CLK0	CE0	RST0
Other
	SOURCEB_MODE	B_SHIFT
	MULT_BYPASS	DISABLED
	CAS_MATCH_REG	FALSE
	GSR		DISABLED
	RESETMODE	ASYNC
	CLK0_DIV	ENABLED
	CLK1_DIV	ENABLED
	CLK2_DIV	ENABLED
	CLK3_DIV	ENABLED
	HIGHSPEED_CLK	NONE
	PSE17		0b00000000000000000
	PSE35		0b00000000000000000

	C_PSE17		0b00000000000000000


Total CPU Time: 0 secs  
Total REAL Time: 0 secs  
Peak Memory Usage: 135 MB

Dumping design to file INS350_5J_JZ_impl1_map.ncd.

trce -f "INS350_5J_JZ_impl1.mt" -o "INS350_5J_JZ_impl1.tw1" "INS350_5J_JZ_impl1_map.ncd" "INS350_5J_JZ_impl1.prf"
trce:  version Diamond (64-bit) 3.12.1.454

Copyright (c) 1991-1994 by NeoCAD Inc. All rights reserved.
Copyright (c) 1995 AT&T Corp.   All rights reserved.
Copyright (c) 1995-2001 Lucent Technologies Inc.  All rights reserved.
Copyright (c) 2001 Agere Systems   All rights reserved.
Copyright (c) 2002-2020 Lattice Semiconductor Corporation,  All rights reserved.

Loading design for application trce from file ins350_5j_jz_impl1_map.ncd.
Design name: INS350_5J_JZ
NCD version: 3.3
Vendor:      LATTICE
Device:      LFE5U-25F
Package:     CABGA256
Performance: 7
Loading device for application trce from file 'sa5p25.nph' in environment: D:/Software/lscc/diamond/3.12/ispfpga.
Package Status:                     Final          Version 1.42.
Performance Hardware Data Status:   Final          Version 55.1.
Setup and Hold Report

--------------------------------------------------------------------------------
Lattice TRACE Report - Setup, Version Diamond (64-bit) 3.12.1.454
Thu Sep 25 10:29:41 2025

Copyright (c) 1991-1994 by NeoCAD Inc. All rights reserved.
Copyright (c) 1995 AT&T Corp.   All rights reserved.
Copyright (c) 1995-2001 Lucent Technologies Inc.  All rights reserved.
Copyright (c) 2001 Agere Systems   All rights reserved.
Copyright (c) 2002-2020 Lattice Semiconductor Corporation,  All rights reserved.

Report Information
------------------
Command line:    trce -v 1 -gt -mapchkpnt 0 -sethld -o INS350_5J_JZ_impl1.tw1 -gui -msgset D:/Project/TLH50_03J_JZ_20250925/promote.xml INS350_5J_JZ_impl1_map.ncd INS350_5J_JZ_impl1.prf 
Design file:     ins350_5j_jz_impl1_map.ncd
Preference file: ins350_5j_jz_impl1.prf
Device,speed:    LFE5U-25F,7
Report level:    verbose report, limited to 1 item per preference
--------------------------------------------------------------------------------

BLOCK ASYNCPATHS
BLOCK RESETPATHS
BLOCK JTAG PATHS
--------------------------------------------------------------------------------


Derating parameters
-------------------
VCCIO Voltage:
                   3.300 V (Bank 0)
                   3.300 V (Bank 1, defined by PAR)
                   3.300 V (Bank 2)
                   3.300 V (Bank 3, defined by PAR)
                   3.300 V (Bank 6, defined by PAR)
                   3.300 V (Bank 7, defined by PAR)



Timing summary (Setup):
---------------

Timing errors: 0  Score: 0
Cumulative negative slack: 0

Constraints cover 38661 paths, 6 nets, and 3789 connections (90.19% coverage)

--------------------------------------------------------------------------------
Lattice TRACE Report - Hold, Version Diamond (64-bit) 3.12.1.454
Thu Sep 25 10:29:41 2025

Copyright (c) 1991-1994 by NeoCAD Inc. All rights reserved.
Copyright (c) 1995 AT&T Corp.   All rights reserved.
Copyright (c) 1995-2001 Lucent Technologies Inc.  All rights reserved.
Copyright (c) 2001 Agere Systems   All rights reserved.
Copyright (c) 2002-2020 Lattice Semiconductor Corporation,  All rights reserved.

Report Information
------------------
Command line:    trce -v 1 -gt -mapchkpnt 0 -sethld -o INS350_5J_JZ_impl1.tw1 -gui -msgset D:/Project/TLH50_03J_JZ_20250925/promote.xml INS350_5J_JZ_impl1_map.ncd INS350_5J_JZ_impl1.prf 
Design file:     ins350_5j_jz_impl1_map.ncd
Preference file: ins350_5j_jz_impl1.prf
Device,speed:    LFE5U-25F,M
Report level:    verbose report, limited to 1 item per preference
--------------------------------------------------------------------------------

BLOCK ASYNCPATHS
BLOCK RESETPATHS
BLOCK JTAG PATHS
--------------------------------------------------------------------------------


Derating parameters
-------------------
VCCIO Voltage:
                   3.300 V (Bank 0)
                   3.300 V (Bank 1, defined by PAR)
                   3.300 V (Bank 2)
                   3.300 V (Bank 3, defined by PAR)
                   3.300 V (Bank 6, defined by PAR)
                   3.300 V (Bank 7, defined by PAR)



Timing summary (Hold):
---------------

Timing errors: 0  Score: 0
Cumulative negative slack: 0

Constraints cover 38661 paths, 6 nets, and 4167 connections (99.19% coverage)



Timing summary (Setup and Hold):
---------------

Timing errors: 0 (setup), 0 (hold)
Score: 0 (setup), 0 (hold)
Cumulative negative slack: 0 (0+0)
--------------------------------------------------------------------------------

--------------------------------------------------------------------------------

Total CPU Time: 1 secs 
Total REAL Time: 2 secs 
Peak Memory Usage: 189 MB


mpartrce -p "INS350_5J_JZ_impl1.p2t" -f "INS350_5J_JZ_impl1.p3t" -tf "INS350_5J_JZ_impl1.pt" "INS350_5J_JZ_impl1_map.ncd" "INS350_5J_JZ_impl1.ncd"

---- MParTrce Tool ----
Removing old design directory at request of -rem command line option to this program.
Running par. Please wait . . .

Lattice Place and Route Report for Design "INS350_5J_JZ_impl1_map.ncd"
Thu Sep 25 10:29:42 2025

PAR: Place And Route Diamond (64-bit) 3.12.1.454.
Command Line: par -w -l 5 -i 6 -t 1 -c 0 -e 0 -gui -msgset D:/Project/TLH50_03J_JZ_20250925/promote.xml -exp parUseNBR=1:parCDP=auto:parCDR=1:parPathBased=OFF:parASE=1 INS350_5J_JZ_impl1_map.ncd INS350_5J_JZ_impl1.dir/5_1.ncd INS350_5J_JZ_impl1.prf
Preference file: INS350_5J_JZ_impl1.prf.
Placement level-cost: 5-1.
Routing Iterations: 6

Loading design for application par from file INS350_5J_JZ_impl1_map.ncd.
Design name: INS350_5J_JZ
NCD version: 3.3
Vendor:      LATTICE
Device:      LFE5U-25F
Package:     CABGA256
Performance: 7
Loading device for application par from file 'sa5p25.nph' in environment: D:/Software/lscc/diamond/3.12/ispfpga.
Package Status:                     Final          Version 1.42.
Performance Hardware Data Status:   Final          Version 55.1.
License checked out.


Ignore Preference Error(s):  True
Device utilization summary:

   PIO (prelim)      32/197          16% used
                     32/197          16% bonded
   IOLOGIC           28/199          14% used

   SLICE            697/12144         5% used

   GSR                1/1           100% used
   EBR                2/56            3% used
   PLL                1/2            50% used
   MULT18             4/28           14% used
   ALU54              1/14            7% used


Number of Signals: 2184
Number of Connections: 4201

Pin Constraint Summary:
   32 out of 32 pins locked (100% locked).


The following 12 signals are selected to use the primary clock routing resources:
    clk120mhz (driver: CLK120/PLLInst_0, clk/ce/sr load #: 446/0/0)
    clk_AD (driver: CLK120/PLLInst_0, clk/ce/sr load #: 101/0/0)
    wendu.clk_us (driver: wendu/SLICE_241, clk/ce/sr load #: 36/0/0)
    signal_process/demodu/AD_validcnt7 (driver: signal_process/demodu/SLICE_285, clk/ce/sr load #: 0/0/33)
    signal_process/rs422/trans_state[5] (driver: signal_process/rs422/SLICE_482, clk/ce/sr load #: 0/0/31)
    signal_process/rs422/N_80_i (driver: signal_process/rs422/SLICE_627, clk/ce/sr load #: 0/31/0)
    signal_process/demodu/N_410_i (driver: signal_process/demodu/SLICE_594, clk/ce/sr load #: 0/29/0)
    signal_process/polarity (driver: signal_process/ctrl_signal/SLICE_405, clk/ce/sr load #: 0/28/0)
    signal_process/integ/DA_dout5 (driver: signal_process/integ/SLICE_641, clk/ce/sr load #: 0/28/0)
    signal_process/demodu/median_sum_n_1_sqmuxa (driver: signal_process/demodu/SLICE_624, clk/ce/sr load #: 0/28/0)
    signal_process/demodu/Latch_sum (driver: signal_process/demodu/SLICE_287, clk/ce/sr load #: 0/28/0)
    signal_process/demodu/N_417_i (driver: signal_process/demodu/SLICE_624, clk/ce/sr load #: 0/28/0)


Signal CLK120/locked_out is selected as Global Set/Reset.
Starting Placer Phase 0.
..............
Finished Placer Phase 0.  REAL time: 3 secs 


Starting Placer Phase 1.
......................
Placer score = 383024.
Finished Placer Phase 1.  REAL time: 9 secs 

Starting Placer Phase 2.
.
Placer score =  319973
Finished Placer Phase 2.  REAL time: 9 secs 


------------------ Clock Report ------------------

Global Clock Resources:
  CLK_PIN    : 0 out of 12 (0%)
  GR_PCLK    : 0 out of 12 (0%)
  PLL        : 1 out of 2 (50%)
  DCS        : 0 out of 2 (0%)
  DCC        : 0 out of 60 (0%)
  CLKDIV     : 0 out of 4 (0%)

Quadrant TL Clocks:
  PRIMARY "clk120mhz" from CLKOP on comp "CLK120/PLLInst_0" on PLL site "PLL_BR0", CLK/CE/SR load = 193
  PRIMARY "clk_AD" from CLKOS on comp "CLK120/PLLInst_0" on PLL site "PLL_BR0", CLK/CE/SR load = 8
  PRIMARY "signal_process/demodu/AD_validcnt7" from F0 on comp "signal_process/demodu/SLICE_285" on site "R22C23C", CLK/CE/SR load = 4
  PRIMARY "signal_process/rs422/trans_state[5]" from Q0 on comp "signal_process/rs422/SLICE_482" on site "R23C31D", CLK/CE/SR load = 31
  PRIMARY "signal_process/rs422/N_80_i" from F1 on comp "signal_process/rs422/SLICE_627" on site "R18C33D", CLK/CE/SR load = 31
  PRIMARY "signal_process/polarity" from Q0 on comp "signal_process/ctrl_signal/SLICE_405" on site "R19C21A", CLK/CE/SR load = 27

  PRIMARY  : 6 out of 16 (37%)

Quadrant TR Clocks:
  PRIMARY "clk120mhz" from CLKOP on comp "CLK120/PLLInst_0" on PLL site "PLL_BR0", CLK/CE/SR load = 33

  PRIMARY  : 1 out of 16 (6%)

Quadrant BL Clocks:
  PRIMARY "clk120mhz" from CLKOP on comp "CLK120/PLLInst_0" on PLL site "PLL_BR0", CLK/CE/SR load = 151
  PRIMARY "clk_AD" from CLKOS on comp "CLK120/PLLInst_0" on PLL site "PLL_BR0", CLK/CE/SR load = 93
  PRIMARY "wendu.clk_us" from Q0 on comp "wendu/SLICE_241" on site "R38C31A", CLK/CE/SR load = 19
  PRIMARY "signal_process/demodu/AD_validcnt7" from F0 on comp "signal_process/demodu/SLICE_285" on site "R22C23C", CLK/CE/SR load = 29
  PRIMARY "signal_process/demodu/N_410_i" from F0 on comp "signal_process/demodu/SLICE_594" on site "R23C24C", CLK/CE/SR load = 29
  PRIMARY "signal_process/polarity" from Q0 on comp "signal_process/ctrl_signal/SLICE_405" on site "R19C21A", CLK/CE/SR load = 1
  PRIMARY "signal_process/integ/DA_dout5" from F0 on comp "signal_process/integ/SLICE_641" on site "R17C21A", CLK/CE/SR load = 28
  PRIMARY "signal_process/demodu/median_sum_n_1_sqmuxa" from F1 on comp "signal_process/demodu/SLICE_624" on site "R19C20B", CLK/CE/SR load = 28
  PRIMARY "signal_process/demodu/Latch_sum" from Q0 on comp "signal_process/demodu/SLICE_287" on site "R23C24A", CLK/CE/SR load = 28
  PRIMARY "signal_process/demodu/N_417_i" from F0 on comp "signal_process/demodu/SLICE_624" on site "R19C20B", CLK/CE/SR load = 28

  PRIMARY  : 10 out of 16 (62%)

Quadrant BR Clocks:
  PRIMARY "clk120mhz" from CLKOP on comp "CLK120/PLLInst_0" on PLL site "PLL_BR0", CLK/CE/SR load = 69
  PRIMARY "wendu.clk_us" from Q0 on comp "wendu/SLICE_241" on site "R38C31A", CLK/CE/SR load = 17

  PRIMARY  : 2 out of 16 (12%)

Edge Clocks:

  No edge clock selected.


--------------- End of Clock Report ---------------


+
I/O Usage Summary (final):
   32 out of 197 (16.2%) PIO sites used.
   32 out of 197 (16.2%) bonded PIO sites used.
   Number of PIO comps: 32; differential: 0.
   Number of Vref pins used: 0.

I/O Bank Usage Summary:
+----------+----------------+------------+------------+------------+
| I/O Bank | Usage          | Bank Vccio | Bank Vref1 | Bank Vref2 |
+----------+----------------+------------+------------+------------+
| 0        | 0 / 24 (  0%)  | 3.3V       | -          | -          |
| 1        | 1 / 32 (  3%)  | 3.3V       | -          | -          |
| 2        | 0 / 32 (  0%)  | 3.3V       | -          | -          |
| 3        | 3 / 32 (  9%)  | 3.3V       | -          | -          |
| 6        | 13 / 32 ( 40%) | 3.3V       | -          | -          |
| 7        | 15 / 32 ( 46%) | 3.3V       | -          | -          |
| 8        | 0 / 13 (  0%)  | 3.3V       | -          | -          |
+----------+----------------+------------+------------+------------+

---------------------------------- DSP Report ----------------------------------

DSP Slice #:           1  2  3  4  5  6  7  8  9 10 11 12 13 14
# of MULT9                                                     
# of MULT18               1  2  1                              
# of ALU24                                                     
# of ALU54                   1                                 
# of PRADD9                                                    
# of PRADD18                                                   

DSP Slice  2         Component_Type       Physical_Type                    Instance_Name                  
  MULT18_R13C9         MULT18X18D             MULT18          signal_process/modu/un1_dout_mult[26:0]     

DSP Slice  3         Component_Type       Physical_Type                    Instance_Name                  
 MULT18_R13C13         MULT18X18D             MULT18            signal_process/rs422/un3_p_sum[0:35]      
 MULT18_R13C14         MULT18X18D             MULT18           signal_process/rs422/un3_p_sum[18:53]      
  ALU54_R13C16           ALU54B               ALU54           signal_process/rs422/un3_p_sum_add[0:53]    

DSP Slice  4         Component_Type       Physical_Type                    Instance_Name                  
 MULT18_R13C17         MULT18X18D             MULT18           signal_process/rs422/un3_p_sum[36:71]      

------------------------------ End of DSP Report -------------------------------
Total placer CPU time: 8 secs 

Dumping design to file INS350_5J_JZ_impl1.dir/5_1.ncd.

0 connections routed; 4201 unrouted.
Starting router resource preassignment
DSP info: No dsp pins have been swapped.

Completed router resource preassignment. Real time: 13 secs 

Start NBR router at 10:29:56 09/25/25

*****************************************************************
Info: NBR allows conflicts(one node used by more than one signal)
      in the earlier iterations. In each iteration, it tries to  
      solve the conflicts while keeping the critical connections 
      routed as short as possible. The routing process is said to
      be completed when no conflicts exist and all connections   
      are routed.                                                
Note: NBR uses a different method to calculate timing slacks. The
      worst slack and total negative slack may not be the same as
      that in TRCE report. You should always run TRCE to verify  
      your design.                                               
*****************************************************************

Start NBR special constraint process at 10:29:56 09/25/25

Start NBR section for initial routing at 10:29:56 09/25/25
Level 1, iteration 1
0(0.00%) conflict; 2539(60.44%) untouched conns; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.127ns/0.000ns; real time: 15 secs 
Level 2, iteration 1
5(0.00%) conflicts; 2516(59.89%) untouched conns; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 15 secs 
Level 3, iteration 1
18(0.00%) conflicts; 2233(53.15%) untouched conns; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 15 secs 
Level 4, iteration 1
147(0.01%) conflicts; 0(0.00%) untouched conn; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 16 secs 

Info: Initial congestion level at 75% usage is 0
Info: Initial congestion area  at 75% usage is 0 (0.00%)

Start NBR section for normal routing at 10:29:58 09/25/25
Level 1, iteration 1
5(0.00%) conflicts; 160(3.81%) untouched conns; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 16 secs 
Level 1, iteration 2
5(0.00%) conflicts; 160(3.81%) untouched conns; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 16 secs 
Level 2, iteration 1
5(0.00%) conflicts; 160(3.81%) untouched conns; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 16 secs 
Level 3, iteration 1
5(0.00%) conflicts; 156(3.71%) untouched conns; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 16 secs 
Level 4, iteration 1
72(0.01%) conflicts; 0(0.00%) untouched conn; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 16 secs 
Level 4, iteration 2
31(0.00%) conflicts; 0(0.00%) untouched conn; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 16 secs 
Level 4, iteration 3
15(0.00%) conflicts; 0(0.00%) untouched conn; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 16 secs 
Level 4, iteration 4
7(0.00%) conflicts; 0(0.00%) untouched conn; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 16 secs 
Level 4, iteration 5
4(0.00%) conflicts; 0(0.00%) untouched conn; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 17 secs 
Level 4, iteration 6
2(0.00%) conflicts; 0(0.00%) untouched conn; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 17 secs 
Level 4, iteration 7
0(0.00%) conflict; 0(0.00%) untouched conn; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 17 secs 

Start NBR section for setup/hold timing optimization with effort level 3 at 10:29:59 09/25/25

Start NBR section for re-routing at 10:30:00 09/25/25
Level 4, iteration 1
0(0.00%) conflict; 0(0.00%) untouched conn; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 18 secs 

Start NBR section for post-routing at 10:30:00 09/25/25

End NBR router with 0 unrouted connection

NBR Summary
-----------
  Number of unrouted connections : 0 (0.00%)
  Number of connections with timing violations : 0 (0.00%)
  Estimated worst slack<setup> : 1.227ns
  Timing score<setup> : 0
-----------
Notes: The timing info is calculated for SETUP only and all PAR_ADJs are ignored.



Total CPU time 21 secs 
Total REAL time: 22 secs 
Completely routed.
End of route.  4201 routed (100.00%); 0 unrouted.

Hold time timing score: 0, hold timing errors: 0

Timing score: 0 

Dumping design to file INS350_5J_JZ_impl1.dir/5_1.ncd.


PAR_SUMMARY::Run status = Completed
PAR_SUMMARY::Number of unrouted conns = 0
PAR_SUMMARY::Worst  slack<setup/<ns>> = 1.227
PAR_SUMMARY::Timing score<setup/<ns>> = 0.000
PAR_SUMMARY::Worst  slack<hold /<ns>> = 0.174
PAR_SUMMARY::Timing score<hold /<ns>> = 0.000
PAR_SUMMARY::Number of errors = 0

Total CPU  time to completion: 21 secs 
Total REAL time to completion: 22 secs 

par done!

Note: user must run 'Trace' for timing closure signoff.

Copyright (c) 1991-1994 by NeoCAD Inc. All rights reserved.
Copyright (c) 1995 AT&T Corp.   All rights reserved.
Copyright (c) 1995-2001 Lucent Technologies Inc.  All rights reserved.
Copyright (c) 2001 Agere Systems   All rights reserved.
Copyright (c) 2002-2020 Lattice Semiconductor Corporation,  All rights reserved.
Exiting par with exit code 0
Exiting mpartrce with exit code 0

trce -f "INS350_5J_JZ_impl1.pt" -o "INS350_5J_JZ_impl1.twr" "INS350_5J_JZ_impl1.ncd" "INS350_5J_JZ_impl1.prf"
trce:  version Diamond (64-bit) 3.12.1.454

Copyright (c) 1991-1994 by NeoCAD Inc. All rights reserved.
Copyright (c) 1995 AT&T Corp.   All rights reserved.
Copyright (c) 1995-2001 Lucent Technologies Inc.  All rights reserved.
Copyright (c) 2001 Agere Systems   All rights reserved.
Copyright (c) 2002-2020 Lattice Semiconductor Corporation,  All rights reserved.

Loading design for application trce from file ins350_5j_jz_impl1.ncd.
Design name: INS350_5J_JZ
NCD version: 3.3
Vendor:      LATTICE
Device:      LFE5U-25F
Package:     CABGA256
Performance: 7
Loading device for application trce from file 'sa5p25.nph' in environment: D:/Software/lscc/diamond/3.12/ispfpga.
Package Status:                     Final          Version 1.42.
Performance Hardware Data Status:   Final          Version 55.1.
Setup and Hold Report

--------------------------------------------------------------------------------
Lattice TRACE Report - Setup, Version Diamond (64-bit) 3.12.1.454
Thu Sep 25 10:30:06 2025

Copyright (c) 1991-1994 by NeoCAD Inc. All rights reserved.
Copyright (c) 1995 AT&T Corp.   All rights reserved.
Copyright (c) 1995-2001 Lucent Technologies Inc.  All rights reserved.
Copyright (c) 2001 Agere Systems   All rights reserved.
Copyright (c) 2002-2020 Lattice Semiconductor Corporation,  All rights reserved.

Report Information
------------------
Command line:    trce -v 10 -gt -sethld -sp 7 -sphld m -o INS350_5J_JZ_impl1.twr -gui -msgset D:/Project/TLH50_03J_JZ_20250925/promote.xml INS350_5J_JZ_impl1.ncd INS350_5J_JZ_impl1.prf 
Design file:     ins350_5j_jz_impl1.ncd
Preference file: ins350_5j_jz_impl1.prf
Device,speed:    LFE5U-25F,7
Report level:    verbose report, limited to 10 items per preference
--------------------------------------------------------------------------------

BLOCK ASYNCPATHS
BLOCK RESETPATHS
BLOCK JTAG PATHS
--------------------------------------------------------------------------------


Derating parameters
-------------------
VCCIO Voltage:
                   3.300 V (Bank 0)
                   3.300 V (Bank 1, defined by PAR)
                   3.300 V (Bank 2)
                   3.300 V (Bank 3, defined by PAR)
                   3.300 V (Bank 6, defined by PAR)
                   3.300 V (Bank 7, defined by PAR)



Timing summary (Setup):
---------------

Timing errors: 0  Score: 0
Cumulative negative slack: 0

Constraints cover 38661 paths, 6 nets, and 4168 connections (99.21% coverage)

--------------------------------------------------------------------------------
Lattice TRACE Report - Hold, Version Diamond (64-bit) 3.12.1.454
Thu Sep 25 10:30:06 2025

Copyright (c) 1991-1994 by NeoCAD Inc. All rights reserved.
Copyright (c) 1995 AT&T Corp.   All rights reserved.
Copyright (c) 1995-2001 Lucent Technologies Inc.  All rights reserved.
Copyright (c) 2001 Agere Systems   All rights reserved.
Copyright (c) 2002-2020 Lattice Semiconductor Corporation,  All rights reserved.

Report Information
------------------
Command line:    trce -v 10 -gt -sethld -sp 7 -sphld m -o INS350_5J_JZ_impl1.twr -gui -msgset D:/Project/TLH50_03J_JZ_20250925/promote.xml INS350_5J_JZ_impl1.ncd INS350_5J_JZ_impl1.prf 
Design file:     ins350_5j_jz_impl1.ncd
Preference file: ins350_5j_jz_impl1.prf
Device,speed:    LFE5U-25F,m
Report level:    verbose report, limited to 10 items per preference
--------------------------------------------------------------------------------

BLOCK ASYNCPATHS
BLOCK RESETPATHS
BLOCK JTAG PATHS
--------------------------------------------------------------------------------


Derating parameters
-------------------
VCCIO Voltage:
                   3.300 V (Bank 0)
                   3.300 V (Bank 1, defined by PAR)
                   3.300 V (Bank 2)
                   3.300 V (Bank 3, defined by PAR)
                   3.300 V (Bank 6, defined by PAR)
                   3.300 V (Bank 7, defined by PAR)



Timing summary (Hold):
---------------

Timing errors: 0  Score: 0
Cumulative negative slack: 0

Constraints cover 38661 paths, 6 nets, and 4168 connections (99.21% coverage)



Timing summary (Setup and Hold):
---------------

Timing errors: 0 (setup), 0 (hold)
Score: 0 (setup), 0 (hold)
Cumulative negative slack: 0 (0+0)
--------------------------------------------------------------------------------

--------------------------------------------------------------------------------

Total CPU Time: 2 secs 
Total REAL Time: 2 secs 
Peak Memory Usage: 190 MB


tmcheck -par "INS350_5J_JZ_impl1.par" 

bitgen -w "INS350_5J_JZ_impl1.ncd" -f "INS350_5J_JZ_impl1.t2b" -e -s "D:/Project/TLH50_03J_JZ_20250925/INS350_5J_JZ.sec" -k "D:/Project/TLH50_03J_JZ_20250925/INS350_5J_JZ.bek" "INS350_5J_JZ_impl1.prf"


BITGEN: Bitstream Generator Diamond (64-bit) 3.12.1.454
Copyright (c) 1991-1994 by NeoCAD Inc. All rights reserved.
Copyright (c) 1995 AT&T Corp.   All rights reserved.
Copyright (c) 1995-2001 Lucent Technologies Inc.  All rights reserved.
Copyright (c) 2001 Agere Systems   All rights reserved.
Copyright (c) 2002-2020 Lattice Semiconductor Corporation,  All rights reserved.


Loading design for application Bitgen from file INS350_5J_JZ_impl1.ncd.
Design name: INS350_5J_JZ
NCD version: 3.3
Vendor:      LATTICE
Device:      LFE5U-25F
Package:     CABGA256
Performance: 7
Loading device for application Bitgen from file 'sa5p25.nph' in environment: D:/Software/lscc/diamond/3.12/ispfpga.
Package Status:                     Final          Version 1.42.
Performance Hardware Data Status:   Final          Version 55.1.

Running DRC.
DRC detected 0 errors and 0 warnings.
Reading Preference File from INS350_5J_JZ_impl1.prf.

Preference Summary:
+---------------------------------+---------------------------------+
|  Preference                     |  Current Setting                |
+---------------------------------+---------------------------------+
|                         RamCfg  |                        Reset**  |
+---------------------------------+---------------------------------+
|                        CfgMode  |                      Disable**  |
+---------------------------------+---------------------------------+
|                        DONE_EX  |                          OFF**  |
+---------------------------------+---------------------------------+
|                        DONE_OD  |                           ON**  |
+---------------------------------+---------------------------------+
|                     MCCLK_FREQ  |                          2.4**  |
+---------------------------------+---------------------------------+
|                  CONFIG_SECURE  |                          OFF**  |
+---------------------------------+---------------------------------+
|                    CONFIG_MODE  |                         JTAG**  |
+---------------------------------+---------------------------------+
|                        WAKE_UP  |                           21**  |
+---------------------------------+---------------------------------+
|                          INBUF  |                          OFF**  |
+---------------------------------+---------------------------------+
|                             ES  |                           No**  |
+---------------------------------+---------------------------------+
|                 SLAVE_SPI_PORT  |                      DISABLE**  |
+---------------------------------+---------------------------------+
|                MASTER_SPI_PORT  |                         ENABLE  |
+---------------------------------+---------------------------------+
|                COMPRESS_CONFIG  |                          OFF**  |
+---------------------------------+---------------------------------+
|            BACKGROUND_RECONFIG  |                          OFF**  |
+---------------------------------+---------------------------------+
|                     DisableUES  |                        FALSE**  |
+---------------------------------+---------------------------------+
|            SLAVE_PARALLEL_PORT  |                      DISABLE**  |
+---------------------------------+---------------------------------+
|                      DONE_PULL  |                           ON**  |
+---------------------------------+---------------------------------+
|               CONFIG_IOVOLTAGE  |                            3.3  |
+---------------------------------+---------------------------------+
|                        TRANSFR  |                          OFF**  |
+---------------------------------+---------------------------------+
 *  Default setting.
 ** The specified setting matches the default setting.


Creating bit map...
 
Bitstream Status: Final           Version 10.27.
 
Saving bit stream in "INS350_5J_JZ_impl1.bit".
Total CPU Time: 2 secs 
Total REAL Time: 3 secs 
Peak Memory Usage: 290 MB
