@N: MF916 |Option synthesis_strategy=base is enabled. 
@N: MF248 |Running in 64-bit mode.
@N: MF666 |Clock conversion enabled. (Command "set_option -fix_gated_and_generated_clocks 1" in the project file.)
@N: MO106 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\ds18b20.v":269:11:269:33|Found ROM RD_CMD_DATA_pmux (in view: work.DS18B20(verilog)) with 16 words by 1 bit.
@N: MO106 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\ds18b20.v":211:11:211:33|Found ROM WR_CMD_DATA_pmux (in view: work.DS18B20(verilog)) with 16 words by 1 bit.
@N: MO231 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\ds18b20.v":157:0:157:5|Found counter in view:work.DS18B20(verilog) instance bit_cnt[3:0] 
@N: MO231 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\speed_select_tx.v":37:0:37:5|Found counter in view:work.speed_select_Tx_120000000s_115200s_1041s_520s(verilog) instance cnt[12:0] 
@N: MO231 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\rs422output.v":150:0:150:5|Found counter in view:work.SignalProcessing_Z3(verilog) instance rs422.count_pos[7:0] 
@N: BN362 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\integration.v":76:0:76:5|Removing sequential instance integ.dout[55] (in view: work.SignalProcessing_Z3(verilog)) of type view:PrimLib.dff(prim) because it does not drive other instances.
@N: BN362 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\integration.v":66:0:66:5|Removing sequential instance signal_process.integ.DA_dout[55] (in view: work.INS350_5J_JZ(verilog)) because it does not drive other instances.
@N: BN362 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":181:0:181:5|Removing sequential instance signal_process.demodu.dout[55] (in view: work.INS350_5J_JZ(verilog)) because it does not drive other instances.
@N: BN362 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":163:0:163:5|Removing sequential instance signal_process.demodu.median_sum_n[55] (in view: work.INS350_5J_JZ(verilog)) because it does not drive other instances.
@N: BN362 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":163:0:163:5|Removing sequential instance signal_process.demodu.INS_dout[55] (in view: work.INS350_5J_JZ(verilog)) because it does not drive other instances.
@N: FX164 |The option to pack registers in the IOB has not been specified. Please set syn_useioff attribute.  
@N: FX1056 |Writing EDF file: D:\Project\TLH50_03J_JZ_20250925\impl1\INS350_5J_JZ_impl1.edi
@N: BW106 |Synplicity Constraint File capacitance units using default value of 1pF 
@N: MT615 |Found clock DS18B20|clk_us_derived_clock with period 5.00ns 
@N: MT320 |This timing report is an estimate of place and route data. For final timing results, use the FPGA vendor place and route report.
@N: MT322 |Clock constraints include only register-to-register paths associated with each individual clock.
