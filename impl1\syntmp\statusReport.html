<html> 
 			<head> 			<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1" />
			<title>Project Status Summary Page</title>
			<link rel="stylesheet" type="text/css" href="projectstatuspage.css" />
			<script type = "text/javascript" src="projectstatuspage.js"></script>
			</head>

 			<body style="background-color:#f0f0ff;">
 			
<table style="border:none;" width="100%" ><tr> <td class="outline">
<table width="100%" border="0" cellspacing="0" cellpadding="0"> 	     <thead class="tablehead"><tr><th colspan="4">Project Settings</th><tr> 
 <tr> <td class="optionTitle" align="left"> Project Name</td> <td> proj_1</td> <td class="optionTitle" align="left"> Device Name</td> <td> impl1: Lattice ECP5U : LFE5U_25F</td> </tr>
<tr> <td class="optionTitle" align="left"> Implementation Name</td> <td> impl1</td> <td class="optionTitle" align="left"> Top Module</td> <td> INS350_5J_JZ</td> </tr>
 		 </thead> 
		 <tbody> <tr> <td class="optionTitle" align="left"> Pipelining</td> <td> 1</td> <td class="optionTitle" align="left"> Retiming</td> <td> 0</td> </tr>
<tr> <td class="optionTitle" align="left"> Resource Sharing</td> <td> 1</td> <td class="optionTitle" align="left"> Fanout Guide</td> <td> 1000</td> </tr>
<tr> <td class="optionTitle" align="left"> Disable I/O Insertion</td> <td> 0</td> <td class="optionTitle" align="left"> Disable Sequential Optimizations</td> <td> 0</td> </tr>
<tr> <td class="optionTitle" align="left"> Clock Conversion</td> <td> 1</td> <td class="optionTitle" align="left"> FSM Compiler</td> <td> 1</td> </tr>
 
</tbody> 
  </table><br>	 <table width="100%" border="1" cellspacing= "0" cellpadding= "0" >
 				   <thead class="tablehead"><tr><th   colspan="9">Run Status</th></tr></thead>
         <tbody>
 		<tr>
 			<th align="left" width="17%">Job Name</th>
			<th align="left">Status</th>
 			<td class="lnote" align="center" title="Notes"></td>
 			<td class="lwarn" align="center" title="Warnings"></td>
 			<td class="lerror" align="center" title="Errors"></td>
 			<th align="left">CPU Time</th>
 			<th align="left">Real Time</th>
 			<th align="left">Memory</th>
 			<th align="left">Date/Time</th>
         </tr>
  <tr>
  <td class="optionTitle"> (compiler)</td><td>Complete</td>
 <td>82</td>
 <td>33</td>
<td>0</td>
<td>-</td>
<td>00m:05s</td>
<td>-</td>
<td><font size="-1">2025/9/25</font><br/><font size="-2">10:29:23</font></td>
</tr> 

 <tr>
  <td class="optionTitle"> (premap)</td><td>Complete</td>
 <td>17</td>
 <td>29</td>
<td>0</td>
<td>0m:01s</td>
<td>0m:01s</td>
<td>195MB</td>
<td><font size="-1">2025/9/25</font><br/><font size="-2">10:29:26</font></td>
</tr> 

 <tr>
  <td class="optionTitle"> (fpga_mapper)</td><td>Complete</td>
 <td>19</td>
 <td>7</td>
<td>0</td>
<td>0m:08s</td>
<td>0m:08s</td>
<td>220MB</td>
<td><font size="-1">2025/9/25</font><br/><font size="-2">10:29:35</font></td>
</tr> 

<tr>
  <td class="optionTitle">Multi-srs Generator</td>
  <td>Complete</td><td class="empty"></td><td class="empty"></td><td class="empty"></td><td></td><td class="empty"></td><td class="empty"></td><td><font size="-1">2025/9/25</font><br/><font size="-2">10:29:24</font></td> 		</tbody>
     </table>
 <br> 
 <table width="100%" border="1" cellspacing= "0" cellpadding= "0" >
 				   <thead class="tablehead"><tr><th   colspan="4">Area Summary</th></tr></thead>
<tfoot> <tr> <td class="optionTitle" colspan="4"></td></tr> 
 </tfoot> 
 <tbody> <tr> 
<td title ="Total Register bits used" class="optionTitle" align="left">Register bits</td> <td>1044</td>
<td title ="Total I/O cells used" class="optionTitle" align="left">I/O cells</td> <td>32</td>
</tr>
<tr> 
<td title ="Total Block RAMs used" class="optionTitle" align="left">Block RAMs
(v_ram)</td> <td>2</td>
<td title ="Total DSPs used" class="optionTitle" align="left">DSPs
(dsp_used)</td> <td>5</td>
</tr>
<tr> 
<td title ="Total ORCA LUTs used" class="optionTitle" align="left">ORCA LUTs
(total_luts)</td> <td>281</td>
<td class="optionTitle"></td><td></td></tr> 
</tbody>
    </table><br>
 <table width="100%" border="1" cellspacing= "0" cellpadding= "0" >
 				   <thead class="tablehead"><tr><th   colspan="4">Timing Summary</th></tr></thead>
<tfoot> <tr> <td class="optionTitle" colspan="4"></td></tr> 
 </tfoot> 
<tbody> 
   <tr><th class="optionTitle" align= "left ">Clock Name</th><th class="optionTitle" align= "left ">Req Freq</th><th class="optionTitle" align= "left ">Est Freq</th><th class="optionTitle" align= "left ">Slack</th></tr> 
<tr> <td  align="left">DS18B20|clk_us_derived_clock</td><td  align="left">200.0 MHz</td><td  align="left">238.7 MHz</td><td  align="left">1.621</td></tr> 
<tr> <td  align="left">global_clock|CLKOP_inferred_clock</td><td  align="left">200.0 MHz</td><td  align="left">202.3 MHz</td><td  align="left">0.058</td></tr> 
<tr> <td  align="left">global_clock|CLKOS_inferred_clock</td><td  align="left">200.0 MHz</td><td  align="left">233.5 MHz</td><td  align="left">0.717</td></tr> 
<tr> <td  align="left">System</td><td  align="left">200.0 MHz</td><td  align="left">2347.4 MHz</td><td  align="left">4.574</td></tr> 
</tbody> 
 </table>
<br>
 <table width="100%" border="1" cellspacing= "0" cellpadding= "0" >
 				   <thead class="tablehead"><tr><th   colspan="4">Optimizations Summary</th></tr></thead>
 <tbody> <tr> 
<td title ="Non-gated/non-generated clock trees / Gated/generated clock trees" class="optionTitle" align="left">Combined Clock Conversion</td> <td>0 / 3</td>
<td class="optionTitle"></td><td></td></tr> 
</tbody>
    </table><br>
<br> 
</td></tr></table></body> 
 </html>