NOTE Copyright (C), 1992-2010, Lattice Semiconductor Corporation *
NOTE All Rights Reserved *
NOTE DATE CREATED: Thu Sep 25 10:30:10 2025 *
NOTE DESIGN NAME: INS350_5J_JZ *
NOTE DEVICE NAME: LFE5U-25F-7CABGA256 *
NOTE PIN ASSIGNMENTS *
NOTE PINS dq : N16 : inout *
NOTE PINS TXD : A13 : out *
NOTE PINS clk_in : L16 : in *
NOTE PINS clk_DA : K1 : out *
NOTE PINS clk_AD_t : L1 : out *
NOTE PINS DA_DATA[13] : K3 : out *
NOTE PINS DA_DATA[12] : K2 : out *
NOTE PINS DA_DATA[11] : J2 : out *
NOTE PINS DA_DATA[10] : J1 : out *
NOTE PINS DA_DATA[9] : G2 : out *
NOTE PINS DA_DATA[8] : G1 : out *
NOTE PINS DA_DATA[7] : F2 : out *
NOTE PINS DA_DATA[6] : F1 : out *
NOTE PINS DA_DATA[5] : E1 : out *
NOTE PINS DA_DATA[4] : D1 : out *
NOTE PINS DA_DATA[3] : C1 : out *
NOTE PINS DA_DATA[2] : C2 : out *
NOTE PINS DA_DATA[1] : B1 : out *
NOTE PINS DA_DATA[0] : B2 : out *
NOTE PINS AD_DATA[11] : P1 : in *
NOTE PINS AD_DATA[10] : P2 : in *
NOTE PINS AD_DATA[9] : R1 : in *
NOTE PINS AD_DATA[8] : R2 : in *
NOTE PINS AD_DATA[7] : T2 : in *
NOTE PINS AD_DATA[6] : R3 : in *
NOTE PINS AD_DATA[5] : T3 : in *
NOTE PINS AD_DATA[4] : R4 : in *
NOTE PINS AD_DATA[3] : T4 : in *
NOTE PINS AD_DATA[2] : R5 : in *
NOTE PINS AD_DATA[1] : P6 : in *
NOTE PINS AD_DATA[0] : N6 : in *
NOTE PINS TxTransmit : P16 : out *
NOTE PINS SYSCONFIG_PIN_D3 : N7 : inout *
NOTE PINS SYSCONFIG_PIN_D1 : T7 : inout *
NOTE PINS SYSCONFIG_PIN_D2 : M7 : inout *
NOTE PINS SYSCONFIG_PIN_D0 : T8 : inout *
NOTE PINS SYSCONFIG_PIN_HOLDN : N8 : inout *
NOTE CONFIGURATION MODE: JTAG *
NOTE COMPRESSION: off *
