
Loading design for application trce from file ins350_5j_jz_impl1.ncd.
Design name: INS350_5J_JZ
NCD version: 3.3
Vendor:      LATTICE
Device:      LFE5U-25F
Package:     CABGA256
Performance: 7
Loading device for application trce from file 'sa5p25.nph' in environment: D:/Software/lscc/diamond/3.12/ispfpga.
Package Status:                     Final          Version 1.42.
Performance Hardware Data Status:   Final          Version 55.1.
Setup and Hold Report

--------------------------------------------------------------------------------
<PERSON><PERSON><PERSON> TRACE Report - Setup, Version Diamond (64-bit) 3.12.1.454
Thu Sep 25 10:30:06 2025

Copyright (c) 1991-1994 by NeoCAD Inc. All rights reserved.
Copyright (c) 1995 AT&T Corp.   All rights reserved.
Copyright (c) 1995-2001 Lucent Technologies Inc.  All rights reserved.
Copyright (c) 2001 Agere Systems   All rights reserved.
Copyright (c) 2002-2020 Lattice Semiconductor Corporation,  All rights reserved.

Report Information
------------------
Command line:    trce -v 10 -gt -sethld -sp 7 -sphld m -o INS350_5J_JZ_impl1.twr -gui -msgset D:/Project/TLH50_03J_JZ_20250925/promote.xml INS350_5J_JZ_impl1.ncd INS350_5J_JZ_impl1.prf 
Design file:     ins350_5j_jz_impl1.ncd
Preference file: ins350_5j_jz_impl1.prf
Device,speed:    LFE5U-25F,7
Report level:    verbose report, limited to 10 items per preference
--------------------------------------------------------------------------------

BLOCK ASYNCPATHS
BLOCK RESETPATHS
BLOCK JTAG PATHS
--------------------------------------------------------------------------------


Derating parameters
-------------------
VCCIO Voltage:
                   3.300 V (Bank 0)
                   3.300 V (Bank 1, defined by PAR)
                   3.300 V (Bank 2)
                   3.300 V (Bank 3, defined by PAR)
                   3.300 V (Bank 6, defined by PAR)
                   3.300 V (Bank 7, defined by PAR)



================================================================================
Preference: FREQUENCY NET "clk120mhz" 120.000000 MHz ;
            4096 items scored, 0 timing errors detected.
--------------------------------------------------------------------------------


Passed: The following path meets requirements by 1.227ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         MULT18X18D Port           signal_process/rs422/un3_p_sum[18:53](ASIC)  (from clk120mhz +)
   Destination:    FF         Data in        signal_process/rs422/p_sum[55]  (to clk120mhz +)

   Delay:               7.255ns  (87.2% logic, 12.8% route), 11 logic levels.

 Constraint Details:

      7.255ns physical path delay signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_70 meets
      8.333ns delay constraint less
      0.089ns skew and
     -0.238ns DIN_SET requirement (totaling 8.482ns) by 1.227ns

 Physical Path Details:

      Data path signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_70:

   Name    Fanout   Delay (ns)          Site               Resource
C2OUT_DEL   ---     3.455 *8_R13C14.CLK0 to *18_R13C14.P27 signal_process/rs422/un3_p_sum[18:53] (from clk120mhz)
ROUTE         1     0.000 *18_R13C14.P27 to *4_R13C16.MB27 signal_process/rs422/un3_p_sum_1[27]
PD_DEL      ---     1.617 *4_R13C16.MB27 to *54_R13C16.R37 signal_process/rs422/un3_p_sum_add[0:53]
ROUTE         1     0.929 *54_R13C16.R37 to     R14C16B.B0 signal_process/rs422/un3_p_sum_add[37]
C0TOFCO_DE  ---     0.401     R14C16B.B0 to    R14C16B.FCO signal_process/rs422/SLICE_62
ROUTE         1     0.000    R14C16B.FCO to    R14C16C.FCI signal_process/rs422/un3_p_sum_add_cry_2
FCITOFCO_D  ---     0.063    R14C16C.FCI to    R14C16C.FCO signal_process/rs422/SLICE_63
ROUTE         1     0.000    R14C16C.FCO to    R14C16D.FCI signal_process/rs422/un3_p_sum_add_cry_4
FCITOFCO_D  ---     0.063    R14C16D.FCI to    R14C16D.FCO signal_process/rs422/SLICE_64
ROUTE         1     0.000    R14C16D.FCO to    R14C17A.FCI signal_process/rs422/un3_p_sum_add_cry_6
FCITOFCO_D  ---     0.063    R14C17A.FCI to    R14C17A.FCO signal_process/rs422/SLICE_65
ROUTE         1     0.000    R14C17A.FCO to    R14C17B.FCI signal_process/rs422/un3_p_sum_add_cry_8
FCITOFCO_D  ---     0.063    R14C17B.FCI to    R14C17B.FCO signal_process/rs422/SLICE_66
ROUTE         1     0.000    R14C17B.FCO to    R14C17C.FCI signal_process/rs422/un3_p_sum_add_cry_10
FCITOFCO_D  ---     0.063    R14C17C.FCI to    R14C17C.FCO signal_process/rs422/SLICE_67
ROUTE         1     0.000    R14C17C.FCO to    R14C17D.FCI signal_process/rs422/un3_p_sum_add_cry_12
FCITOFCO_D  ---     0.063    R14C17D.FCI to    R14C17D.FCO signal_process/rs422/SLICE_68
ROUTE         1     0.000    R14C17D.FCO to    R14C18A.FCI signal_process/rs422/un3_p_sum_add_cry_14
FCITOFCO_D  ---     0.063    R14C18A.FCI to    R14C18A.FCO signal_process/rs422/SLICE_69
ROUTE         1     0.000    R14C18A.FCO to    R14C18B.FCI signal_process/rs422/un3_p_sum_add_cry_16
FCITOF1_DE  ---     0.412    R14C18B.FCI to     R14C18B.F1 signal_process/rs422/SLICE_70
ROUTE         1     0.000     R14C18B.F1 to    R14C18B.DI1 signal_process/rs422/un3_p_sum[54] (to clk120mhz)
                  --------
                    7.255   (87.2% logic, 12.8% route), 11 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/rs422/un3_p_sum[18:53]:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       446     1.979  PLL_BR0.CLKOP to *8_R13C14.CLK0 clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/rs422/SLICE_70:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       446     1.890  PLL_BR0.CLKOP to    R14C18B.CLK clk120mhz
                  --------
                    1.890   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 1.227ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         MULT18X18D Port           signal_process/rs422/un3_p_sum[18:53](ASIC)  (from clk120mhz +)
   Destination:    FF         Data in        signal_process/rs422/p_sum[55]  (to clk120mhz +)

   Delay:               7.255ns  (87.2% logic, 12.8% route), 11 logic levels.

 Constraint Details:

      7.255ns physical path delay signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_70 meets
      8.333ns delay constraint less
      0.089ns skew and
     -0.238ns DIN_SET requirement (totaling 8.482ns) by 1.227ns

 Physical Path Details:

      Data path signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_70:

   Name    Fanout   Delay (ns)          Site               Resource
C2OUT_DEL   ---     3.455 *8_R13C14.CLK0 to *18_R13C14.P33 signal_process/rs422/un3_p_sum[18:53] (from clk120mhz)
ROUTE         1     0.000 *18_R13C14.P33 to *4_R13C16.MB33 signal_process/rs422/un3_p_sum_1[33]
PD_DEL      ---     1.617 *4_R13C16.MB33 to *54_R13C16.R37 signal_process/rs422/un3_p_sum_add[0:53]
ROUTE         1     0.929 *54_R13C16.R37 to     R14C16B.B0 signal_process/rs422/un3_p_sum_add[37]
C0TOFCO_DE  ---     0.401     R14C16B.B0 to    R14C16B.FCO signal_process/rs422/SLICE_62
ROUTE         1     0.000    R14C16B.FCO to    R14C16C.FCI signal_process/rs422/un3_p_sum_add_cry_2
FCITOFCO_D  ---     0.063    R14C16C.FCI to    R14C16C.FCO signal_process/rs422/SLICE_63
ROUTE         1     0.000    R14C16C.FCO to    R14C16D.FCI signal_process/rs422/un3_p_sum_add_cry_4
FCITOFCO_D  ---     0.063    R14C16D.FCI to    R14C16D.FCO signal_process/rs422/SLICE_64
ROUTE         1     0.000    R14C16D.FCO to    R14C17A.FCI signal_process/rs422/un3_p_sum_add_cry_6
FCITOFCO_D  ---     0.063    R14C17A.FCI to    R14C17A.FCO signal_process/rs422/SLICE_65
ROUTE         1     0.000    R14C17A.FCO to    R14C17B.FCI signal_process/rs422/un3_p_sum_add_cry_8
FCITOFCO_D  ---     0.063    R14C17B.FCI to    R14C17B.FCO signal_process/rs422/SLICE_66
ROUTE         1     0.000    R14C17B.FCO to    R14C17C.FCI signal_process/rs422/un3_p_sum_add_cry_10
FCITOFCO_D  ---     0.063    R14C17C.FCI to    R14C17C.FCO signal_process/rs422/SLICE_67
ROUTE         1     0.000    R14C17C.FCO to    R14C17D.FCI signal_process/rs422/un3_p_sum_add_cry_12
FCITOFCO_D  ---     0.063    R14C17D.FCI to    R14C17D.FCO signal_process/rs422/SLICE_68
ROUTE         1     0.000    R14C17D.FCO to    R14C18A.FCI signal_process/rs422/un3_p_sum_add_cry_14
FCITOFCO_D  ---     0.063    R14C18A.FCI to    R14C18A.FCO signal_process/rs422/SLICE_69
ROUTE         1     0.000    R14C18A.FCO to    R14C18B.FCI signal_process/rs422/un3_p_sum_add_cry_16
FCITOF1_DE  ---     0.412    R14C18B.FCI to     R14C18B.F1 signal_process/rs422/SLICE_70
ROUTE         1     0.000     R14C18B.F1 to    R14C18B.DI1 signal_process/rs422/un3_p_sum[54] (to clk120mhz)
                  --------
                    7.255   (87.2% logic, 12.8% route), 11 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/rs422/un3_p_sum[18:53]:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       446     1.979  PLL_BR0.CLKOP to *8_R13C14.CLK0 clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/rs422/SLICE_70:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       446     1.890  PLL_BR0.CLKOP to    R14C18B.CLK clk120mhz
                  --------
                    1.890   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 1.227ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         MULT18X18D Port           signal_process/rs422/un3_p_sum[18:53](ASIC)  (from clk120mhz +)
   Destination:    FF         Data in        signal_process/rs422/p_sum[55]  (to clk120mhz +)

   Delay:               7.255ns  (87.2% logic, 12.8% route), 11 logic levels.

 Constraint Details:

      7.255ns physical path delay signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_70 meets
      8.333ns delay constraint less
      0.089ns skew and
     -0.238ns DIN_SET requirement (totaling 8.482ns) by 1.227ns

 Physical Path Details:

      Data path signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_70:

   Name    Fanout   Delay (ns)          Site               Resource
C2OUT_DEL   ---     3.455 *8_R13C14.CLK0 to *18_R13C14.P35 signal_process/rs422/un3_p_sum[18:53] (from clk120mhz)
ROUTE         1     0.000 *18_R13C14.P35 to *4_R13C16.MB35 signal_process/rs422/un3_p_sum_1[35]
PD_DEL      ---     1.617 *4_R13C16.MB35 to *54_R13C16.R37 signal_process/rs422/un3_p_sum_add[0:53]
ROUTE         1     0.929 *54_R13C16.R37 to     R14C16B.B0 signal_process/rs422/un3_p_sum_add[37]
C0TOFCO_DE  ---     0.401     R14C16B.B0 to    R14C16B.FCO signal_process/rs422/SLICE_62
ROUTE         1     0.000    R14C16B.FCO to    R14C16C.FCI signal_process/rs422/un3_p_sum_add_cry_2
FCITOFCO_D  ---     0.063    R14C16C.FCI to    R14C16C.FCO signal_process/rs422/SLICE_63
ROUTE         1     0.000    R14C16C.FCO to    R14C16D.FCI signal_process/rs422/un3_p_sum_add_cry_4
FCITOFCO_D  ---     0.063    R14C16D.FCI to    R14C16D.FCO signal_process/rs422/SLICE_64
ROUTE         1     0.000    R14C16D.FCO to    R14C17A.FCI signal_process/rs422/un3_p_sum_add_cry_6
FCITOFCO_D  ---     0.063    R14C17A.FCI to    R14C17A.FCO signal_process/rs422/SLICE_65
ROUTE         1     0.000    R14C17A.FCO to    R14C17B.FCI signal_process/rs422/un3_p_sum_add_cry_8
FCITOFCO_D  ---     0.063    R14C17B.FCI to    R14C17B.FCO signal_process/rs422/SLICE_66
ROUTE         1     0.000    R14C17B.FCO to    R14C17C.FCI signal_process/rs422/un3_p_sum_add_cry_10
FCITOFCO_D  ---     0.063    R14C17C.FCI to    R14C17C.FCO signal_process/rs422/SLICE_67
ROUTE         1     0.000    R14C17C.FCO to    R14C17D.FCI signal_process/rs422/un3_p_sum_add_cry_12
FCITOFCO_D  ---     0.063    R14C17D.FCI to    R14C17D.FCO signal_process/rs422/SLICE_68
ROUTE         1     0.000    R14C17D.FCO to    R14C18A.FCI signal_process/rs422/un3_p_sum_add_cry_14
FCITOFCO_D  ---     0.063    R14C18A.FCI to    R14C18A.FCO signal_process/rs422/SLICE_69
ROUTE         1     0.000    R14C18A.FCO to    R14C18B.FCI signal_process/rs422/un3_p_sum_add_cry_16
FCITOF1_DE  ---     0.412    R14C18B.FCI to     R14C18B.F1 signal_process/rs422/SLICE_70
ROUTE         1     0.000     R14C18B.F1 to    R14C18B.DI1 signal_process/rs422/un3_p_sum[54] (to clk120mhz)
                  --------
                    7.255   (87.2% logic, 12.8% route), 11 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/rs422/un3_p_sum[18:53]:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       446     1.979  PLL_BR0.CLKOP to *8_R13C14.CLK0 clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/rs422/SLICE_70:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       446     1.890  PLL_BR0.CLKOP to    R14C18B.CLK clk120mhz
                  --------
                    1.890   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 1.227ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         MULT18X18D Port           signal_process/rs422/un3_p_sum[18:53](ASIC)  (from clk120mhz +)
   Destination:    FF         Data in        signal_process/rs422/p_sum[55]  (to clk120mhz +)

   Delay:               7.255ns  (87.2% logic, 12.8% route), 11 logic levels.

 Constraint Details:

      7.255ns physical path delay signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_70 meets
      8.333ns delay constraint less
      0.089ns skew and
     -0.238ns DIN_SET requirement (totaling 8.482ns) by 1.227ns

 Physical Path Details:

      Data path signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_70:

   Name    Fanout   Delay (ns)          Site               Resource
C2OUT_DEL   ---     3.455 *8_R13C14.CLK0 to *18_R13C14.P29 signal_process/rs422/un3_p_sum[18:53] (from clk120mhz)
ROUTE         1     0.000 *18_R13C14.P29 to *4_R13C16.MB29 signal_process/rs422/un3_p_sum_1[29]
PD_DEL      ---     1.617 *4_R13C16.MB29 to *54_R13C16.R37 signal_process/rs422/un3_p_sum_add[0:53]
ROUTE         1     0.929 *54_R13C16.R37 to     R14C16B.B0 signal_process/rs422/un3_p_sum_add[37]
C0TOFCO_DE  ---     0.401     R14C16B.B0 to    R14C16B.FCO signal_process/rs422/SLICE_62
ROUTE         1     0.000    R14C16B.FCO to    R14C16C.FCI signal_process/rs422/un3_p_sum_add_cry_2
FCITOFCO_D  ---     0.063    R14C16C.FCI to    R14C16C.FCO signal_process/rs422/SLICE_63
ROUTE         1     0.000    R14C16C.FCO to    R14C16D.FCI signal_process/rs422/un3_p_sum_add_cry_4
FCITOFCO_D  ---     0.063    R14C16D.FCI to    R14C16D.FCO signal_process/rs422/SLICE_64
ROUTE         1     0.000    R14C16D.FCO to    R14C17A.FCI signal_process/rs422/un3_p_sum_add_cry_6
FCITOFCO_D  ---     0.063    R14C17A.FCI to    R14C17A.FCO signal_process/rs422/SLICE_65
ROUTE         1     0.000    R14C17A.FCO to    R14C17B.FCI signal_process/rs422/un3_p_sum_add_cry_8
FCITOFCO_D  ---     0.063    R14C17B.FCI to    R14C17B.FCO signal_process/rs422/SLICE_66
ROUTE         1     0.000    R14C17B.FCO to    R14C17C.FCI signal_process/rs422/un3_p_sum_add_cry_10
FCITOFCO_D  ---     0.063    R14C17C.FCI to    R14C17C.FCO signal_process/rs422/SLICE_67
ROUTE         1     0.000    R14C17C.FCO to    R14C17D.FCI signal_process/rs422/un3_p_sum_add_cry_12
FCITOFCO_D  ---     0.063    R14C17D.FCI to    R14C17D.FCO signal_process/rs422/SLICE_68
ROUTE         1     0.000    R14C17D.FCO to    R14C18A.FCI signal_process/rs422/un3_p_sum_add_cry_14
FCITOFCO_D  ---     0.063    R14C18A.FCI to    R14C18A.FCO signal_process/rs422/SLICE_69
ROUTE         1     0.000    R14C18A.FCO to    R14C18B.FCI signal_process/rs422/un3_p_sum_add_cry_16
FCITOF1_DE  ---     0.412    R14C18B.FCI to     R14C18B.F1 signal_process/rs422/SLICE_70
ROUTE         1     0.000     R14C18B.F1 to    R14C18B.DI1 signal_process/rs422/un3_p_sum[54] (to clk120mhz)
                  --------
                    7.255   (87.2% logic, 12.8% route), 11 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/rs422/un3_p_sum[18:53]:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       446     1.979  PLL_BR0.CLKOP to *8_R13C14.CLK0 clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/rs422/SLICE_70:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       446     1.890  PLL_BR0.CLKOP to    R14C18B.CLK clk120mhz
                  --------
                    1.890   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 1.227ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         MULT18X18D Port           signal_process/rs422/un3_p_sum[18:53](ASIC)  (from clk120mhz +)
   Destination:    FF         Data in        signal_process/rs422/p_sum[55]  (to clk120mhz +)

   Delay:               7.255ns  (87.2% logic, 12.8% route), 11 logic levels.

 Constraint Details:

      7.255ns physical path delay signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_70 meets
      8.333ns delay constraint less
      0.089ns skew and
     -0.238ns DIN_SET requirement (totaling 8.482ns) by 1.227ns

 Physical Path Details:

      Data path signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_70:

   Name    Fanout   Delay (ns)          Site               Resource
C2OUT_DEL   ---     3.455 *8_R13C14.CLK0 to *18_R13C14.P31 signal_process/rs422/un3_p_sum[18:53] (from clk120mhz)
ROUTE         1     0.000 *18_R13C14.P31 to *4_R13C16.MB31 signal_process/rs422/un3_p_sum_1[31]
PD_DEL      ---     1.617 *4_R13C16.MB31 to *54_R13C16.R37 signal_process/rs422/un3_p_sum_add[0:53]
ROUTE         1     0.929 *54_R13C16.R37 to     R14C16B.B0 signal_process/rs422/un3_p_sum_add[37]
C0TOFCO_DE  ---     0.401     R14C16B.B0 to    R14C16B.FCO signal_process/rs422/SLICE_62
ROUTE         1     0.000    R14C16B.FCO to    R14C16C.FCI signal_process/rs422/un3_p_sum_add_cry_2
FCITOFCO_D  ---     0.063    R14C16C.FCI to    R14C16C.FCO signal_process/rs422/SLICE_63
ROUTE         1     0.000    R14C16C.FCO to    R14C16D.FCI signal_process/rs422/un3_p_sum_add_cry_4
FCITOFCO_D  ---     0.063    R14C16D.FCI to    R14C16D.FCO signal_process/rs422/SLICE_64
ROUTE         1     0.000    R14C16D.FCO to    R14C17A.FCI signal_process/rs422/un3_p_sum_add_cry_6
FCITOFCO_D  ---     0.063    R14C17A.FCI to    R14C17A.FCO signal_process/rs422/SLICE_65
ROUTE         1     0.000    R14C17A.FCO to    R14C17B.FCI signal_process/rs422/un3_p_sum_add_cry_8
FCITOFCO_D  ---     0.063    R14C17B.FCI to    R14C17B.FCO signal_process/rs422/SLICE_66
ROUTE         1     0.000    R14C17B.FCO to    R14C17C.FCI signal_process/rs422/un3_p_sum_add_cry_10
FCITOFCO_D  ---     0.063    R14C17C.FCI to    R14C17C.FCO signal_process/rs422/SLICE_67
ROUTE         1     0.000    R14C17C.FCO to    R14C17D.FCI signal_process/rs422/un3_p_sum_add_cry_12
FCITOFCO_D  ---     0.063    R14C17D.FCI to    R14C17D.FCO signal_process/rs422/SLICE_68
ROUTE         1     0.000    R14C17D.FCO to    R14C18A.FCI signal_process/rs422/un3_p_sum_add_cry_14
FCITOFCO_D  ---     0.063    R14C18A.FCI to    R14C18A.FCO signal_process/rs422/SLICE_69
ROUTE         1     0.000    R14C18A.FCO to    R14C18B.FCI signal_process/rs422/un3_p_sum_add_cry_16
FCITOF1_DE  ---     0.412    R14C18B.FCI to     R14C18B.F1 signal_process/rs422/SLICE_70
ROUTE         1     0.000     R14C18B.F1 to    R14C18B.DI1 signal_process/rs422/un3_p_sum[54] (to clk120mhz)
                  --------
                    7.255   (87.2% logic, 12.8% route), 11 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/rs422/un3_p_sum[18:53]:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       446     1.979  PLL_BR0.CLKOP to *8_R13C14.CLK0 clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/rs422/SLICE_70:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       446     1.890  PLL_BR0.CLKOP to    R14C18B.CLK clk120mhz
                  --------
                    1.890   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 1.227ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         MULT18X18D Port           signal_process/rs422/un3_p_sum[18:53](ASIC)  (from clk120mhz +)
   Destination:    FF         Data in        signal_process/rs422/p_sum[55]  (to clk120mhz +)

   Delay:               7.255ns  (87.2% logic, 12.8% route), 11 logic levels.

 Constraint Details:

      7.255ns physical path delay signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_70 meets
      8.333ns delay constraint less
      0.089ns skew and
     -0.238ns DIN_SET requirement (totaling 8.482ns) by 1.227ns

 Physical Path Details:

      Data path signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_70:

   Name    Fanout   Delay (ns)          Site               Resource
C2OUT_DEL   ---     3.455 *8_R13C14.CLK0 to *18_R13C14.P26 signal_process/rs422/un3_p_sum[18:53] (from clk120mhz)
ROUTE         1     0.000 *18_R13C14.P26 to *4_R13C16.MB26 signal_process/rs422/un3_p_sum_1[26]
PD_DEL      ---     1.617 *4_R13C16.MB26 to *54_R13C16.R37 signal_process/rs422/un3_p_sum_add[0:53]
ROUTE         1     0.929 *54_R13C16.R37 to     R14C16B.B0 signal_process/rs422/un3_p_sum_add[37]
C0TOFCO_DE  ---     0.401     R14C16B.B0 to    R14C16B.FCO signal_process/rs422/SLICE_62
ROUTE         1     0.000    R14C16B.FCO to    R14C16C.FCI signal_process/rs422/un3_p_sum_add_cry_2
FCITOFCO_D  ---     0.063    R14C16C.FCI to    R14C16C.FCO signal_process/rs422/SLICE_63
ROUTE         1     0.000    R14C16C.FCO to    R14C16D.FCI signal_process/rs422/un3_p_sum_add_cry_4
FCITOFCO_D  ---     0.063    R14C16D.FCI to    R14C16D.FCO signal_process/rs422/SLICE_64
ROUTE         1     0.000    R14C16D.FCO to    R14C17A.FCI signal_process/rs422/un3_p_sum_add_cry_6
FCITOFCO_D  ---     0.063    R14C17A.FCI to    R14C17A.FCO signal_process/rs422/SLICE_65
ROUTE         1     0.000    R14C17A.FCO to    R14C17B.FCI signal_process/rs422/un3_p_sum_add_cry_8
FCITOFCO_D  ---     0.063    R14C17B.FCI to    R14C17B.FCO signal_process/rs422/SLICE_66
ROUTE         1     0.000    R14C17B.FCO to    R14C17C.FCI signal_process/rs422/un3_p_sum_add_cry_10
FCITOFCO_D  ---     0.063    R14C17C.FCI to    R14C17C.FCO signal_process/rs422/SLICE_67
ROUTE         1     0.000    R14C17C.FCO to    R14C17D.FCI signal_process/rs422/un3_p_sum_add_cry_12
FCITOFCO_D  ---     0.063    R14C17D.FCI to    R14C17D.FCO signal_process/rs422/SLICE_68
ROUTE         1     0.000    R14C17D.FCO to    R14C18A.FCI signal_process/rs422/un3_p_sum_add_cry_14
FCITOFCO_D  ---     0.063    R14C18A.FCI to    R14C18A.FCO signal_process/rs422/SLICE_69
ROUTE         1     0.000    R14C18A.FCO to    R14C18B.FCI signal_process/rs422/un3_p_sum_add_cry_16
FCITOF1_DE  ---     0.412    R14C18B.FCI to     R14C18B.F1 signal_process/rs422/SLICE_70
ROUTE         1     0.000     R14C18B.F1 to    R14C18B.DI1 signal_process/rs422/un3_p_sum[54] (to clk120mhz)
                  --------
                    7.255   (87.2% logic, 12.8% route), 11 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/rs422/un3_p_sum[18:53]:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       446     1.979  PLL_BR0.CLKOP to *8_R13C14.CLK0 clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/rs422/SLICE_70:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       446     1.890  PLL_BR0.CLKOP to    R14C18B.CLK clk120mhz
                  --------
                    1.890   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 1.227ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         MULT18X18D Port           signal_process/rs422/un3_p_sum[18:53](ASIC)  (from clk120mhz +)
   Destination:    FF         Data in        signal_process/rs422/p_sum[55]  (to clk120mhz +)

   Delay:               7.255ns  (87.2% logic, 12.8% route), 11 logic levels.

 Constraint Details:

      7.255ns physical path delay signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_70 meets
      8.333ns delay constraint less
      0.089ns skew and
     -0.238ns DIN_SET requirement (totaling 8.482ns) by 1.227ns

 Physical Path Details:

      Data path signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_70:

   Name    Fanout   Delay (ns)          Site               Resource
C2OUT_DEL   ---     3.455 *8_R13C14.CLK0 to *18_R13C14.P32 signal_process/rs422/un3_p_sum[18:53] (from clk120mhz)
ROUTE         1     0.000 *18_R13C14.P32 to *4_R13C16.MB32 signal_process/rs422/un3_p_sum_1[32]
PD_DEL      ---     1.617 *4_R13C16.MB32 to *54_R13C16.R37 signal_process/rs422/un3_p_sum_add[0:53]
ROUTE         1     0.929 *54_R13C16.R37 to     R14C16B.B0 signal_process/rs422/un3_p_sum_add[37]
C0TOFCO_DE  ---     0.401     R14C16B.B0 to    R14C16B.FCO signal_process/rs422/SLICE_62
ROUTE         1     0.000    R14C16B.FCO to    R14C16C.FCI signal_process/rs422/un3_p_sum_add_cry_2
FCITOFCO_D  ---     0.063    R14C16C.FCI to    R14C16C.FCO signal_process/rs422/SLICE_63
ROUTE         1     0.000    R14C16C.FCO to    R14C16D.FCI signal_process/rs422/un3_p_sum_add_cry_4
FCITOFCO_D  ---     0.063    R14C16D.FCI to    R14C16D.FCO signal_process/rs422/SLICE_64
ROUTE         1     0.000    R14C16D.FCO to    R14C17A.FCI signal_process/rs422/un3_p_sum_add_cry_6
FCITOFCO_D  ---     0.063    R14C17A.FCI to    R14C17A.FCO signal_process/rs422/SLICE_65
ROUTE         1     0.000    R14C17A.FCO to    R14C17B.FCI signal_process/rs422/un3_p_sum_add_cry_8
FCITOFCO_D  ---     0.063    R14C17B.FCI to    R14C17B.FCO signal_process/rs422/SLICE_66
ROUTE         1     0.000    R14C17B.FCO to    R14C17C.FCI signal_process/rs422/un3_p_sum_add_cry_10
FCITOFCO_D  ---     0.063    R14C17C.FCI to    R14C17C.FCO signal_process/rs422/SLICE_67
ROUTE         1     0.000    R14C17C.FCO to    R14C17D.FCI signal_process/rs422/un3_p_sum_add_cry_12
FCITOFCO_D  ---     0.063    R14C17D.FCI to    R14C17D.FCO signal_process/rs422/SLICE_68
ROUTE         1     0.000    R14C17D.FCO to    R14C18A.FCI signal_process/rs422/un3_p_sum_add_cry_14
FCITOFCO_D  ---     0.063    R14C18A.FCI to    R14C18A.FCO signal_process/rs422/SLICE_69
ROUTE         1     0.000    R14C18A.FCO to    R14C18B.FCI signal_process/rs422/un3_p_sum_add_cry_16
FCITOF1_DE  ---     0.412    R14C18B.FCI to     R14C18B.F1 signal_process/rs422/SLICE_70
ROUTE         1     0.000     R14C18B.F1 to    R14C18B.DI1 signal_process/rs422/un3_p_sum[54] (to clk120mhz)
                  --------
                    7.255   (87.2% logic, 12.8% route), 11 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/rs422/un3_p_sum[18:53]:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       446     1.979  PLL_BR0.CLKOP to *8_R13C14.CLK0 clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/rs422/SLICE_70:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       446     1.890  PLL_BR0.CLKOP to    R14C18B.CLK clk120mhz
                  --------
                    1.890   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 1.227ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         MULT18X18D Port           signal_process/rs422/un3_p_sum[18:53](ASIC)  (from clk120mhz +)
   Destination:    FF         Data in        signal_process/rs422/p_sum[55]  (to clk120mhz +)

   Delay:               7.255ns  (87.2% logic, 12.8% route), 11 logic levels.

 Constraint Details:

      7.255ns physical path delay signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_70 meets
      8.333ns delay constraint less
      0.089ns skew and
     -0.238ns DIN_SET requirement (totaling 8.482ns) by 1.227ns

 Physical Path Details:

      Data path signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_70:

   Name    Fanout   Delay (ns)          Site               Resource
C2OUT_DEL   ---     3.455 *8_R13C14.CLK0 to *18_R13C14.P30 signal_process/rs422/un3_p_sum[18:53] (from clk120mhz)
ROUTE         1     0.000 *18_R13C14.P30 to *4_R13C16.MB30 signal_process/rs422/un3_p_sum_1[30]
PD_DEL      ---     1.617 *4_R13C16.MB30 to *54_R13C16.R37 signal_process/rs422/un3_p_sum_add[0:53]
ROUTE         1     0.929 *54_R13C16.R37 to     R14C16B.B0 signal_process/rs422/un3_p_sum_add[37]
C0TOFCO_DE  ---     0.401     R14C16B.B0 to    R14C16B.FCO signal_process/rs422/SLICE_62
ROUTE         1     0.000    R14C16B.FCO to    R14C16C.FCI signal_process/rs422/un3_p_sum_add_cry_2
FCITOFCO_D  ---     0.063    R14C16C.FCI to    R14C16C.FCO signal_process/rs422/SLICE_63
ROUTE         1     0.000    R14C16C.FCO to    R14C16D.FCI signal_process/rs422/un3_p_sum_add_cry_4
FCITOFCO_D  ---     0.063    R14C16D.FCI to    R14C16D.FCO signal_process/rs422/SLICE_64
ROUTE         1     0.000    R14C16D.FCO to    R14C17A.FCI signal_process/rs422/un3_p_sum_add_cry_6
FCITOFCO_D  ---     0.063    R14C17A.FCI to    R14C17A.FCO signal_process/rs422/SLICE_65
ROUTE         1     0.000    R14C17A.FCO to    R14C17B.FCI signal_process/rs422/un3_p_sum_add_cry_8
FCITOFCO_D  ---     0.063    R14C17B.FCI to    R14C17B.FCO signal_process/rs422/SLICE_66
ROUTE         1     0.000    R14C17B.FCO to    R14C17C.FCI signal_process/rs422/un3_p_sum_add_cry_10
FCITOFCO_D  ---     0.063    R14C17C.FCI to    R14C17C.FCO signal_process/rs422/SLICE_67
ROUTE         1     0.000    R14C17C.FCO to    R14C17D.FCI signal_process/rs422/un3_p_sum_add_cry_12
FCITOFCO_D  ---     0.063    R14C17D.FCI to    R14C17D.FCO signal_process/rs422/SLICE_68
ROUTE         1     0.000    R14C17D.FCO to    R14C18A.FCI signal_process/rs422/un3_p_sum_add_cry_14
FCITOFCO_D  ---     0.063    R14C18A.FCI to    R14C18A.FCO signal_process/rs422/SLICE_69
ROUTE         1     0.000    R14C18A.FCO to    R14C18B.FCI signal_process/rs422/un3_p_sum_add_cry_16
FCITOF1_DE  ---     0.412    R14C18B.FCI to     R14C18B.F1 signal_process/rs422/SLICE_70
ROUTE         1     0.000     R14C18B.F1 to    R14C18B.DI1 signal_process/rs422/un3_p_sum[54] (to clk120mhz)
                  --------
                    7.255   (87.2% logic, 12.8% route), 11 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/rs422/un3_p_sum[18:53]:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       446     1.979  PLL_BR0.CLKOP to *8_R13C14.CLK0 clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/rs422/SLICE_70:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       446     1.890  PLL_BR0.CLKOP to    R14C18B.CLK clk120mhz
                  --------
                    1.890   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 1.227ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         MULT18X18D Port           signal_process/rs422/un3_p_sum[18:53](ASIC)  (from clk120mhz +)
   Destination:    FF         Data in        signal_process/rs422/p_sum[55]  (to clk120mhz +)

   Delay:               7.255ns  (87.2% logic, 12.8% route), 11 logic levels.

 Constraint Details:

      7.255ns physical path delay signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_70 meets
      8.333ns delay constraint less
      0.089ns skew and
     -0.238ns DIN_SET requirement (totaling 8.482ns) by 1.227ns

 Physical Path Details:

      Data path signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_70:

   Name    Fanout   Delay (ns)          Site               Resource
C2OUT_DEL   ---     3.455 *8_R13C14.CLK0 to *18_R13C14.P34 signal_process/rs422/un3_p_sum[18:53] (from clk120mhz)
ROUTE         1     0.000 *18_R13C14.P34 to *4_R13C16.MB34 signal_process/rs422/un3_p_sum_1[34]
PD_DEL      ---     1.617 *4_R13C16.MB34 to *54_R13C16.R37 signal_process/rs422/un3_p_sum_add[0:53]
ROUTE         1     0.929 *54_R13C16.R37 to     R14C16B.B0 signal_process/rs422/un3_p_sum_add[37]
C0TOFCO_DE  ---     0.401     R14C16B.B0 to    R14C16B.FCO signal_process/rs422/SLICE_62
ROUTE         1     0.000    R14C16B.FCO to    R14C16C.FCI signal_process/rs422/un3_p_sum_add_cry_2
FCITOFCO_D  ---     0.063    R14C16C.FCI to    R14C16C.FCO signal_process/rs422/SLICE_63
ROUTE         1     0.000    R14C16C.FCO to    R14C16D.FCI signal_process/rs422/un3_p_sum_add_cry_4
FCITOFCO_D  ---     0.063    R14C16D.FCI to    R14C16D.FCO signal_process/rs422/SLICE_64
ROUTE         1     0.000    R14C16D.FCO to    R14C17A.FCI signal_process/rs422/un3_p_sum_add_cry_6
FCITOFCO_D  ---     0.063    R14C17A.FCI to    R14C17A.FCO signal_process/rs422/SLICE_65
ROUTE         1     0.000    R14C17A.FCO to    R14C17B.FCI signal_process/rs422/un3_p_sum_add_cry_8
FCITOFCO_D  ---     0.063    R14C17B.FCI to    R14C17B.FCO signal_process/rs422/SLICE_66
ROUTE         1     0.000    R14C17B.FCO to    R14C17C.FCI signal_process/rs422/un3_p_sum_add_cry_10
FCITOFCO_D  ---     0.063    R14C17C.FCI to    R14C17C.FCO signal_process/rs422/SLICE_67
ROUTE         1     0.000    R14C17C.FCO to    R14C17D.FCI signal_process/rs422/un3_p_sum_add_cry_12
FCITOFCO_D  ---     0.063    R14C17D.FCI to    R14C17D.FCO signal_process/rs422/SLICE_68
ROUTE         1     0.000    R14C17D.FCO to    R14C18A.FCI signal_process/rs422/un3_p_sum_add_cry_14
FCITOFCO_D  ---     0.063    R14C18A.FCI to    R14C18A.FCO signal_process/rs422/SLICE_69
ROUTE         1     0.000    R14C18A.FCO to    R14C18B.FCI signal_process/rs422/un3_p_sum_add_cry_16
FCITOF1_DE  ---     0.412    R14C18B.FCI to     R14C18B.F1 signal_process/rs422/SLICE_70
ROUTE         1     0.000     R14C18B.F1 to    R14C18B.DI1 signal_process/rs422/un3_p_sum[54] (to clk120mhz)
                  --------
                    7.255   (87.2% logic, 12.8% route), 11 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/rs422/un3_p_sum[18:53]:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       446     1.979  PLL_BR0.CLKOP to *8_R13C14.CLK0 clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/rs422/SLICE_70:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       446     1.890  PLL_BR0.CLKOP to    R14C18B.CLK clk120mhz
                  --------
                    1.890   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 1.227ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         MULT18X18D Port           signal_process/rs422/un3_p_sum[18:53](ASIC)  (from clk120mhz +)
   Destination:    FF         Data in        signal_process/rs422/p_sum[55]  (to clk120mhz +)

   Delay:               7.255ns  (87.2% logic, 12.8% route), 11 logic levels.

 Constraint Details:

      7.255ns physical path delay signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_70 meets
      8.333ns delay constraint less
      0.089ns skew and
     -0.238ns DIN_SET requirement (totaling 8.482ns) by 1.227ns

 Physical Path Details:

      Data path signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_70:

   Name    Fanout   Delay (ns)          Site               Resource
C2OUT_DEL   ---     3.455 *8_R13C14.CLK0 to *18_R13C14.P28 signal_process/rs422/un3_p_sum[18:53] (from clk120mhz)
ROUTE         1     0.000 *18_R13C14.P28 to *4_R13C16.MB28 signal_process/rs422/un3_p_sum_1[28]
PD_DEL      ---     1.617 *4_R13C16.MB28 to *54_R13C16.R37 signal_process/rs422/un3_p_sum_add[0:53]
ROUTE         1     0.929 *54_R13C16.R37 to     R14C16B.B0 signal_process/rs422/un3_p_sum_add[37]
C0TOFCO_DE  ---     0.401     R14C16B.B0 to    R14C16B.FCO signal_process/rs422/SLICE_62
ROUTE         1     0.000    R14C16B.FCO to    R14C16C.FCI signal_process/rs422/un3_p_sum_add_cry_2
FCITOFCO_D  ---     0.063    R14C16C.FCI to    R14C16C.FCO signal_process/rs422/SLICE_63
ROUTE         1     0.000    R14C16C.FCO to    R14C16D.FCI signal_process/rs422/un3_p_sum_add_cry_4
FCITOFCO_D  ---     0.063    R14C16D.FCI to    R14C16D.FCO signal_process/rs422/SLICE_64
ROUTE         1     0.000    R14C16D.FCO to    R14C17A.FCI signal_process/rs422/un3_p_sum_add_cry_6
FCITOFCO_D  ---     0.063    R14C17A.FCI to    R14C17A.FCO signal_process/rs422/SLICE_65
ROUTE         1     0.000    R14C17A.FCO to    R14C17B.FCI signal_process/rs422/un3_p_sum_add_cry_8
FCITOFCO_D  ---     0.063    R14C17B.FCI to    R14C17B.FCO signal_process/rs422/SLICE_66
ROUTE         1     0.000    R14C17B.FCO to    R14C17C.FCI signal_process/rs422/un3_p_sum_add_cry_10
FCITOFCO_D  ---     0.063    R14C17C.FCI to    R14C17C.FCO signal_process/rs422/SLICE_67
ROUTE         1     0.000    R14C17C.FCO to    R14C17D.FCI signal_process/rs422/un3_p_sum_add_cry_12
FCITOFCO_D  ---     0.063    R14C17D.FCI to    R14C17D.FCO signal_process/rs422/SLICE_68
ROUTE         1     0.000    R14C17D.FCO to    R14C18A.FCI signal_process/rs422/un3_p_sum_add_cry_14
FCITOFCO_D  ---     0.063    R14C18A.FCI to    R14C18A.FCO signal_process/rs422/SLICE_69
ROUTE         1     0.000    R14C18A.FCO to    R14C18B.FCI signal_process/rs422/un3_p_sum_add_cry_16
FCITOF1_DE  ---     0.412    R14C18B.FCI to     R14C18B.F1 signal_process/rs422/SLICE_70
ROUTE         1     0.000     R14C18B.F1 to    R14C18B.DI1 signal_process/rs422/un3_p_sum[54] (to clk120mhz)
                  --------
                    7.255   (87.2% logic, 12.8% route), 11 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/rs422/un3_p_sum[18:53]:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       446     1.979  PLL_BR0.CLKOP to *8_R13C14.CLK0 clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/rs422/SLICE_70:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       446     1.890  PLL_BR0.CLKOP to    R14C18B.CLK clk120mhz
                  --------
                    1.890   (0.0% logic, 100.0% route), 0 logic levels.

Report:  140.726MHz is the maximum frequency for this preference.


================================================================================
Preference: FREQUENCY NET "wendu.clk_us" 1.000000 MHz ;
            3495 items scored, 0 timing errors detected.
--------------------------------------------------------------------------------


Passed: The following path meets requirements by 990.679ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/cnt_us[2]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/cnt_us[14]  (to wendu.clk_us +)

   Delay:               9.559ns  (30.3% logic, 69.7% route), 15 logic levels.

 Constraint Details:

      9.559ns physical path delay wendu/SLICE_566 to wendu/SLICE_571 meets
    1000.000ns delay constraint less
      0.000ns skew and
     -0.238ns DIN_SET requirement (totaling 1000.238ns) by 990.679ns

 Physical Path Details:

      Data path wendu/SLICE_566 to wendu/SLICE_571:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.457    R30C21D.CLK to     R30C21D.Q1 wendu/SLICE_566 (from wendu.clk_us)
ROUTE         7     1.371     R30C21D.Q1 to     R31C23A.A0 wendu/cnt_us[2]
CTOF_DEL    ---     0.208     R31C23A.A0 to     R31C23A.F0 wendu/SLICE_603
ROUTE         2     0.368     R31C23A.F0 to     R30C23B.D0 wendu/cnt_us_RNIM6RG[1]
CTOF_DEL    ---     0.208     R30C23B.D0 to     R30C23B.F0 wendu/SLICE_604
ROUTE         2     0.697     R30C23B.F0 to     R30C25C.C1 wendu/cnt_us_RNIRSM11[6]
CTOF_DEL    ---     0.208     R30C25C.C1 to     R30C25C.F1 wendu/SLICE_612
ROUTE         4     0.870     R30C25C.F1 to     R31C25A.B1 wendu/un1_cur_state_3_i_a2_3_o3_1
CTOF_DEL    ---     0.208     R31C25A.B1 to     R31C25A.F1 wendu/SLICE_589
ROUTE        18     1.611     R31C25A.F1 to     R30C24D.B0 wendu/un1_cur_state_3_i_a2_3_a2
CTOF_DEL    ---     0.208     R30C24D.B0 to     R30C24D.F0 wendu/SLICE_610
ROUTE         1     0.909     R30C24D.F0 to     R29C21A.A0 wendu/un1_cnt_us_0_sqmuxa_2_i
C0TOFCO_DE  ---     0.401     R29C21A.A0 to    R29C21A.FCO wendu/SLICE_231
ROUTE         1     0.000    R29C21A.FCO to    R29C21B.FCI wendu/un1_cnt_us_18_cry_0
FCITOFCO_D  ---     0.063    R29C21B.FCI to    R29C21B.FCO wendu/SLICE_232
ROUTE         1     0.000    R29C21B.FCO to    R29C21C.FCI wendu/un1_cnt_us_18_cry_2
FCITOFCO_D  ---     0.063    R29C21C.FCI to    R29C21C.FCO wendu/SLICE_233
ROUTE         1     0.000    R29C21C.FCO to    R29C21D.FCI wendu/un1_cnt_us_18_cry_4
FCITOFCO_D  ---     0.063    R29C21D.FCI to    R29C21D.FCO wendu/SLICE_234
ROUTE         1     0.000    R29C21D.FCO to    R29C22A.FCI wendu/un1_cnt_us_18_cry_6
FCITOFCO_D  ---     0.063    R29C22A.FCI to    R29C22A.FCO wendu/SLICE_235
ROUTE         1     0.000    R29C22A.FCO to    R29C22B.FCI wendu/un1_cnt_us_18_cry_8
FCITOFCO_D  ---     0.063    R29C22B.FCI to    R29C22B.FCO wendu/SLICE_236
ROUTE         1     0.000    R29C22B.FCO to    R29C22C.FCI wendu/un1_cnt_us_18_cry_10
FCITOFCO_D  ---     0.063    R29C22C.FCI to    R29C22C.FCO wendu/SLICE_237
ROUTE         1     0.000    R29C22C.FCO to    R29C22D.FCI wendu/un1_cnt_us_18_cry_12
FCITOF1_DE  ---     0.412    R29C22D.FCI to     R29C22D.F1 wendu/SLICE_238
ROUTE         1     0.837     R29C22D.F1 to     R30C22A.B1 wendu/un1_cnt_us_18_cry_13_0_S1
CTOF_DEL    ---     0.208     R30C22A.B1 to     R30C22A.F1 wendu/SLICE_571
ROUTE         1     0.000     R30C22A.F1 to    R30C22A.DI1 wendu/cnt_us_12[14] (to wendu.clk_us)
                  --------
                    9.559   (30.3% logic, 69.7% route), 15 logic levels.

 Clock Skew Details: 

      Source Clock Path wendu/SLICE_241 to wendu/SLICE_566:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.394     R38C31A.Q0 to    R30C21D.CLK wendu.clk_us
                  --------
                    2.394   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path wendu/SLICE_241 to wendu/SLICE_571:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.394     R38C31A.Q0 to    R30C22A.CLK wendu.clk_us
                  --------
                    2.394   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 990.884ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/cnt_us[2]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/cnt_us[17]  (to wendu.clk_us +)

   Delay:               9.351ns  (32.0% logic, 68.0% route), 17 logic levels.

 Constraint Details:

      9.351ns physical path delay wendu/SLICE_566 to wendu/SLICE_573 meets
    1000.000ns delay constraint less
      0.000ns skew and
     -0.235ns DIN_SET requirement (totaling 1000.235ns) by 990.884ns

 Physical Path Details:

      Data path wendu/SLICE_566 to wendu/SLICE_573:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.457    R30C21D.CLK to     R30C21D.Q1 wendu/SLICE_566 (from wendu.clk_us)
ROUTE         7     1.371     R30C21D.Q1 to     R31C23A.A0 wendu/cnt_us[2]
CTOF_DEL    ---     0.208     R31C23A.A0 to     R31C23A.F0 wendu/SLICE_603
ROUTE         2     0.368     R31C23A.F0 to     R30C23B.D0 wendu/cnt_us_RNIM6RG[1]
CTOF_DEL    ---     0.208     R30C23B.D0 to     R30C23B.F0 wendu/SLICE_604
ROUTE         2     0.697     R30C23B.F0 to     R30C25C.C1 wendu/cnt_us_RNIRSM11[6]
CTOF_DEL    ---     0.208     R30C25C.C1 to     R30C25C.F1 wendu/SLICE_612
ROUTE         4     0.870     R30C25C.F1 to     R31C25A.B1 wendu/un1_cur_state_3_i_a2_3_o3_1
CTOF_DEL    ---     0.208     R31C25A.B1 to     R31C25A.F1 wendu/SLICE_589
ROUTE        18     1.611     R31C25A.F1 to     R30C24D.B0 wendu/un1_cur_state_3_i_a2_3_a2
CTOF_DEL    ---     0.208     R30C24D.B0 to     R30C24D.F0 wendu/SLICE_610
ROUTE         1     0.909     R30C24D.F0 to     R29C21A.A0 wendu/un1_cnt_us_0_sqmuxa_2_i
C0TOFCO_DE  ---     0.401     R29C21A.A0 to    R29C21A.FCO wendu/SLICE_231
ROUTE         1     0.000    R29C21A.FCO to    R29C21B.FCI wendu/un1_cnt_us_18_cry_0
FCITOFCO_D  ---     0.063    R29C21B.FCI to    R29C21B.FCO wendu/SLICE_232
ROUTE         1     0.000    R29C21B.FCO to    R29C21C.FCI wendu/un1_cnt_us_18_cry_2
FCITOFCO_D  ---     0.063    R29C21C.FCI to    R29C21C.FCO wendu/SLICE_233
ROUTE         1     0.000    R29C21C.FCO to    R29C21D.FCI wendu/un1_cnt_us_18_cry_4
FCITOFCO_D  ---     0.063    R29C21D.FCI to    R29C21D.FCO wendu/SLICE_234
ROUTE         1     0.000    R29C21D.FCO to    R29C22A.FCI wendu/un1_cnt_us_18_cry_6
FCITOFCO_D  ---     0.063    R29C22A.FCI to    R29C22A.FCO wendu/SLICE_235
ROUTE         1     0.000    R29C22A.FCO to    R29C22B.FCI wendu/un1_cnt_us_18_cry_8
FCITOFCO_D  ---     0.063    R29C22B.FCI to    R29C22B.FCO wendu/SLICE_236
ROUTE         1     0.000    R29C22B.FCO to    R29C22C.FCI wendu/un1_cnt_us_18_cry_10
FCITOFCO_D  ---     0.063    R29C22C.FCI to    R29C22C.FCO wendu/SLICE_237
ROUTE         1     0.000    R29C22C.FCO to    R29C22D.FCI wendu/un1_cnt_us_18_cry_12
FCITOFCO_D  ---     0.063    R29C22D.FCI to    R29C22D.FCO wendu/SLICE_238
ROUTE         1     0.000    R29C22D.FCO to    R29C23A.FCI wendu/un1_cnt_us_18_cry_14
FCITOFCO_D  ---     0.063    R29C23A.FCI to    R29C23A.FCO wendu/SLICE_239
ROUTE         1     0.000    R29C23A.FCO to    R29C23B.FCI wendu/un1_cnt_us_18_cry_16
FCITOF0_DE  ---     0.385    R29C23B.FCI to     R29C23B.F0 wendu/SLICE_240
ROUTE         1     0.530     R29C23B.F0 to     R30C23C.C0 wendu/un1_cnt_us_18_cry_17_0_S0
CTOF_DEL    ---     0.208     R30C23C.C0 to     R30C23C.F0 wendu/SLICE_573
ROUTE         1     0.000     R30C23C.F0 to    R30C23C.DI0 wendu/cnt_us_12[17] (to wendu.clk_us)
                  --------
                    9.351   (32.0% logic, 68.0% route), 17 logic levels.

 Clock Skew Details: 

      Source Clock Path wendu/SLICE_241 to wendu/SLICE_566:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.394     R38C31A.Q0 to    R30C21D.CLK wendu.clk_us
                  --------
                    2.394   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path wendu/SLICE_241 to wendu/SLICE_573:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.394     R38C31A.Q0 to    R30C23C.CLK wendu.clk_us
                  --------
                    2.394   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 990.932ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/cnt_us[2]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/cnt_us[16]  (to wendu.clk_us +)

   Delay:               9.306ns  (31.8% logic, 68.2% route), 16 logic levels.

 Constraint Details:

      9.306ns physical path delay wendu/SLICE_566 to wendu/SLICE_572 meets
    1000.000ns delay constraint less
      0.000ns skew and
     -0.238ns DIN_SET requirement (totaling 1000.238ns) by 990.932ns

 Physical Path Details:

      Data path wendu/SLICE_566 to wendu/SLICE_572:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.457    R30C21D.CLK to     R30C21D.Q1 wendu/SLICE_566 (from wendu.clk_us)
ROUTE         7     1.371     R30C21D.Q1 to     R31C23A.A0 wendu/cnt_us[2]
CTOF_DEL    ---     0.208     R31C23A.A0 to     R31C23A.F0 wendu/SLICE_603
ROUTE         2     0.368     R31C23A.F0 to     R30C23B.D0 wendu/cnt_us_RNIM6RG[1]
CTOF_DEL    ---     0.208     R30C23B.D0 to     R30C23B.F0 wendu/SLICE_604
ROUTE         2     0.697     R30C23B.F0 to     R30C25C.C1 wendu/cnt_us_RNIRSM11[6]
CTOF_DEL    ---     0.208     R30C25C.C1 to     R30C25C.F1 wendu/SLICE_612
ROUTE         4     0.870     R30C25C.F1 to     R31C25A.B1 wendu/un1_cur_state_3_i_a2_3_o3_1
CTOF_DEL    ---     0.208     R31C25A.B1 to     R31C25A.F1 wendu/SLICE_589
ROUTE        18     1.611     R31C25A.F1 to     R30C24D.B0 wendu/un1_cur_state_3_i_a2_3_a2
CTOF_DEL    ---     0.208     R30C24D.B0 to     R30C24D.F0 wendu/SLICE_610
ROUTE         1     0.909     R30C24D.F0 to     R29C21A.A0 wendu/un1_cnt_us_0_sqmuxa_2_i
C0TOFCO_DE  ---     0.401     R29C21A.A0 to    R29C21A.FCO wendu/SLICE_231
ROUTE         1     0.000    R29C21A.FCO to    R29C21B.FCI wendu/un1_cnt_us_18_cry_0
FCITOFCO_D  ---     0.063    R29C21B.FCI to    R29C21B.FCO wendu/SLICE_232
ROUTE         1     0.000    R29C21B.FCO to    R29C21C.FCI wendu/un1_cnt_us_18_cry_2
FCITOFCO_D  ---     0.063    R29C21C.FCI to    R29C21C.FCO wendu/SLICE_233
ROUTE         1     0.000    R29C21C.FCO to    R29C21D.FCI wendu/un1_cnt_us_18_cry_4
FCITOFCO_D  ---     0.063    R29C21D.FCI to    R29C21D.FCO wendu/SLICE_234
ROUTE         1     0.000    R29C21D.FCO to    R29C22A.FCI wendu/un1_cnt_us_18_cry_6
FCITOFCO_D  ---     0.063    R29C22A.FCI to    R29C22A.FCO wendu/SLICE_235
ROUTE         1     0.000    R29C22A.FCO to    R29C22B.FCI wendu/un1_cnt_us_18_cry_8
FCITOFCO_D  ---     0.063    R29C22B.FCI to    R29C22B.FCO wendu/SLICE_236
ROUTE         1     0.000    R29C22B.FCO to    R29C22C.FCI wendu/un1_cnt_us_18_cry_10
FCITOFCO_D  ---     0.063    R29C22C.FCI to    R29C22C.FCO wendu/SLICE_237
ROUTE         1     0.000    R29C22C.FCO to    R29C22D.FCI wendu/un1_cnt_us_18_cry_12
FCITOFCO_D  ---     0.063    R29C22D.FCI to    R29C22D.FCO wendu/SLICE_238
ROUTE         1     0.000    R29C22D.FCO to    R29C23A.FCI wendu/un1_cnt_us_18_cry_14
FCITOF1_DE  ---     0.412    R29C23A.FCI to     R29C23A.F1 wendu/SLICE_239
ROUTE         1     0.521     R29C23A.F1 to     R30C23D.D1 wendu/un1_cnt_us_18_cry_15_0_S1
CTOF_DEL    ---     0.208     R30C23D.D1 to     R30C23D.F1 wendu/SLICE_572
ROUTE         1     0.000     R30C23D.F1 to    R30C23D.DI1 wendu/cnt_us_12[16] (to wendu.clk_us)
                  --------
                    9.306   (31.8% logic, 68.2% route), 16 logic levels.

 Clock Skew Details: 

      Source Clock Path wendu/SLICE_241 to wendu/SLICE_566:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.394     R38C31A.Q0 to    R30C21D.CLK wendu.clk_us
                  --------
                    2.394   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path wendu/SLICE_241 to wendu/SLICE_572:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.394     R38C31A.Q0 to    R30C23D.CLK wendu.clk_us
                  --------
                    2.394   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 990.947ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/cnt_us[2]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/cnt_us[15]  (to wendu.clk_us +)

   Delay:               9.288ns  (31.6% logic, 68.4% route), 16 logic levels.

 Constraint Details:

      9.288ns physical path delay wendu/SLICE_566 to wendu/SLICE_572 meets
    1000.000ns delay constraint less
      0.000ns skew and
     -0.235ns DIN_SET requirement (totaling 1000.235ns) by 990.947ns

 Physical Path Details:

      Data path wendu/SLICE_566 to wendu/SLICE_572:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.457    R30C21D.CLK to     R30C21D.Q1 wendu/SLICE_566 (from wendu.clk_us)
ROUTE         7     1.371     R30C21D.Q1 to     R31C23A.A0 wendu/cnt_us[2]
CTOF_DEL    ---     0.208     R31C23A.A0 to     R31C23A.F0 wendu/SLICE_603
ROUTE         2     0.368     R31C23A.F0 to     R30C23B.D0 wendu/cnt_us_RNIM6RG[1]
CTOF_DEL    ---     0.208     R30C23B.D0 to     R30C23B.F0 wendu/SLICE_604
ROUTE         2     0.697     R30C23B.F0 to     R30C25C.C1 wendu/cnt_us_RNIRSM11[6]
CTOF_DEL    ---     0.208     R30C25C.C1 to     R30C25C.F1 wendu/SLICE_612
ROUTE         4     0.870     R30C25C.F1 to     R31C25A.B1 wendu/un1_cur_state_3_i_a2_3_o3_1
CTOF_DEL    ---     0.208     R31C25A.B1 to     R31C25A.F1 wendu/SLICE_589
ROUTE        18     1.611     R31C25A.F1 to     R30C24D.B0 wendu/un1_cur_state_3_i_a2_3_a2
CTOF_DEL    ---     0.208     R30C24D.B0 to     R30C24D.F0 wendu/SLICE_610
ROUTE         1     0.909     R30C24D.F0 to     R29C21A.A0 wendu/un1_cnt_us_0_sqmuxa_2_i
C0TOFCO_DE  ---     0.401     R29C21A.A0 to    R29C21A.FCO wendu/SLICE_231
ROUTE         1     0.000    R29C21A.FCO to    R29C21B.FCI wendu/un1_cnt_us_18_cry_0
FCITOFCO_D  ---     0.063    R29C21B.FCI to    R29C21B.FCO wendu/SLICE_232
ROUTE         1     0.000    R29C21B.FCO to    R29C21C.FCI wendu/un1_cnt_us_18_cry_2
FCITOFCO_D  ---     0.063    R29C21C.FCI to    R29C21C.FCO wendu/SLICE_233
ROUTE         1     0.000    R29C21C.FCO to    R29C21D.FCI wendu/un1_cnt_us_18_cry_4
FCITOFCO_D  ---     0.063    R29C21D.FCI to    R29C21D.FCO wendu/SLICE_234
ROUTE         1     0.000    R29C21D.FCO to    R29C22A.FCI wendu/un1_cnt_us_18_cry_6
FCITOFCO_D  ---     0.063    R29C22A.FCI to    R29C22A.FCO wendu/SLICE_235
ROUTE         1     0.000    R29C22A.FCO to    R29C22B.FCI wendu/un1_cnt_us_18_cry_8
FCITOFCO_D  ---     0.063    R29C22B.FCI to    R29C22B.FCO wendu/SLICE_236
ROUTE         1     0.000    R29C22B.FCO to    R29C22C.FCI wendu/un1_cnt_us_18_cry_10
FCITOFCO_D  ---     0.063    R29C22C.FCI to    R29C22C.FCO wendu/SLICE_237
ROUTE         1     0.000    R29C22C.FCO to    R29C22D.FCI wendu/un1_cnt_us_18_cry_12
FCITOFCO_D  ---     0.063    R29C22D.FCI to    R29C22D.FCO wendu/SLICE_238
ROUTE         1     0.000    R29C22D.FCO to    R29C23A.FCI wendu/un1_cnt_us_18_cry_14
FCITOF0_DE  ---     0.385    R29C23A.FCI to     R29C23A.F0 wendu/SLICE_239
ROUTE         1     0.530     R29C23A.F0 to     R30C23D.C0 wendu/un1_cnt_us_18_cry_15_0_S0
CTOF_DEL    ---     0.208     R30C23D.C0 to     R30C23D.F0 wendu/SLICE_572
ROUTE         1     0.000     R30C23D.F0 to    R30C23D.DI0 wendu/cnt_us_12[15] (to wendu.clk_us)
                  --------
                    9.288   (31.6% logic, 68.4% route), 16 logic levels.

 Clock Skew Details: 

      Source Clock Path wendu/SLICE_241 to wendu/SLICE_566:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.394     R38C31A.Q0 to    R30C21D.CLK wendu.clk_us
                  --------
                    2.394   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path wendu/SLICE_241 to wendu/SLICE_572:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.394     R38C31A.Q0 to    R30C23D.CLK wendu.clk_us
                  --------
                    2.394   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 990.993ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/cnt_us[2]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/cnt_us[19]  (to wendu.clk_us +)

   Delay:               9.245ns  (33.1% logic, 66.9% route), 18 logic levels.

 Constraint Details:

      9.245ns physical path delay wendu/SLICE_566 to wendu/SLICE_573 meets
    1000.000ns delay constraint less
      0.000ns skew and
     -0.238ns DIN_SET requirement (totaling 1000.238ns) by 990.993ns

 Physical Path Details:

      Data path wendu/SLICE_566 to wendu/SLICE_573:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.457    R30C21D.CLK to     R30C21D.Q1 wendu/SLICE_566 (from wendu.clk_us)
ROUTE         7     1.371     R30C21D.Q1 to     R31C23A.A0 wendu/cnt_us[2]
CTOF_DEL    ---     0.208     R31C23A.A0 to     R31C23A.F0 wendu/SLICE_603
ROUTE         2     0.368     R31C23A.F0 to     R30C23B.D0 wendu/cnt_us_RNIM6RG[1]
CTOF_DEL    ---     0.208     R30C23B.D0 to     R30C23B.F0 wendu/SLICE_604
ROUTE         2     0.697     R30C23B.F0 to     R30C25C.C1 wendu/cnt_us_RNIRSM11[6]
CTOF_DEL    ---     0.208     R30C25C.C1 to     R30C25C.F1 wendu/SLICE_612
ROUTE         4     0.870     R30C25C.F1 to     R31C25A.B1 wendu/un1_cur_state_3_i_a2_3_o3_1
CTOF_DEL    ---     0.208     R31C25A.B1 to     R31C25A.F1 wendu/SLICE_589
ROUTE        18     1.611     R31C25A.F1 to     R30C24D.B0 wendu/un1_cur_state_3_i_a2_3_a2
CTOF_DEL    ---     0.208     R30C24D.B0 to     R30C24D.F0 wendu/SLICE_610
ROUTE         1     0.909     R30C24D.F0 to     R29C21A.A0 wendu/un1_cnt_us_0_sqmuxa_2_i
C0TOFCO_DE  ---     0.401     R29C21A.A0 to    R29C21A.FCO wendu/SLICE_231
ROUTE         1     0.000    R29C21A.FCO to    R29C21B.FCI wendu/un1_cnt_us_18_cry_0
FCITOFCO_D  ---     0.063    R29C21B.FCI to    R29C21B.FCO wendu/SLICE_232
ROUTE         1     0.000    R29C21B.FCO to    R29C21C.FCI wendu/un1_cnt_us_18_cry_2
FCITOFCO_D  ---     0.063    R29C21C.FCI to    R29C21C.FCO wendu/SLICE_233
ROUTE         1     0.000    R29C21C.FCO to    R29C21D.FCI wendu/un1_cnt_us_18_cry_4
FCITOFCO_D  ---     0.063    R29C21D.FCI to    R29C21D.FCO wendu/SLICE_234
ROUTE         1     0.000    R29C21D.FCO to    R29C22A.FCI wendu/un1_cnt_us_18_cry_6
FCITOFCO_D  ---     0.063    R29C22A.FCI to    R29C22A.FCO wendu/SLICE_235
ROUTE         1     0.000    R29C22A.FCO to    R29C22B.FCI wendu/un1_cnt_us_18_cry_8
FCITOFCO_D  ---     0.063    R29C22B.FCI to    R29C22B.FCO wendu/SLICE_236
ROUTE         1     0.000    R29C22B.FCO to    R29C22C.FCI wendu/un1_cnt_us_18_cry_10
FCITOFCO_D  ---     0.063    R29C22C.FCI to    R29C22C.FCO wendu/SLICE_237
ROUTE         1     0.000    R29C22C.FCO to    R29C22D.FCI wendu/un1_cnt_us_18_cry_12
FCITOFCO_D  ---     0.063    R29C22D.FCI to    R29C22D.FCO wendu/SLICE_238
ROUTE         1     0.000    R29C22D.FCO to    R29C23A.FCI wendu/un1_cnt_us_18_cry_14
FCITOFCO_D  ---     0.063    R29C23A.FCI to    R29C23A.FCO wendu/SLICE_239
ROUTE         1     0.000    R29C23A.FCO to    R29C23B.FCI wendu/un1_cnt_us_18_cry_16
FCITOFCO_D  ---     0.063    R29C23B.FCI to    R29C23B.FCO wendu/SLICE_240
ROUTE         1     0.000    R29C23B.FCO to    R29C23C.FCI wendu/un1_cnt_us_18_cry_18
FCITOF0_DE  ---     0.385    R29C23C.FCI to     R29C23C.F0 wendu/SLICE_225
ROUTE         1     0.361     R29C23C.F0 to     R30C23C.D1 wendu/un1_cnt_us_18_s_19_0_S0
CTOF_DEL    ---     0.208     R30C23C.D1 to     R30C23C.F1 wendu/SLICE_573
ROUTE         1     0.000     R30C23C.F1 to    R30C23C.DI1 wendu/cnt_us_12[19] (to wendu.clk_us)
                  --------
                    9.245   (33.1% logic, 66.9% route), 18 logic levels.

 Clock Skew Details: 

      Source Clock Path wendu/SLICE_241 to wendu/SLICE_566:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.394     R38C31A.Q0 to    R30C21D.CLK wendu.clk_us
                  --------
                    2.394   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path wendu/SLICE_241 to wendu/SLICE_573:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.394     R38C31A.Q0 to    R30C23C.CLK wendu.clk_us
                  --------
                    2.394   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 991.010ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/cnt_us[2]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/cnt_us[13]  (to wendu.clk_us +)

   Delay:               9.225ns  (31.1% logic, 68.9% route), 15 logic levels.

 Constraint Details:

      9.225ns physical path delay wendu/SLICE_566 to wendu/SLICE_571 meets
    1000.000ns delay constraint less
      0.000ns skew and
     -0.235ns DIN_SET requirement (totaling 1000.235ns) by 991.010ns

 Physical Path Details:

      Data path wendu/SLICE_566 to wendu/SLICE_571:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.457    R30C21D.CLK to     R30C21D.Q1 wendu/SLICE_566 (from wendu.clk_us)
ROUTE         7     1.371     R30C21D.Q1 to     R31C23A.A0 wendu/cnt_us[2]
CTOF_DEL    ---     0.208     R31C23A.A0 to     R31C23A.F0 wendu/SLICE_603
ROUTE         2     0.368     R31C23A.F0 to     R30C23B.D0 wendu/cnt_us_RNIM6RG[1]
CTOF_DEL    ---     0.208     R30C23B.D0 to     R30C23B.F0 wendu/SLICE_604
ROUTE         2     0.697     R30C23B.F0 to     R30C25C.C1 wendu/cnt_us_RNIRSM11[6]
CTOF_DEL    ---     0.208     R30C25C.C1 to     R30C25C.F1 wendu/SLICE_612
ROUTE         4     0.870     R30C25C.F1 to     R31C25A.B1 wendu/un1_cur_state_3_i_a2_3_o3_1
CTOF_DEL    ---     0.208     R31C25A.B1 to     R31C25A.F1 wendu/SLICE_589
ROUTE        18     1.611     R31C25A.F1 to     R30C24D.B0 wendu/un1_cur_state_3_i_a2_3_a2
CTOF_DEL    ---     0.208     R30C24D.B0 to     R30C24D.F0 wendu/SLICE_610
ROUTE         1     0.909     R30C24D.F0 to     R29C21A.A0 wendu/un1_cnt_us_0_sqmuxa_2_i
C0TOFCO_DE  ---     0.401     R29C21A.A0 to    R29C21A.FCO wendu/SLICE_231
ROUTE         1     0.000    R29C21A.FCO to    R29C21B.FCI wendu/un1_cnt_us_18_cry_0
FCITOFCO_D  ---     0.063    R29C21B.FCI to    R29C21B.FCO wendu/SLICE_232
ROUTE         1     0.000    R29C21B.FCO to    R29C21C.FCI wendu/un1_cnt_us_18_cry_2
FCITOFCO_D  ---     0.063    R29C21C.FCI to    R29C21C.FCO wendu/SLICE_233
ROUTE         1     0.000    R29C21C.FCO to    R29C21D.FCI wendu/un1_cnt_us_18_cry_4
FCITOFCO_D  ---     0.063    R29C21D.FCI to    R29C21D.FCO wendu/SLICE_234
ROUTE         1     0.000    R29C21D.FCO to    R29C22A.FCI wendu/un1_cnt_us_18_cry_6
FCITOFCO_D  ---     0.063    R29C22A.FCI to    R29C22A.FCO wendu/SLICE_235
ROUTE         1     0.000    R29C22A.FCO to    R29C22B.FCI wendu/un1_cnt_us_18_cry_8
FCITOFCO_D  ---     0.063    R29C22B.FCI to    R29C22B.FCO wendu/SLICE_236
ROUTE         1     0.000    R29C22B.FCO to    R29C22C.FCI wendu/un1_cnt_us_18_cry_10
FCITOFCO_D  ---     0.063    R29C22C.FCI to    R29C22C.FCO wendu/SLICE_237
ROUTE         1     0.000    R29C22C.FCO to    R29C22D.FCI wendu/un1_cnt_us_18_cry_12
FCITOF0_DE  ---     0.385    R29C22D.FCI to     R29C22D.F0 wendu/SLICE_238
ROUTE         1     0.530     R29C22D.F0 to     R30C22A.C0 wendu/un1_cnt_us_18_cry_13_0_S0
CTOF_DEL    ---     0.208     R30C22A.C0 to     R30C22A.F0 wendu/SLICE_571
ROUTE         1     0.000     R30C22A.F0 to    R30C22A.DI0 wendu/cnt_us_12[13] (to wendu.clk_us)
                  --------
                    9.225   (31.1% logic, 68.9% route), 15 logic levels.

 Clock Skew Details: 

      Source Clock Path wendu/SLICE_241 to wendu/SLICE_566:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.394     R38C31A.Q0 to    R30C21D.CLK wendu.clk_us
                  --------
                    2.394   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path wendu/SLICE_241 to wendu/SLICE_571:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.394     R38C31A.Q0 to    R30C22A.CLK wendu.clk_us
                  --------
                    2.394   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 991.028ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/cnt_us[2]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/cnt_us[8]  (to wendu.clk_us +)

   Delay:               9.210ns  (29.4% logic, 70.6% route), 12 logic levels.

 Constraint Details:

      9.210ns physical path delay wendu/SLICE_566 to wendu/SLICE_569 meets
    1000.000ns delay constraint less
      0.000ns skew and
     -0.238ns DIN_SET requirement (totaling 1000.238ns) by 991.028ns

 Physical Path Details:

      Data path wendu/SLICE_566 to wendu/SLICE_569:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.457    R30C21D.CLK to     R30C21D.Q1 wendu/SLICE_566 (from wendu.clk_us)
ROUTE         7     1.371     R30C21D.Q1 to     R31C23A.A0 wendu/cnt_us[2]
CTOF_DEL    ---     0.208     R31C23A.A0 to     R31C23A.F0 wendu/SLICE_603
ROUTE         2     0.368     R31C23A.F0 to     R30C23B.D0 wendu/cnt_us_RNIM6RG[1]
CTOF_DEL    ---     0.208     R30C23B.D0 to     R30C23B.F0 wendu/SLICE_604
ROUTE         2     0.697     R30C23B.F0 to     R30C25C.C1 wendu/cnt_us_RNIRSM11[6]
CTOF_DEL    ---     0.208     R30C25C.C1 to     R30C25C.F1 wendu/SLICE_612
ROUTE         4     0.870     R30C25C.F1 to     R31C25A.B1 wendu/un1_cur_state_3_i_a2_3_o3_1
CTOF_DEL    ---     0.208     R31C25A.B1 to     R31C25A.F1 wendu/SLICE_589
ROUTE        18     1.611     R31C25A.F1 to     R30C24D.B0 wendu/un1_cur_state_3_i_a2_3_a2
CTOF_DEL    ---     0.208     R30C24D.B0 to     R30C24D.F0 wendu/SLICE_610
ROUTE         1     0.909     R30C24D.F0 to     R29C21A.A0 wendu/un1_cnt_us_0_sqmuxa_2_i
C0TOFCO_DE  ---     0.401     R29C21A.A0 to    R29C21A.FCO wendu/SLICE_231
ROUTE         1     0.000    R29C21A.FCO to    R29C21B.FCI wendu/un1_cnt_us_18_cry_0
FCITOFCO_D  ---     0.063    R29C21B.FCI to    R29C21B.FCO wendu/SLICE_232
ROUTE         1     0.000    R29C21B.FCO to    R29C21C.FCI wendu/un1_cnt_us_18_cry_2
FCITOFCO_D  ---     0.063    R29C21C.FCI to    R29C21C.FCO wendu/SLICE_233
ROUTE         1     0.000    R29C21C.FCO to    R29C21D.FCI wendu/un1_cnt_us_18_cry_4
FCITOFCO_D  ---     0.063    R29C21D.FCI to    R29C21D.FCO wendu/SLICE_234
ROUTE         1     0.000    R29C21D.FCO to    R29C22A.FCI wendu/un1_cnt_us_18_cry_6
FCITOF1_DE  ---     0.412    R29C22A.FCI to     R29C22A.F1 wendu/SLICE_235
ROUTE         1     0.677     R29C22A.F1 to     R30C22B.B1 wendu/un1_cnt_us_18_cry_7_0_S1
CTOF_DEL    ---     0.208     R30C22B.B1 to     R30C22B.F1 wendu/SLICE_569
ROUTE         1     0.000     R30C22B.F1 to    R30C22B.DI1 wendu/cnt_us_12[8] (to wendu.clk_us)
                  --------
                    9.210   (29.4% logic, 70.6% route), 12 logic levels.

 Clock Skew Details: 

      Source Clock Path wendu/SLICE_241 to wendu/SLICE_566:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.394     R38C31A.Q0 to    R30C21D.CLK wendu.clk_us
                  --------
                    2.394   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path wendu/SLICE_241 to wendu/SLICE_569:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.394     R38C31A.Q0 to    R30C22B.CLK wendu.clk_us
                  --------
                    2.394   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 991.043ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/cnt_us[4]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/cnt_us[14]  (to wendu.clk_us +)

   Delay:               9.195ns  (31.5% logic, 68.5% route), 15 logic levels.

 Constraint Details:

      9.195ns physical path delay wendu/SLICE_567 to wendu/SLICE_571 meets
    1000.000ns delay constraint less
      0.000ns skew and
     -0.238ns DIN_SET requirement (totaling 1000.238ns) by 991.043ns

 Physical Path Details:

      Data path wendu/SLICE_567 to wendu/SLICE_571:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.457    R30C21C.CLK to     R30C21C.Q1 wendu/SLICE_567 (from wendu.clk_us)
ROUTE         8     1.007     R30C21C.Q1 to     R31C23A.D0 wendu/cnt_us[4]
CTOF_DEL    ---     0.208     R31C23A.D0 to     R31C23A.F0 wendu/SLICE_603
ROUTE         2     0.368     R31C23A.F0 to     R30C23B.D0 wendu/cnt_us_RNIM6RG[1]
CTOF_DEL    ---     0.208     R30C23B.D0 to     R30C23B.F0 wendu/SLICE_604
ROUTE         2     0.697     R30C23B.F0 to     R30C25C.C1 wendu/cnt_us_RNIRSM11[6]
CTOF_DEL    ---     0.208     R30C25C.C1 to     R30C25C.F1 wendu/SLICE_612
ROUTE         4     0.870     R30C25C.F1 to     R31C25A.B1 wendu/un1_cur_state_3_i_a2_3_o3_1
CTOF_DEL    ---     0.208     R31C25A.B1 to     R31C25A.F1 wendu/SLICE_589
ROUTE        18     1.611     R31C25A.F1 to     R30C24D.B0 wendu/un1_cur_state_3_i_a2_3_a2
CTOF_DEL    ---     0.208     R30C24D.B0 to     R30C24D.F0 wendu/SLICE_610
ROUTE         1     0.909     R30C24D.F0 to     R29C21A.A0 wendu/un1_cnt_us_0_sqmuxa_2_i
C0TOFCO_DE  ---     0.401     R29C21A.A0 to    R29C21A.FCO wendu/SLICE_231
ROUTE         1     0.000    R29C21A.FCO to    R29C21B.FCI wendu/un1_cnt_us_18_cry_0
FCITOFCO_D  ---     0.063    R29C21B.FCI to    R29C21B.FCO wendu/SLICE_232
ROUTE         1     0.000    R29C21B.FCO to    R29C21C.FCI wendu/un1_cnt_us_18_cry_2
FCITOFCO_D  ---     0.063    R29C21C.FCI to    R29C21C.FCO wendu/SLICE_233
ROUTE         1     0.000    R29C21C.FCO to    R29C21D.FCI wendu/un1_cnt_us_18_cry_4
FCITOFCO_D  ---     0.063    R29C21D.FCI to    R29C21D.FCO wendu/SLICE_234
ROUTE         1     0.000    R29C21D.FCO to    R29C22A.FCI wendu/un1_cnt_us_18_cry_6
FCITOFCO_D  ---     0.063    R29C22A.FCI to    R29C22A.FCO wendu/SLICE_235
ROUTE         1     0.000    R29C22A.FCO to    R29C22B.FCI wendu/un1_cnt_us_18_cry_8
FCITOFCO_D  ---     0.063    R29C22B.FCI to    R29C22B.FCO wendu/SLICE_236
ROUTE         1     0.000    R29C22B.FCO to    R29C22C.FCI wendu/un1_cnt_us_18_cry_10
FCITOFCO_D  ---     0.063    R29C22C.FCI to    R29C22C.FCO wendu/SLICE_237
ROUTE         1     0.000    R29C22C.FCO to    R29C22D.FCI wendu/un1_cnt_us_18_cry_12
FCITOF1_DE  ---     0.412    R29C22D.FCI to     R29C22D.F1 wendu/SLICE_238
ROUTE         1     0.837     R29C22D.F1 to     R30C22A.B1 wendu/un1_cnt_us_18_cry_13_0_S1
CTOF_DEL    ---     0.208     R30C22A.B1 to     R30C22A.F1 wendu/SLICE_571
ROUTE         1     0.000     R30C22A.F1 to    R30C22A.DI1 wendu/cnt_us_12[14] (to wendu.clk_us)
                  --------
                    9.195   (31.5% logic, 68.5% route), 15 logic levels.

 Clock Skew Details: 

      Source Clock Path wendu/SLICE_241 to wendu/SLICE_567:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.394     R38C31A.Q0 to    R30C21C.CLK wendu.clk_us
                  --------
                    2.394   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path wendu/SLICE_241 to wendu/SLICE_571:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.394     R38C31A.Q0 to    R30C22A.CLK wendu.clk_us
                  --------
                    2.394   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 991.086ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/cnt_us[2]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/cnt_us[6]  (to wendu.clk_us +)

   Delay:               9.152ns  (28.9% logic, 71.1% route), 11 logic levels.

 Constraint Details:

      9.152ns physical path delay wendu/SLICE_566 to wendu/SLICE_568 meets
    1000.000ns delay constraint less
      0.000ns skew and
     -0.238ns DIN_SET requirement (totaling 1000.238ns) by 991.086ns

 Physical Path Details:

      Data path wendu/SLICE_566 to wendu/SLICE_568:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.457    R30C21D.CLK to     R30C21D.Q1 wendu/SLICE_566 (from wendu.clk_us)
ROUTE         7     1.371     R30C21D.Q1 to     R31C23A.A0 wendu/cnt_us[2]
CTOF_DEL    ---     0.208     R31C23A.A0 to     R31C23A.F0 wendu/SLICE_603
ROUTE         2     0.368     R31C23A.F0 to     R30C23B.D0 wendu/cnt_us_RNIM6RG[1]
CTOF_DEL    ---     0.208     R30C23B.D0 to     R30C23B.F0 wendu/SLICE_604
ROUTE         2     0.697     R30C23B.F0 to     R30C25C.C1 wendu/cnt_us_RNIRSM11[6]
CTOF_DEL    ---     0.208     R30C25C.C1 to     R30C25C.F1 wendu/SLICE_612
ROUTE         4     0.870     R30C25C.F1 to     R31C25A.B1 wendu/un1_cur_state_3_i_a2_3_o3_1
CTOF_DEL    ---     0.208     R31C25A.B1 to     R31C25A.F1 wendu/SLICE_589
ROUTE        18     1.611     R31C25A.F1 to     R30C24D.B0 wendu/un1_cur_state_3_i_a2_3_a2
CTOF_DEL    ---     0.208     R30C24D.B0 to     R30C24D.F0 wendu/SLICE_610
ROUTE         1     0.909     R30C24D.F0 to     R29C21A.A0 wendu/un1_cnt_us_0_sqmuxa_2_i
C0TOFCO_DE  ---     0.401     R29C21A.A0 to    R29C21A.FCO wendu/SLICE_231
ROUTE         1     0.000    R29C21A.FCO to    R29C21B.FCI wendu/un1_cnt_us_18_cry_0
FCITOFCO_D  ---     0.063    R29C21B.FCI to    R29C21B.FCO wendu/SLICE_232
ROUTE         1     0.000    R29C21B.FCO to    R29C21C.FCI wendu/un1_cnt_us_18_cry_2
FCITOFCO_D  ---     0.063    R29C21C.FCI to    R29C21C.FCO wendu/SLICE_233
ROUTE         1     0.000    R29C21C.FCO to    R29C21D.FCI wendu/un1_cnt_us_18_cry_4
FCITOF1_DE  ---     0.412    R29C21D.FCI to     R29C21D.F1 wendu/SLICE_234
ROUTE         1     0.682     R29C21D.F1 to     R30C21B.A1 wendu/un1_cnt_us_18_cry_5_0_S1
CTOF_DEL    ---     0.208     R30C21B.A1 to     R30C21B.F1 wendu/SLICE_568
ROUTE         1     0.000     R30C21B.F1 to    R30C21B.DI1 wendu/cnt_us_12[6] (to wendu.clk_us)
                  --------
                    9.152   (28.9% logic, 71.1% route), 11 logic levels.

 Clock Skew Details: 

      Source Clock Path wendu/SLICE_241 to wendu/SLICE_566:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.394     R38C31A.Q0 to    R30C21D.CLK wendu.clk_us
                  --------
                    2.394   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path wendu/SLICE_241 to wendu/SLICE_568:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.394     R38C31A.Q0 to    R30C21B.CLK wendu.clk_us
                  --------
                    2.394   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 991.087ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/cnt_us[10]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/cnt_us[14]  (to wendu.clk_us +)

   Delay:               9.151ns  (36.2% logic, 63.8% route), 17 logic levels.

 Constraint Details:

      9.151ns physical path delay wendu/SLICE_570 to wendu/SLICE_571 meets
    1000.000ns delay constraint less
      0.000ns skew and
     -0.238ns DIN_SET requirement (totaling 1000.238ns) by 991.087ns

 Physical Path Details:

      Data path wendu/SLICE_570 to wendu/SLICE_571:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.457    R30C22D.CLK to     R30C22D.Q1 wendu/SLICE_570 (from wendu.clk_us)
ROUTE         3     0.901     R30C22D.Q1 to     R29C23D.A1 wendu/cnt_us[10]
CTOF_DEL    ---     0.208     R29C23D.A1 to     R29C23D.F1 wendu/SLICE_620
ROUTE         1     0.488     R29C23D.F1 to     R29C23D.A0 wendu/un1_cur_state_3_i_a2_3_o3_5_4
CTOF_DEL    ---     0.208     R29C23D.A0 to     R29C23D.F0 wendu/SLICE_620
ROUTE         3     0.580     R29C23D.F0 to     R31C25C.D0 wendu/un1_cur_state_3_i_a2_3_o3_5
CTOF_DEL    ---     0.208     R31C25C.D0 to     R31C25C.F0 wendu/SLICE_614
ROUTE         3     0.543     R31C25C.F0 to     R31C23B.C0 wendu/un1_cur_state_3_i_a2_3_o3_0
CTOF_DEL    ---     0.208     R31C23B.C0 to     R31C23B.F0 wendu/SLICE_611
ROUTE         2     0.373     R31C23B.F0 to     R31C24B.D1 wendu/data_temp_1_sqmuxa_0_o3_0
CTOF_DEL    ---     0.208     R31C24B.D1 to     R31C24B.F1 wendu/SLICE_560
ROUTE         3     0.701     R31C24B.F1 to     R30C24D.A1 wendu/data_temp_1_sqmuxa_0_o3_0_RNIT8UQ
CTOF_DEL    ---     0.208     R30C24D.A1 to     R30C24D.F1 wendu/SLICE_610
ROUTE        19     0.507     R30C24D.F1 to     R30C24D.A0 wendu/N_93_i
CTOF_DEL    ---     0.208     R30C24D.A0 to     R30C24D.F0 wendu/SLICE_610
ROUTE         1     0.909     R30C24D.F0 to     R29C21A.A0 wendu/un1_cnt_us_0_sqmuxa_2_i
C0TOFCO_DE  ---     0.401     R29C21A.A0 to    R29C21A.FCO wendu/SLICE_231
ROUTE         1     0.000    R29C21A.FCO to    R29C21B.FCI wendu/un1_cnt_us_18_cry_0
FCITOFCO_D  ---     0.063    R29C21B.FCI to    R29C21B.FCO wendu/SLICE_232
ROUTE         1     0.000    R29C21B.FCO to    R29C21C.FCI wendu/un1_cnt_us_18_cry_2
FCITOFCO_D  ---     0.063    R29C21C.FCI to    R29C21C.FCO wendu/SLICE_233
ROUTE         1     0.000    R29C21C.FCO to    R29C21D.FCI wendu/un1_cnt_us_18_cry_4
FCITOFCO_D  ---     0.063    R29C21D.FCI to    R29C21D.FCO wendu/SLICE_234
ROUTE         1     0.000    R29C21D.FCO to    R29C22A.FCI wendu/un1_cnt_us_18_cry_6
FCITOFCO_D  ---     0.063    R29C22A.FCI to    R29C22A.FCO wendu/SLICE_235
ROUTE         1     0.000    R29C22A.FCO to    R29C22B.FCI wendu/un1_cnt_us_18_cry_8
FCITOFCO_D  ---     0.063    R29C22B.FCI to    R29C22B.FCO wendu/SLICE_236
ROUTE         1     0.000    R29C22B.FCO to    R29C22C.FCI wendu/un1_cnt_us_18_cry_10
FCITOFCO_D  ---     0.063    R29C22C.FCI to    R29C22C.FCO wendu/SLICE_237
ROUTE         1     0.000    R29C22C.FCO to    R29C22D.FCI wendu/un1_cnt_us_18_cry_12
FCITOF1_DE  ---     0.412    R29C22D.FCI to     R29C22D.F1 wendu/SLICE_238
ROUTE         1     0.837     R29C22D.F1 to     R30C22A.B1 wendu/un1_cnt_us_18_cry_13_0_S1
CTOF_DEL    ---     0.208     R30C22A.B1 to     R30C22A.F1 wendu/SLICE_571
ROUTE         1     0.000     R30C22A.F1 to    R30C22A.DI1 wendu/cnt_us_12[14] (to wendu.clk_us)
                  --------
                    9.151   (36.2% logic, 63.8% route), 17 logic levels.

 Clock Skew Details: 

      Source Clock Path wendu/SLICE_241 to wendu/SLICE_570:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.394     R38C31A.Q0 to    R30C22D.CLK wendu.clk_us
                  --------
                    2.394   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path wendu/SLICE_241 to wendu/SLICE_571:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.394     R38C31A.Q0 to    R30C22A.CLK wendu.clk_us
                  --------
                    2.394   (0.0% logic, 100.0% route), 0 logic levels.

Report:  107.285MHz is the maximum frequency for this preference.


================================================================================
Preference: FREQUENCY NET "clk_in_c" 20.000000 MHz ;
            0 items scored, 0 timing errors detected.
--------------------------------------------------------------------------------


================================================================================
Preference: FREQUENCY NET "clk_AD" 60.000000 MHz ;
            3104 items scored, 0 timing errors detected.
--------------------------------------------------------------------------------


Passed: The following path meets requirements by 4.979ns (weighted slack = 9.958ns)

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/Read_en  (from clk120mhz +)
   Destination:    PDPW16KD   Port           signal_process/demodu/fifo/pdp_ram_0_0_1(ASIC)  (to clk_AD +)

   Delay:               3.132ns  (21.3% logic, 78.7% route), 2 logic levels.

 Constraint Details:

      3.132ns physical path delay signal_process/demodu/SLICE_288 to signal_process/demodu/fifo/pdp_ram_0_0_1 meets
      8.334ns delay constraint less
     -0.089ns skew and
      0.000ns feedback compensation and
      0.312ns CE_SET requirement (totaling 8.111ns) by 4.979ns

 Physical Path Details:

      Data path signal_process/demodu/SLICE_288 to signal_process/demodu/fifo/pdp_ram_0_0_1:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.460    R41C20D.CLK to     R41C20D.Q0 signal_process/demodu/SLICE_288 (from clk120mhz)
ROUTE         4     0.371     R41C20D.Q0 to     R40C20D.D0 signal_process/demodu/Read_en
CTOF_DEL    ---     0.208     R40C20D.D0 to     R40C20D.F0 signal_process/demodu/fifo/SLICE_643
ROUTE         9     2.093     R40C20D.F0 to EBR_R37C13.CER signal_process/demodu/fifo/rden_i (to clk_AD)
                  --------
                    3.132   (21.3% logic, 78.7% route), 2 logic levels.

 Clock Skew Details: 

      Source Clock Path clk_in to signal_process/demodu/SLICE_288:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI clk_in
ROUTE         1     0.350      L16.PADDI to   PLL_BR0.CLKI clk_in_c
CLKI2OP_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOP CLK120/PLLInst_0
ROUTE       446     1.890  PLL_BR0.CLKOP to    R41C20D.CLK clk120mhz
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Source Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP CLK120/PLLInst_0
ROUTE       446     1.979  PLL_BR0.CLKOP to  PLL_BR0.CLKFB clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.

      Destination Clock Path clk_in to signal_process/demodu/fifo/pdp_ram_0_0_1:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI clk_in
ROUTE         1     0.350      L16.PADDI to   PLL_BR0.CLKI clk_in_c
CLKI2OS_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOS CLK120/PLLInst_0
ROUTE       101     1.979  PLL_BR0.CLKOS to *R_R37C13.CLKR clk_AD
                  --------
                    3.384   (31.2% logic, 68.8% route), 2 logic levels.

      Destination Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP CLK120/PLLInst_0
ROUTE       446     1.979  PLL_BR0.CLKOP to  PLL_BR0.CLKFB clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.


Passed: The following path meets requirements by 5.068ns (weighted slack = 10.136ns)

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/Read_en  (from clk120mhz +)
   Destination:    PDPW16KD   Port           signal_process/demodu/fifo/pdp_ram_0_0_1(ASIC)  (to clk_AD +)

   Delay:               3.132ns  (21.3% logic, 78.7% route), 2 logic levels.

 Constraint Details:

      3.132ns physical path delay signal_process/demodu/SLICE_288 to signal_process/demodu/fifo/pdp_ram_0_0_1 meets
      8.334ns delay constraint less
     -0.089ns skew and
      0.000ns feedback compensation and
      0.223ns CE_SET requirement (totaling 8.200ns) by 5.068ns

 Physical Path Details:

      Data path signal_process/demodu/SLICE_288 to signal_process/demodu/fifo/pdp_ram_0_0_1:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.460    R41C20D.CLK to     R41C20D.Q0 signal_process/demodu/SLICE_288 (from clk120mhz)
ROUTE         4     0.371     R41C20D.Q0 to     R40C20D.D0 signal_process/demodu/Read_en
CTOF_DEL    ---     0.208     R40C20D.D0 to     R40C20D.F0 signal_process/demodu/fifo/SLICE_643
ROUTE         9     2.093     R40C20D.F0 to *R_R37C13.OCER signal_process/demodu/fifo/rden_i (to clk_AD)
                  --------
                    3.132   (21.3% logic, 78.7% route), 2 logic levels.

 Clock Skew Details: 

      Source Clock Path clk_in to signal_process/demodu/SLICE_288:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI clk_in
ROUTE         1     0.350      L16.PADDI to   PLL_BR0.CLKI clk_in_c
CLKI2OP_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOP CLK120/PLLInst_0
ROUTE       446     1.890  PLL_BR0.CLKOP to    R41C20D.CLK clk120mhz
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Source Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP CLK120/PLLInst_0
ROUTE       446     1.979  PLL_BR0.CLKOP to  PLL_BR0.CLKFB clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.

      Destination Clock Path clk_in to signal_process/demodu/fifo/pdp_ram_0_0_1:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI clk_in
ROUTE         1     0.350      L16.PADDI to   PLL_BR0.CLKI clk_in_c
CLKI2OS_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOS CLK120/PLLInst_0
ROUTE       101     1.979  PLL_BR0.CLKOS to *R_R37C13.CLKR clk_AD
                  --------
                    3.384   (31.2% logic, 68.8% route), 2 logic levels.

      Destination Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP CLK120/PLLInst_0
ROUTE       446     1.979  PLL_BR0.CLKOP to  PLL_BR0.CLKFB clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.


Passed: The following path meets requirements by 5.478ns (weighted slack = 10.956ns)

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/Read_en  (from clk120mhz +)
   Destination:    FF         Data in        signal_process/demodu/fifo/FF_19  (to clk_AD +)

   Delay:               3.091ns  (59.9% logic, 40.1% route), 8 logic levels.

 Constraint Details:

      3.091ns physical path delay signal_process/demodu/SLICE_288 to signal_process/demodu/fifo/SLICE_298 meets
      8.334ns delay constraint less
      0.000ns skew and
      0.000ns feedback compensation and
     -0.235ns DIN_SET requirement (totaling 8.569ns) by 5.478ns

 Physical Path Details:

      Data path signal_process/demodu/SLICE_288 to signal_process/demodu/fifo/SLICE_298:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.460    R41C20D.CLK to     R41C20D.Q0 signal_process/demodu/SLICE_288 (from clk120mhz)
ROUTE         4     0.371     R41C20D.Q0 to     R40C20D.D0 signal_process/demodu/Read_en
CTOF_DEL    ---     0.208     R40C20D.D0 to     R40C20D.F0 signal_process/demodu/fifo/SLICE_643
ROUTE         9     0.702     R40C20D.F0 to     R40C19B.B0 signal_process/demodu/fifo/rden_i
C0TOFCO_DE  ---     0.401     R40C19B.B0 to    R40C19B.FCO signal_process/demodu/fifo/SLICE_126
ROUTE         1     0.000    R40C19B.FCO to    R40C19C.FCI signal_process/demodu/fifo/co0_1
FCITOFCO_D  ---     0.063    R40C19C.FCI to    R40C19C.FCO signal_process/demodu/fifo/SLICE_127
ROUTE         1     0.000    R40C19C.FCO to    R40C19D.FCI signal_process/demodu/fifo/co1_1
FCITOFCO_D  ---     0.063    R40C19D.FCI to    R40C19D.FCO signal_process/demodu/fifo/SLICE_128
ROUTE         1     0.000    R40C19D.FCO to    R40C20A.FCI signal_process/demodu/fifo/co2_1
FCITOFCO_D  ---     0.063    R40C20A.FCI to    R40C20A.FCO signal_process/demodu/fifo/SLICE_129
ROUTE         1     0.000    R40C20A.FCO to    R40C20B.FCI signal_process/demodu/fifo/cmp_le_1_c
FCITOF0_DE  ---     0.385    R40C20B.FCI to     R40C20B.F0 signal_process/demodu/fifo/SLICE_130
ROUTE         1     0.167     R40C20B.F0 to     R40C20C.D0 signal_process/demodu/fifo/cmp_le_1
CTOF_DEL    ---     0.208     R40C20C.D0 to     R40C20C.F0 signal_process/demodu/fifo/SLICE_298
ROUTE         1     0.000     R40C20C.F0 to    R40C20C.DI0 signal_process/demodu/fifo/empty_d (to clk_AD)
                  --------
                    3.091   (59.9% logic, 40.1% route), 8 logic levels.

 Clock Skew Details: 

      Source Clock Path clk_in to signal_process/demodu/SLICE_288:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI clk_in
ROUTE         1     0.350      L16.PADDI to   PLL_BR0.CLKI clk_in_c
CLKI2OP_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOP CLK120/PLLInst_0
ROUTE       446     1.890  PLL_BR0.CLKOP to    R41C20D.CLK clk120mhz
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Source Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP CLK120/PLLInst_0
ROUTE       446     1.979  PLL_BR0.CLKOP to  PLL_BR0.CLKFB clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.

      Destination Clock Path clk_in to signal_process/demodu/fifo/SLICE_298:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI clk_in
ROUTE         1     0.350      L16.PADDI to   PLL_BR0.CLKI clk_in_c
CLKI2OS_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOS CLK120/PLLInst_0
ROUTE       101     1.890  PLL_BR0.CLKOS to    R40C20C.CLK clk_AD
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Destination Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP CLK120/PLLInst_0
ROUTE       446     1.979  PLL_BR0.CLKOP to  PLL_BR0.CLKFB clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.


Passed: The following path meets requirements by 5.483ns (weighted slack = 10.966ns)

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/Read_en  (from clk120mhz +)
   Destination:    PDPW16KD   Port           signal_process/demodu/fifo/pdp_ram_0_1_0(ASIC)  (to clk_AD +)

   Delay:               2.628ns  (25.4% logic, 74.6% route), 2 logic levels.

 Constraint Details:

      2.628ns physical path delay signal_process/demodu/SLICE_288 to signal_process/demodu/fifo/pdp_ram_0_1_0 meets
      8.334ns delay constraint less
     -0.089ns skew and
      0.000ns feedback compensation and
      0.312ns CE_SET requirement (totaling 8.111ns) by 5.483ns

 Physical Path Details:

      Data path signal_process/demodu/SLICE_288 to signal_process/demodu/fifo/pdp_ram_0_1_0:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.460    R41C20D.CLK to     R41C20D.Q0 signal_process/demodu/SLICE_288 (from clk120mhz)
ROUTE         4     0.371     R41C20D.Q0 to     R40C20D.D0 signal_process/demodu/Read_en
CTOF_DEL    ---     0.208     R40C20D.D0 to     R40C20D.F0 signal_process/demodu/fifo/SLICE_643
ROUTE         9     1.589     R40C20D.F0 to EBR_R37C15.CER signal_process/demodu/fifo/rden_i (to clk_AD)
                  --------
                    2.628   (25.4% logic, 74.6% route), 2 logic levels.

 Clock Skew Details: 

      Source Clock Path clk_in to signal_process/demodu/SLICE_288:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI clk_in
ROUTE         1     0.350      L16.PADDI to   PLL_BR0.CLKI clk_in_c
CLKI2OP_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOP CLK120/PLLInst_0
ROUTE       446     1.890  PLL_BR0.CLKOP to    R41C20D.CLK clk120mhz
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Source Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP CLK120/PLLInst_0
ROUTE       446     1.979  PLL_BR0.CLKOP to  PLL_BR0.CLKFB clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.

      Destination Clock Path clk_in to signal_process/demodu/fifo/pdp_ram_0_1_0:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI clk_in
ROUTE         1     0.350      L16.PADDI to   PLL_BR0.CLKI clk_in_c
CLKI2OS_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOS CLK120/PLLInst_0
ROUTE       101     1.979  PLL_BR0.CLKOS to *R_R37C15.CLKR clk_AD
                  --------
                    3.384   (31.2% logic, 68.8% route), 2 logic levels.

      Destination Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP CLK120/PLLInst_0
ROUTE       446     1.979  PLL_BR0.CLKOP to  PLL_BR0.CLKFB clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.


Passed: The following path meets requirements by 11.047ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/AD_validcnt[6]  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/sample_sum[52]  (to clk_AD +)
                   FF                        signal_process/demodu/sample_sum[51]

   Delay:               5.738ns  (15.2% logic, 84.8% route), 3 logic levels.

 Constraint Details:

      5.738ns physical path delay signal_process/demodu/SLICE_207 to signal_process/demodu/SLICE_201 meets
     16.667ns delay constraint less
      0.000ns skew and
     -0.118ns CE_SET requirement (totaling 16.785ns) by 11.047ns

 Physical Path Details:

      Data path signal_process/demodu/SLICE_207 to signal_process/demodu/SLICE_201:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.457    R22C24D.CLK to     R22C24D.Q1 signal_process/demodu/SLICE_207 (from clk_AD)
ROUTE         2     0.684     R22C24D.Q1 to     R23C24C.B1 signal_process/demodu/AD_validcnt[6]
CTOF_DEL    ---     0.208     R23C24C.B1 to     R23C24C.F1 signal_process/demodu/SLICE_594
ROUTE         4     0.507     R23C24C.F1 to     R23C24C.A0 signal_process/demodu/un1_AD_validcntlto7_i_o4
CTOF_DEL    ---     0.208     R23C24C.A0 to     R23C24C.F0 signal_process/demodu/SLICE_594
ROUTE        29     3.674     R23C24C.F0 to     R33C17C.CE signal_process/demodu/N_410_i (to clk_AD)
                  --------
                    5.738   (15.2% logic, 84.8% route), 3 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_207:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     1.890  PLL_BR0.CLKOS to    R22C24D.CLK clk_AD
                  --------
                    1.890   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_201:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     1.890  PLL_BR0.CLKOS to    R33C17C.CLK clk_AD
                  --------
                    1.890   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 11.047ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/AD_validcnt[6]  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/sample_sum[48]  (to clk_AD +)
                   FF                        signal_process/demodu/sample_sum[47]

   Delay:               5.738ns  (15.2% logic, 84.8% route), 3 logic levels.

 Constraint Details:

      5.738ns physical path delay signal_process/demodu/SLICE_207 to signal_process/demodu/SLICE_199 meets
     16.667ns delay constraint less
      0.000ns skew and
     -0.118ns CE_SET requirement (totaling 16.785ns) by 11.047ns

 Physical Path Details:

      Data path signal_process/demodu/SLICE_207 to signal_process/demodu/SLICE_199:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.457    R22C24D.CLK to     R22C24D.Q1 signal_process/demodu/SLICE_207 (from clk_AD)
ROUTE         2     0.684     R22C24D.Q1 to     R23C24C.B1 signal_process/demodu/AD_validcnt[6]
CTOF_DEL    ---     0.208     R23C24C.B1 to     R23C24C.F1 signal_process/demodu/SLICE_594
ROUTE         4     0.507     R23C24C.F1 to     R23C24C.A0 signal_process/demodu/un1_AD_validcntlto7_i_o4
CTOF_DEL    ---     0.208     R23C24C.A0 to     R23C24C.F0 signal_process/demodu/SLICE_594
ROUTE        29     3.674     R23C24C.F0 to     R33C17A.CE signal_process/demodu/N_410_i (to clk_AD)
                  --------
                    5.738   (15.2% logic, 84.8% route), 3 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_207:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     1.890  PLL_BR0.CLKOS to    R22C24D.CLK clk_AD
                  --------
                    1.890   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_199:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     1.890  PLL_BR0.CLKOS to    R33C17A.CLK clk_AD
                  --------
                    1.890   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 11.047ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/AD_validcnt[6]  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/sample_sum[44]  (to clk_AD +)
                   FF                        signal_process/demodu/sample_sum[43]

   Delay:               5.738ns  (15.2% logic, 84.8% route), 3 logic levels.

 Constraint Details:

      5.738ns physical path delay signal_process/demodu/SLICE_207 to signal_process/demodu/SLICE_197 meets
     16.667ns delay constraint less
      0.000ns skew and
     -0.118ns CE_SET requirement (totaling 16.785ns) by 11.047ns

 Physical Path Details:

      Data path signal_process/demodu/SLICE_207 to signal_process/demodu/SLICE_197:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.457    R22C24D.CLK to     R22C24D.Q1 signal_process/demodu/SLICE_207 (from clk_AD)
ROUTE         2     0.684     R22C24D.Q1 to     R23C24C.B1 signal_process/demodu/AD_validcnt[6]
CTOF_DEL    ---     0.208     R23C24C.B1 to     R23C24C.F1 signal_process/demodu/SLICE_594
ROUTE         4     0.507     R23C24C.F1 to     R23C24C.A0 signal_process/demodu/un1_AD_validcntlto7_i_o4
CTOF_DEL    ---     0.208     R23C24C.A0 to     R23C24C.F0 signal_process/demodu/SLICE_594
ROUTE        29     3.674     R23C24C.F0 to     R33C16C.CE signal_process/demodu/N_410_i (to clk_AD)
                  --------
                    5.738   (15.2% logic, 84.8% route), 3 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_207:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     1.890  PLL_BR0.CLKOS to    R22C24D.CLK clk_AD
                  --------
                    1.890   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_197:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     1.890  PLL_BR0.CLKOS to    R33C16C.CLK clk_AD
                  --------
                    1.890   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 11.047ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/AD_validcnt[6]  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/sample_sum[40]  (to clk_AD +)
                   FF                        signal_process/demodu/sample_sum[39]

   Delay:               5.738ns  (15.2% logic, 84.8% route), 3 logic levels.

 Constraint Details:

      5.738ns physical path delay signal_process/demodu/SLICE_207 to signal_process/demodu/SLICE_195 meets
     16.667ns delay constraint less
      0.000ns skew and
     -0.118ns CE_SET requirement (totaling 16.785ns) by 11.047ns

 Physical Path Details:

      Data path signal_process/demodu/SLICE_207 to signal_process/demodu/SLICE_195:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.457    R22C24D.CLK to     R22C24D.Q1 signal_process/demodu/SLICE_207 (from clk_AD)
ROUTE         2     0.684     R22C24D.Q1 to     R23C24C.B1 signal_process/demodu/AD_validcnt[6]
CTOF_DEL    ---     0.208     R23C24C.B1 to     R23C24C.F1 signal_process/demodu/SLICE_594
ROUTE         4     0.507     R23C24C.F1 to     R23C24C.A0 signal_process/demodu/un1_AD_validcntlto7_i_o4
CTOF_DEL    ---     0.208     R23C24C.A0 to     R23C24C.F0 signal_process/demodu/SLICE_594
ROUTE        29     3.674     R23C24C.F0 to     R33C16A.CE signal_process/demodu/N_410_i (to clk_AD)
                  --------
                    5.738   (15.2% logic, 84.8% route), 3 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_207:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     1.890  PLL_BR0.CLKOS to    R22C24D.CLK clk_AD
                  --------
                    1.890   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_195:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     1.890  PLL_BR0.CLKOS to    R33C16A.CLK clk_AD
                  --------
                    1.890   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 11.047ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/AD_validcnt[6]  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/sample_sum[36]  (to clk_AD +)
                   FF                        signal_process/demodu/sample_sum[35]

   Delay:               5.738ns  (15.2% logic, 84.8% route), 3 logic levels.

 Constraint Details:

      5.738ns physical path delay signal_process/demodu/SLICE_207 to signal_process/demodu/SLICE_193 meets
     16.667ns delay constraint less
      0.000ns skew and
     -0.118ns CE_SET requirement (totaling 16.785ns) by 11.047ns

 Physical Path Details:

      Data path signal_process/demodu/SLICE_207 to signal_process/demodu/SLICE_193:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.457    R22C24D.CLK to     R22C24D.Q1 signal_process/demodu/SLICE_207 (from clk_AD)
ROUTE         2     0.684     R22C24D.Q1 to     R23C24C.B1 signal_process/demodu/AD_validcnt[6]
CTOF_DEL    ---     0.208     R23C24C.B1 to     R23C24C.F1 signal_process/demodu/SLICE_594
ROUTE         4     0.507     R23C24C.F1 to     R23C24C.A0 signal_process/demodu/un1_AD_validcntlto7_i_o4
CTOF_DEL    ---     0.208     R23C24C.A0 to     R23C24C.F0 signal_process/demodu/SLICE_594
ROUTE        29     3.674     R23C24C.F0 to     R33C15C.CE signal_process/demodu/N_410_i (to clk_AD)
                  --------
                    5.738   (15.2% logic, 84.8% route), 3 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_207:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     1.890  PLL_BR0.CLKOS to    R22C24D.CLK clk_AD
                  --------
                    1.890   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_193:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     1.890  PLL_BR0.CLKOS to    R33C15C.CLK clk_AD
                  --------
                    1.890   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 11.047ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/AD_validcnt[6]  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/sample_sum[55]  (to clk_AD +)

   Delay:               5.738ns  (15.2% logic, 84.8% route), 3 logic levels.

 Constraint Details:

      5.738ns physical path delay signal_process/demodu/SLICE_207 to signal_process/demodu/SLICE_203 meets
     16.667ns delay constraint less
      0.000ns skew and
     -0.118ns CE_SET requirement (totaling 16.785ns) by 11.047ns

 Physical Path Details:

      Data path signal_process/demodu/SLICE_207 to signal_process/demodu/SLICE_203:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.457    R22C24D.CLK to     R22C24D.Q1 signal_process/demodu/SLICE_207 (from clk_AD)
ROUTE         2     0.684     R22C24D.Q1 to     R23C24C.B1 signal_process/demodu/AD_validcnt[6]
CTOF_DEL    ---     0.208     R23C24C.B1 to     R23C24C.F1 signal_process/demodu/SLICE_594
ROUTE         4     0.507     R23C24C.F1 to     R23C24C.A0 signal_process/demodu/un1_AD_validcntlto7_i_o4
CTOF_DEL    ---     0.208     R23C24C.A0 to     R23C24C.F0 signal_process/demodu/SLICE_594
ROUTE        29     3.674     R23C24C.F0 to     R33C18A.CE signal_process/demodu/N_410_i (to clk_AD)
                  --------
                    5.738   (15.2% logic, 84.8% route), 3 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_207:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     1.890  PLL_BR0.CLKOS to    R22C24D.CLK clk_AD
                  --------
                    1.890   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_203:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     1.890  PLL_BR0.CLKOS to    R33C18A.CLK clk_AD
                  --------
                    1.890   (0.0% logic, 100.0% route), 0 logic levels.

Report:  149.031MHz is the maximum frequency for this preference.


================================================================================
Preference: FREQUENCY NET "clk_AD_t_c" 60.000000 MHz ;
            0 items scored, 0 timing errors detected.
--------------------------------------------------------------------------------


================================================================================
Preference: BLOCK PATH FROM CLKNET "clk_AD" TO CLKNET "clk120mhz" ;
            1596 items scored.
--------------------------------------------------------------------------------


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         PDPW16KD   Port           signal_process/demodu/fifo/pdp_ram_0_0_1(ASIC)  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/INS_dout[54]  (to clk120mhz +)

   Delay:               8.793ns  (83.8% logic, 16.2% route), 28 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
C2Q_DEL     ---     4.979 *R_R37C13.CLKR to *R_R37C13.DO19 signal_process/demodu/fifo/pdp_ram_0_0_1 (from clk_AD)
ROUTE         1     1.426 *R_R37C13.DO19 to     R38C12B.A0 signal_process/demodu/sample_sum_DY[1]
C0TOFCO_DE  ---     0.401     R38C12B.A0 to    R38C12B.FCO signal_process/demodu/SLICE_148
ROUTE         1     0.000    R38C12B.FCO to    R38C12C.FCI signal_process/demodu/INS_dout_1_cry_2
FCITOFCO_D  ---     0.063    R38C12C.FCI to    R38C12C.FCO signal_process/demodu/SLICE_149
ROUTE         1     0.000    R38C12C.FCO to    R38C12D.FCI signal_process/demodu/INS_dout_1_cry_4
FCITOFCO_D  ---     0.063    R38C12D.FCI to    R38C12D.FCO signal_process/demodu/SLICE_150
ROUTE         1     0.000    R38C12D.FCO to    R38C13A.FCI signal_process/demodu/INS_dout_1_cry_6
FCITOFCO_D  ---     0.063    R38C13A.FCI to    R38C13A.FCO signal_process/demodu/SLICE_151
ROUTE         1     0.000    R38C13A.FCO to    R38C13B.FCI signal_process/demodu/INS_dout_1_cry_8
FCITOFCO_D  ---     0.063    R38C13B.FCI to    R38C13B.FCO signal_process/demodu/SLICE_152
ROUTE         1     0.000    R38C13B.FCO to    R38C13C.FCI signal_process/demodu/INS_dout_1_cry_10
FCITOFCO_D  ---     0.063    R38C13C.FCI to    R38C13C.FCO signal_process/demodu/SLICE_153
ROUTE         1     0.000    R38C13C.FCO to    R38C13D.FCI signal_process/demodu/INS_dout_1_cry_12
FCITOFCO_D  ---     0.063    R38C13D.FCI to    R38C13D.FCO signal_process/demodu/SLICE_154
ROUTE         1     0.000    R38C13D.FCO to    R38C14A.FCI signal_process/demodu/INS_dout_1_cry_14
FCITOFCO_D  ---     0.063    R38C14A.FCI to    R38C14A.FCO signal_process/demodu/SLICE_155
ROUTE         1     0.000    R38C14A.FCO to    R38C14B.FCI signal_process/demodu/INS_dout_1_cry_16
FCITOFCO_D  ---     0.063    R38C14B.FCI to    R38C14B.FCO signal_process/demodu/SLICE_156
ROUTE         1     0.000    R38C14B.FCO to    R38C14C.FCI signal_process/demodu/INS_dout_1_cry_18
FCITOFCO_D  ---     0.063    R38C14C.FCI to    R38C14C.FCO signal_process/demodu/SLICE_157
ROUTE         1     0.000    R38C14C.FCO to    R38C14D.FCI signal_process/demodu/INS_dout_1_cry_20
FCITOFCO_D  ---     0.063    R38C14D.FCI to    R38C14D.FCO signal_process/demodu/SLICE_158
ROUTE         1     0.000    R38C14D.FCO to    R38C15A.FCI signal_process/demodu/INS_dout_1_cry_22
FCITOFCO_D  ---     0.063    R38C15A.FCI to    R38C15A.FCO signal_process/demodu/SLICE_159
ROUTE         1     0.000    R38C15A.FCO to    R38C15B.FCI signal_process/demodu/INS_dout_1_cry_24
FCITOFCO_D  ---     0.063    R38C15B.FCI to    R38C15B.FCO signal_process/demodu/SLICE_160
ROUTE         1     0.000    R38C15B.FCO to    R38C15C.FCI signal_process/demodu/INS_dout_1_cry_26
FCITOFCO_D  ---     0.063    R38C15C.FCI to    R38C15C.FCO signal_process/demodu/SLICE_161
ROUTE         1     0.000    R38C15C.FCO to    R38C15D.FCI signal_process/demodu/INS_dout_1_cry_28
FCITOFCO_D  ---     0.063    R38C15D.FCI to    R38C15D.FCO signal_process/demodu/SLICE_162
ROUTE         1     0.000    R38C15D.FCO to    R38C16A.FCI signal_process/demodu/INS_dout_1_cry_30
FCITOFCO_D  ---     0.063    R38C16A.FCI to    R38C16A.FCO signal_process/demodu/SLICE_163
ROUTE         1     0.000    R38C16A.FCO to    R38C16B.FCI signal_process/demodu/INS_dout_1_cry_32
FCITOFCO_D  ---     0.063    R38C16B.FCI to    R38C16B.FCO signal_process/demodu/SLICE_164
ROUTE         1     0.000    R38C16B.FCO to    R38C16C.FCI signal_process/demodu/INS_dout_1_cry_34
FCITOFCO_D  ---     0.063    R38C16C.FCI to    R38C16C.FCO signal_process/demodu/SLICE_165
ROUTE         1     0.000    R38C16C.FCO to    R38C16D.FCI signal_process/demodu/INS_dout_1_cry_36
FCITOFCO_D  ---     0.063    R38C16D.FCI to    R38C16D.FCO signal_process/demodu/SLICE_166
ROUTE         1     0.000    R38C16D.FCO to    R38C17A.FCI signal_process/demodu/INS_dout_1_cry_38
FCITOFCO_D  ---     0.063    R38C17A.FCI to    R38C17A.FCO signal_process/demodu/SLICE_167
ROUTE         1     0.000    R38C17A.FCO to    R38C17B.FCI signal_process/demodu/INS_dout_1_cry_40
FCITOFCO_D  ---     0.063    R38C17B.FCI to    R38C17B.FCO signal_process/demodu/SLICE_168
ROUTE         1     0.000    R38C17B.FCO to    R38C17C.FCI signal_process/demodu/INS_dout_1_cry_42
FCITOFCO_D  ---     0.063    R38C17C.FCI to    R38C17C.FCO signal_process/demodu/SLICE_169
ROUTE         1     0.000    R38C17C.FCO to    R38C17D.FCI signal_process/demodu/INS_dout_1_cry_44
FCITOFCO_D  ---     0.063    R38C17D.FCI to    R38C17D.FCO signal_process/demodu/SLICE_170
ROUTE         1     0.000    R38C17D.FCO to    R38C18A.FCI signal_process/demodu/INS_dout_1_cry_46
FCITOFCO_D  ---     0.063    R38C18A.FCI to    R38C18A.FCO signal_process/demodu/SLICE_171
ROUTE         1     0.000    R38C18A.FCO to    R38C18B.FCI signal_process/demodu/INS_dout_1_cry_48
FCITOFCO_D  ---     0.063    R38C18B.FCI to    R38C18B.FCO signal_process/demodu/SLICE_172
ROUTE         1     0.000    R38C18B.FCO to    R38C18C.FCI signal_process/demodu/INS_dout_1_cry_50
FCITOFCO_D  ---     0.063    R38C18C.FCI to    R38C18C.FCO signal_process/demodu/SLICE_173
ROUTE         1     0.000    R38C18C.FCO to    R38C18D.FCI signal_process/demodu/INS_dout_1_cry_52
FCITOF1_DE  ---     0.412    R38C18D.FCI to     R38C18D.F1 signal_process/demodu/SLICE_174
ROUTE         1     0.000     R38C18D.F1 to    R38C18D.DI1 signal_process/demodu/INS_dout_1[54] (to clk120mhz)
                  --------
                    8.793   (83.8% logic, 16.2% route), 28 logic levels.


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         PDPW16KD   Port           signal_process/demodu/fifo/pdp_ram_0_0_1(ASIC)  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/INS_dout[53]  (to clk120mhz +)

   Delay:               8.766ns  (83.7% logic, 16.3% route), 28 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
C2Q_DEL     ---     4.979 *R_R37C13.CLKR to *R_R37C13.DO19 signal_process/demodu/fifo/pdp_ram_0_0_1 (from clk_AD)
ROUTE         1     1.426 *R_R37C13.DO19 to     R38C12B.A0 signal_process/demodu/sample_sum_DY[1]
C0TOFCO_DE  ---     0.401     R38C12B.A0 to    R38C12B.FCO signal_process/demodu/SLICE_148
ROUTE         1     0.000    R38C12B.FCO to    R38C12C.FCI signal_process/demodu/INS_dout_1_cry_2
FCITOFCO_D  ---     0.063    R38C12C.FCI to    R38C12C.FCO signal_process/demodu/SLICE_149
ROUTE         1     0.000    R38C12C.FCO to    R38C12D.FCI signal_process/demodu/INS_dout_1_cry_4
FCITOFCO_D  ---     0.063    R38C12D.FCI to    R38C12D.FCO signal_process/demodu/SLICE_150
ROUTE         1     0.000    R38C12D.FCO to    R38C13A.FCI signal_process/demodu/INS_dout_1_cry_6
FCITOFCO_D  ---     0.063    R38C13A.FCI to    R38C13A.FCO signal_process/demodu/SLICE_151
ROUTE         1     0.000    R38C13A.FCO to    R38C13B.FCI signal_process/demodu/INS_dout_1_cry_8
FCITOFCO_D  ---     0.063    R38C13B.FCI to    R38C13B.FCO signal_process/demodu/SLICE_152
ROUTE         1     0.000    R38C13B.FCO to    R38C13C.FCI signal_process/demodu/INS_dout_1_cry_10
FCITOFCO_D  ---     0.063    R38C13C.FCI to    R38C13C.FCO signal_process/demodu/SLICE_153
ROUTE         1     0.000    R38C13C.FCO to    R38C13D.FCI signal_process/demodu/INS_dout_1_cry_12
FCITOFCO_D  ---     0.063    R38C13D.FCI to    R38C13D.FCO signal_process/demodu/SLICE_154
ROUTE         1     0.000    R38C13D.FCO to    R38C14A.FCI signal_process/demodu/INS_dout_1_cry_14
FCITOFCO_D  ---     0.063    R38C14A.FCI to    R38C14A.FCO signal_process/demodu/SLICE_155
ROUTE         1     0.000    R38C14A.FCO to    R38C14B.FCI signal_process/demodu/INS_dout_1_cry_16
FCITOFCO_D  ---     0.063    R38C14B.FCI to    R38C14B.FCO signal_process/demodu/SLICE_156
ROUTE         1     0.000    R38C14B.FCO to    R38C14C.FCI signal_process/demodu/INS_dout_1_cry_18
FCITOFCO_D  ---     0.063    R38C14C.FCI to    R38C14C.FCO signal_process/demodu/SLICE_157
ROUTE         1     0.000    R38C14C.FCO to    R38C14D.FCI signal_process/demodu/INS_dout_1_cry_20
FCITOFCO_D  ---     0.063    R38C14D.FCI to    R38C14D.FCO signal_process/demodu/SLICE_158
ROUTE         1     0.000    R38C14D.FCO to    R38C15A.FCI signal_process/demodu/INS_dout_1_cry_22
FCITOFCO_D  ---     0.063    R38C15A.FCI to    R38C15A.FCO signal_process/demodu/SLICE_159
ROUTE         1     0.000    R38C15A.FCO to    R38C15B.FCI signal_process/demodu/INS_dout_1_cry_24
FCITOFCO_D  ---     0.063    R38C15B.FCI to    R38C15B.FCO signal_process/demodu/SLICE_160
ROUTE         1     0.000    R38C15B.FCO to    R38C15C.FCI signal_process/demodu/INS_dout_1_cry_26
FCITOFCO_D  ---     0.063    R38C15C.FCI to    R38C15C.FCO signal_process/demodu/SLICE_161
ROUTE         1     0.000    R38C15C.FCO to    R38C15D.FCI signal_process/demodu/INS_dout_1_cry_28
FCITOFCO_D  ---     0.063    R38C15D.FCI to    R38C15D.FCO signal_process/demodu/SLICE_162
ROUTE         1     0.000    R38C15D.FCO to    R38C16A.FCI signal_process/demodu/INS_dout_1_cry_30
FCITOFCO_D  ---     0.063    R38C16A.FCI to    R38C16A.FCO signal_process/demodu/SLICE_163
ROUTE         1     0.000    R38C16A.FCO to    R38C16B.FCI signal_process/demodu/INS_dout_1_cry_32
FCITOFCO_D  ---     0.063    R38C16B.FCI to    R38C16B.FCO signal_process/demodu/SLICE_164
ROUTE         1     0.000    R38C16B.FCO to    R38C16C.FCI signal_process/demodu/INS_dout_1_cry_34
FCITOFCO_D  ---     0.063    R38C16C.FCI to    R38C16C.FCO signal_process/demodu/SLICE_165
ROUTE         1     0.000    R38C16C.FCO to    R38C16D.FCI signal_process/demodu/INS_dout_1_cry_36
FCITOFCO_D  ---     0.063    R38C16D.FCI to    R38C16D.FCO signal_process/demodu/SLICE_166
ROUTE         1     0.000    R38C16D.FCO to    R38C17A.FCI signal_process/demodu/INS_dout_1_cry_38
FCITOFCO_D  ---     0.063    R38C17A.FCI to    R38C17A.FCO signal_process/demodu/SLICE_167
ROUTE         1     0.000    R38C17A.FCO to    R38C17B.FCI signal_process/demodu/INS_dout_1_cry_40
FCITOFCO_D  ---     0.063    R38C17B.FCI to    R38C17B.FCO signal_process/demodu/SLICE_168
ROUTE         1     0.000    R38C17B.FCO to    R38C17C.FCI signal_process/demodu/INS_dout_1_cry_42
FCITOFCO_D  ---     0.063    R38C17C.FCI to    R38C17C.FCO signal_process/demodu/SLICE_169
ROUTE         1     0.000    R38C17C.FCO to    R38C17D.FCI signal_process/demodu/INS_dout_1_cry_44
FCITOFCO_D  ---     0.063    R38C17D.FCI to    R38C17D.FCO signal_process/demodu/SLICE_170
ROUTE         1     0.000    R38C17D.FCO to    R38C18A.FCI signal_process/demodu/INS_dout_1_cry_46
FCITOFCO_D  ---     0.063    R38C18A.FCI to    R38C18A.FCO signal_process/demodu/SLICE_171
ROUTE         1     0.000    R38C18A.FCO to    R38C18B.FCI signal_process/demodu/INS_dout_1_cry_48
FCITOFCO_D  ---     0.063    R38C18B.FCI to    R38C18B.FCO signal_process/demodu/SLICE_172
ROUTE         1     0.000    R38C18B.FCO to    R38C18C.FCI signal_process/demodu/INS_dout_1_cry_50
FCITOFCO_D  ---     0.063    R38C18C.FCI to    R38C18C.FCO signal_process/demodu/SLICE_173
ROUTE         1     0.000    R38C18C.FCO to    R38C18D.FCI signal_process/demodu/INS_dout_1_cry_52
FCITOF0_DE  ---     0.385    R38C18D.FCI to     R38C18D.F0 signal_process/demodu/SLICE_174
ROUTE         1     0.000     R38C18D.F0 to    R38C18D.DI0 signal_process/demodu/INS_dout_1[53] (to clk120mhz)
                  --------
                    8.766   (83.7% logic, 16.3% route), 28 logic levels.


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         PDPW16KD   Port           signal_process/demodu/fifo/pdp_ram_0_0_1(ASIC)  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/INS_dout[52]  (to clk120mhz +)

   Delay:               8.730ns  (83.7% logic, 16.3% route), 27 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
C2Q_DEL     ---     4.979 *R_R37C13.CLKR to *R_R37C13.DO19 signal_process/demodu/fifo/pdp_ram_0_0_1 (from clk_AD)
ROUTE         1     1.426 *R_R37C13.DO19 to     R38C12B.A0 signal_process/demodu/sample_sum_DY[1]
C0TOFCO_DE  ---     0.401     R38C12B.A0 to    R38C12B.FCO signal_process/demodu/SLICE_148
ROUTE         1     0.000    R38C12B.FCO to    R38C12C.FCI signal_process/demodu/INS_dout_1_cry_2
FCITOFCO_D  ---     0.063    R38C12C.FCI to    R38C12C.FCO signal_process/demodu/SLICE_149
ROUTE         1     0.000    R38C12C.FCO to    R38C12D.FCI signal_process/demodu/INS_dout_1_cry_4
FCITOFCO_D  ---     0.063    R38C12D.FCI to    R38C12D.FCO signal_process/demodu/SLICE_150
ROUTE         1     0.000    R38C12D.FCO to    R38C13A.FCI signal_process/demodu/INS_dout_1_cry_6
FCITOFCO_D  ---     0.063    R38C13A.FCI to    R38C13A.FCO signal_process/demodu/SLICE_151
ROUTE         1     0.000    R38C13A.FCO to    R38C13B.FCI signal_process/demodu/INS_dout_1_cry_8
FCITOFCO_D  ---     0.063    R38C13B.FCI to    R38C13B.FCO signal_process/demodu/SLICE_152
ROUTE         1     0.000    R38C13B.FCO to    R38C13C.FCI signal_process/demodu/INS_dout_1_cry_10
FCITOFCO_D  ---     0.063    R38C13C.FCI to    R38C13C.FCO signal_process/demodu/SLICE_153
ROUTE         1     0.000    R38C13C.FCO to    R38C13D.FCI signal_process/demodu/INS_dout_1_cry_12
FCITOFCO_D  ---     0.063    R38C13D.FCI to    R38C13D.FCO signal_process/demodu/SLICE_154
ROUTE         1     0.000    R38C13D.FCO to    R38C14A.FCI signal_process/demodu/INS_dout_1_cry_14
FCITOFCO_D  ---     0.063    R38C14A.FCI to    R38C14A.FCO signal_process/demodu/SLICE_155
ROUTE         1     0.000    R38C14A.FCO to    R38C14B.FCI signal_process/demodu/INS_dout_1_cry_16
FCITOFCO_D  ---     0.063    R38C14B.FCI to    R38C14B.FCO signal_process/demodu/SLICE_156
ROUTE         1     0.000    R38C14B.FCO to    R38C14C.FCI signal_process/demodu/INS_dout_1_cry_18
FCITOFCO_D  ---     0.063    R38C14C.FCI to    R38C14C.FCO signal_process/demodu/SLICE_157
ROUTE         1     0.000    R38C14C.FCO to    R38C14D.FCI signal_process/demodu/INS_dout_1_cry_20
FCITOFCO_D  ---     0.063    R38C14D.FCI to    R38C14D.FCO signal_process/demodu/SLICE_158
ROUTE         1     0.000    R38C14D.FCO to    R38C15A.FCI signal_process/demodu/INS_dout_1_cry_22
FCITOFCO_D  ---     0.063    R38C15A.FCI to    R38C15A.FCO signal_process/demodu/SLICE_159
ROUTE         1     0.000    R38C15A.FCO to    R38C15B.FCI signal_process/demodu/INS_dout_1_cry_24
FCITOFCO_D  ---     0.063    R38C15B.FCI to    R38C15B.FCO signal_process/demodu/SLICE_160
ROUTE         1     0.000    R38C15B.FCO to    R38C15C.FCI signal_process/demodu/INS_dout_1_cry_26
FCITOFCO_D  ---     0.063    R38C15C.FCI to    R38C15C.FCO signal_process/demodu/SLICE_161
ROUTE         1     0.000    R38C15C.FCO to    R38C15D.FCI signal_process/demodu/INS_dout_1_cry_28
FCITOFCO_D  ---     0.063    R38C15D.FCI to    R38C15D.FCO signal_process/demodu/SLICE_162
ROUTE         1     0.000    R38C15D.FCO to    R38C16A.FCI signal_process/demodu/INS_dout_1_cry_30
FCITOFCO_D  ---     0.063    R38C16A.FCI to    R38C16A.FCO signal_process/demodu/SLICE_163
ROUTE         1     0.000    R38C16A.FCO to    R38C16B.FCI signal_process/demodu/INS_dout_1_cry_32
FCITOFCO_D  ---     0.063    R38C16B.FCI to    R38C16B.FCO signal_process/demodu/SLICE_164
ROUTE         1     0.000    R38C16B.FCO to    R38C16C.FCI signal_process/demodu/INS_dout_1_cry_34
FCITOFCO_D  ---     0.063    R38C16C.FCI to    R38C16C.FCO signal_process/demodu/SLICE_165
ROUTE         1     0.000    R38C16C.FCO to    R38C16D.FCI signal_process/demodu/INS_dout_1_cry_36
FCITOFCO_D  ---     0.063    R38C16D.FCI to    R38C16D.FCO signal_process/demodu/SLICE_166
ROUTE         1     0.000    R38C16D.FCO to    R38C17A.FCI signal_process/demodu/INS_dout_1_cry_38
FCITOFCO_D  ---     0.063    R38C17A.FCI to    R38C17A.FCO signal_process/demodu/SLICE_167
ROUTE         1     0.000    R38C17A.FCO to    R38C17B.FCI signal_process/demodu/INS_dout_1_cry_40
FCITOFCO_D  ---     0.063    R38C17B.FCI to    R38C17B.FCO signal_process/demodu/SLICE_168
ROUTE         1     0.000    R38C17B.FCO to    R38C17C.FCI signal_process/demodu/INS_dout_1_cry_42
FCITOFCO_D  ---     0.063    R38C17C.FCI to    R38C17C.FCO signal_process/demodu/SLICE_169
ROUTE         1     0.000    R38C17C.FCO to    R38C17D.FCI signal_process/demodu/INS_dout_1_cry_44
FCITOFCO_D  ---     0.063    R38C17D.FCI to    R38C17D.FCO signal_process/demodu/SLICE_170
ROUTE         1     0.000    R38C17D.FCO to    R38C18A.FCI signal_process/demodu/INS_dout_1_cry_46
FCITOFCO_D  ---     0.063    R38C18A.FCI to    R38C18A.FCO signal_process/demodu/SLICE_171
ROUTE         1     0.000    R38C18A.FCO to    R38C18B.FCI signal_process/demodu/INS_dout_1_cry_48
FCITOFCO_D  ---     0.063    R38C18B.FCI to    R38C18B.FCO signal_process/demodu/SLICE_172
ROUTE         1     0.000    R38C18B.FCO to    R38C18C.FCI signal_process/demodu/INS_dout_1_cry_50
FCITOF1_DE  ---     0.412    R38C18C.FCI to     R38C18C.F1 signal_process/demodu/SLICE_173
ROUTE         1     0.000     R38C18C.F1 to    R38C18C.DI1 signal_process/demodu/INS_dout_1[52] (to clk120mhz)
                  --------
                    8.730   (83.7% logic, 16.3% route), 27 logic levels.


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         PDPW16KD   Port           signal_process/demodu/fifo/pdp_ram_0_0_1(ASIC)  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/INS_dout[51]  (to clk120mhz +)

   Delay:               8.703ns  (83.6% logic, 16.4% route), 27 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
C2Q_DEL     ---     4.979 *R_R37C13.CLKR to *R_R37C13.DO19 signal_process/demodu/fifo/pdp_ram_0_0_1 (from clk_AD)
ROUTE         1     1.426 *R_R37C13.DO19 to     R38C12B.A0 signal_process/demodu/sample_sum_DY[1]
C0TOFCO_DE  ---     0.401     R38C12B.A0 to    R38C12B.FCO signal_process/demodu/SLICE_148
ROUTE         1     0.000    R38C12B.FCO to    R38C12C.FCI signal_process/demodu/INS_dout_1_cry_2
FCITOFCO_D  ---     0.063    R38C12C.FCI to    R38C12C.FCO signal_process/demodu/SLICE_149
ROUTE         1     0.000    R38C12C.FCO to    R38C12D.FCI signal_process/demodu/INS_dout_1_cry_4
FCITOFCO_D  ---     0.063    R38C12D.FCI to    R38C12D.FCO signal_process/demodu/SLICE_150
ROUTE         1     0.000    R38C12D.FCO to    R38C13A.FCI signal_process/demodu/INS_dout_1_cry_6
FCITOFCO_D  ---     0.063    R38C13A.FCI to    R38C13A.FCO signal_process/demodu/SLICE_151
ROUTE         1     0.000    R38C13A.FCO to    R38C13B.FCI signal_process/demodu/INS_dout_1_cry_8
FCITOFCO_D  ---     0.063    R38C13B.FCI to    R38C13B.FCO signal_process/demodu/SLICE_152
ROUTE         1     0.000    R38C13B.FCO to    R38C13C.FCI signal_process/demodu/INS_dout_1_cry_10
FCITOFCO_D  ---     0.063    R38C13C.FCI to    R38C13C.FCO signal_process/demodu/SLICE_153
ROUTE         1     0.000    R38C13C.FCO to    R38C13D.FCI signal_process/demodu/INS_dout_1_cry_12
FCITOFCO_D  ---     0.063    R38C13D.FCI to    R38C13D.FCO signal_process/demodu/SLICE_154
ROUTE         1     0.000    R38C13D.FCO to    R38C14A.FCI signal_process/demodu/INS_dout_1_cry_14
FCITOFCO_D  ---     0.063    R38C14A.FCI to    R38C14A.FCO signal_process/demodu/SLICE_155
ROUTE         1     0.000    R38C14A.FCO to    R38C14B.FCI signal_process/demodu/INS_dout_1_cry_16
FCITOFCO_D  ---     0.063    R38C14B.FCI to    R38C14B.FCO signal_process/demodu/SLICE_156
ROUTE         1     0.000    R38C14B.FCO to    R38C14C.FCI signal_process/demodu/INS_dout_1_cry_18
FCITOFCO_D  ---     0.063    R38C14C.FCI to    R38C14C.FCO signal_process/demodu/SLICE_157
ROUTE         1     0.000    R38C14C.FCO to    R38C14D.FCI signal_process/demodu/INS_dout_1_cry_20
FCITOFCO_D  ---     0.063    R38C14D.FCI to    R38C14D.FCO signal_process/demodu/SLICE_158
ROUTE         1     0.000    R38C14D.FCO to    R38C15A.FCI signal_process/demodu/INS_dout_1_cry_22
FCITOFCO_D  ---     0.063    R38C15A.FCI to    R38C15A.FCO signal_process/demodu/SLICE_159
ROUTE         1     0.000    R38C15A.FCO to    R38C15B.FCI signal_process/demodu/INS_dout_1_cry_24
FCITOFCO_D  ---     0.063    R38C15B.FCI to    R38C15B.FCO signal_process/demodu/SLICE_160
ROUTE         1     0.000    R38C15B.FCO to    R38C15C.FCI signal_process/demodu/INS_dout_1_cry_26
FCITOFCO_D  ---     0.063    R38C15C.FCI to    R38C15C.FCO signal_process/demodu/SLICE_161
ROUTE         1     0.000    R38C15C.FCO to    R38C15D.FCI signal_process/demodu/INS_dout_1_cry_28
FCITOFCO_D  ---     0.063    R38C15D.FCI to    R38C15D.FCO signal_process/demodu/SLICE_162
ROUTE         1     0.000    R38C15D.FCO to    R38C16A.FCI signal_process/demodu/INS_dout_1_cry_30
FCITOFCO_D  ---     0.063    R38C16A.FCI to    R38C16A.FCO signal_process/demodu/SLICE_163
ROUTE         1     0.000    R38C16A.FCO to    R38C16B.FCI signal_process/demodu/INS_dout_1_cry_32
FCITOFCO_D  ---     0.063    R38C16B.FCI to    R38C16B.FCO signal_process/demodu/SLICE_164
ROUTE         1     0.000    R38C16B.FCO to    R38C16C.FCI signal_process/demodu/INS_dout_1_cry_34
FCITOFCO_D  ---     0.063    R38C16C.FCI to    R38C16C.FCO signal_process/demodu/SLICE_165
ROUTE         1     0.000    R38C16C.FCO to    R38C16D.FCI signal_process/demodu/INS_dout_1_cry_36
FCITOFCO_D  ---     0.063    R38C16D.FCI to    R38C16D.FCO signal_process/demodu/SLICE_166
ROUTE         1     0.000    R38C16D.FCO to    R38C17A.FCI signal_process/demodu/INS_dout_1_cry_38
FCITOFCO_D  ---     0.063    R38C17A.FCI to    R38C17A.FCO signal_process/demodu/SLICE_167
ROUTE         1     0.000    R38C17A.FCO to    R38C17B.FCI signal_process/demodu/INS_dout_1_cry_40
FCITOFCO_D  ---     0.063    R38C17B.FCI to    R38C17B.FCO signal_process/demodu/SLICE_168
ROUTE         1     0.000    R38C17B.FCO to    R38C17C.FCI signal_process/demodu/INS_dout_1_cry_42
FCITOFCO_D  ---     0.063    R38C17C.FCI to    R38C17C.FCO signal_process/demodu/SLICE_169
ROUTE         1     0.000    R38C17C.FCO to    R38C17D.FCI signal_process/demodu/INS_dout_1_cry_44
FCITOFCO_D  ---     0.063    R38C17D.FCI to    R38C17D.FCO signal_process/demodu/SLICE_170
ROUTE         1     0.000    R38C17D.FCO to    R38C18A.FCI signal_process/demodu/INS_dout_1_cry_46
FCITOFCO_D  ---     0.063    R38C18A.FCI to    R38C18A.FCO signal_process/demodu/SLICE_171
ROUTE         1     0.000    R38C18A.FCO to    R38C18B.FCI signal_process/demodu/INS_dout_1_cry_48
FCITOFCO_D  ---     0.063    R38C18B.FCI to    R38C18B.FCO signal_process/demodu/SLICE_172
ROUTE         1     0.000    R38C18B.FCO to    R38C18C.FCI signal_process/demodu/INS_dout_1_cry_50
FCITOF0_DE  ---     0.385    R38C18C.FCI to     R38C18C.F0 signal_process/demodu/SLICE_173
ROUTE         1     0.000     R38C18C.F0 to    R38C18C.DI0 signal_process/demodu/INS_dout_1[51] (to clk120mhz)
                  --------
                    8.703   (83.6% logic, 16.4% route), 27 logic levels.


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         PDPW16KD   Port           signal_process/demodu/fifo/pdp_ram_0_0_1(ASIC)  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/INS_dout[50]  (to clk120mhz +)

   Delay:               8.667ns  (83.5% logic, 16.5% route), 26 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
C2Q_DEL     ---     4.979 *R_R37C13.CLKR to *R_R37C13.DO19 signal_process/demodu/fifo/pdp_ram_0_0_1 (from clk_AD)
ROUTE         1     1.426 *R_R37C13.DO19 to     R38C12B.A0 signal_process/demodu/sample_sum_DY[1]
C0TOFCO_DE  ---     0.401     R38C12B.A0 to    R38C12B.FCO signal_process/demodu/SLICE_148
ROUTE         1     0.000    R38C12B.FCO to    R38C12C.FCI signal_process/demodu/INS_dout_1_cry_2
FCITOFCO_D  ---     0.063    R38C12C.FCI to    R38C12C.FCO signal_process/demodu/SLICE_149
ROUTE         1     0.000    R38C12C.FCO to    R38C12D.FCI signal_process/demodu/INS_dout_1_cry_4
FCITOFCO_D  ---     0.063    R38C12D.FCI to    R38C12D.FCO signal_process/demodu/SLICE_150
ROUTE         1     0.000    R38C12D.FCO to    R38C13A.FCI signal_process/demodu/INS_dout_1_cry_6
FCITOFCO_D  ---     0.063    R38C13A.FCI to    R38C13A.FCO signal_process/demodu/SLICE_151
ROUTE         1     0.000    R38C13A.FCO to    R38C13B.FCI signal_process/demodu/INS_dout_1_cry_8
FCITOFCO_D  ---     0.063    R38C13B.FCI to    R38C13B.FCO signal_process/demodu/SLICE_152
ROUTE         1     0.000    R38C13B.FCO to    R38C13C.FCI signal_process/demodu/INS_dout_1_cry_10
FCITOFCO_D  ---     0.063    R38C13C.FCI to    R38C13C.FCO signal_process/demodu/SLICE_153
ROUTE         1     0.000    R38C13C.FCO to    R38C13D.FCI signal_process/demodu/INS_dout_1_cry_12
FCITOFCO_D  ---     0.063    R38C13D.FCI to    R38C13D.FCO signal_process/demodu/SLICE_154
ROUTE         1     0.000    R38C13D.FCO to    R38C14A.FCI signal_process/demodu/INS_dout_1_cry_14
FCITOFCO_D  ---     0.063    R38C14A.FCI to    R38C14A.FCO signal_process/demodu/SLICE_155
ROUTE         1     0.000    R38C14A.FCO to    R38C14B.FCI signal_process/demodu/INS_dout_1_cry_16
FCITOFCO_D  ---     0.063    R38C14B.FCI to    R38C14B.FCO signal_process/demodu/SLICE_156
ROUTE         1     0.000    R38C14B.FCO to    R38C14C.FCI signal_process/demodu/INS_dout_1_cry_18
FCITOFCO_D  ---     0.063    R38C14C.FCI to    R38C14C.FCO signal_process/demodu/SLICE_157
ROUTE         1     0.000    R38C14C.FCO to    R38C14D.FCI signal_process/demodu/INS_dout_1_cry_20
FCITOFCO_D  ---     0.063    R38C14D.FCI to    R38C14D.FCO signal_process/demodu/SLICE_158
ROUTE         1     0.000    R38C14D.FCO to    R38C15A.FCI signal_process/demodu/INS_dout_1_cry_22
FCITOFCO_D  ---     0.063    R38C15A.FCI to    R38C15A.FCO signal_process/demodu/SLICE_159
ROUTE         1     0.000    R38C15A.FCO to    R38C15B.FCI signal_process/demodu/INS_dout_1_cry_24
FCITOFCO_D  ---     0.063    R38C15B.FCI to    R38C15B.FCO signal_process/demodu/SLICE_160
ROUTE         1     0.000    R38C15B.FCO to    R38C15C.FCI signal_process/demodu/INS_dout_1_cry_26
FCITOFCO_D  ---     0.063    R38C15C.FCI to    R38C15C.FCO signal_process/demodu/SLICE_161
ROUTE         1     0.000    R38C15C.FCO to    R38C15D.FCI signal_process/demodu/INS_dout_1_cry_28
FCITOFCO_D  ---     0.063    R38C15D.FCI to    R38C15D.FCO signal_process/demodu/SLICE_162
ROUTE         1     0.000    R38C15D.FCO to    R38C16A.FCI signal_process/demodu/INS_dout_1_cry_30
FCITOFCO_D  ---     0.063    R38C16A.FCI to    R38C16A.FCO signal_process/demodu/SLICE_163
ROUTE         1     0.000    R38C16A.FCO to    R38C16B.FCI signal_process/demodu/INS_dout_1_cry_32
FCITOFCO_D  ---     0.063    R38C16B.FCI to    R38C16B.FCO signal_process/demodu/SLICE_164
ROUTE         1     0.000    R38C16B.FCO to    R38C16C.FCI signal_process/demodu/INS_dout_1_cry_34
FCITOFCO_D  ---     0.063    R38C16C.FCI to    R38C16C.FCO signal_process/demodu/SLICE_165
ROUTE         1     0.000    R38C16C.FCO to    R38C16D.FCI signal_process/demodu/INS_dout_1_cry_36
FCITOFCO_D  ---     0.063    R38C16D.FCI to    R38C16D.FCO signal_process/demodu/SLICE_166
ROUTE         1     0.000    R38C16D.FCO to    R38C17A.FCI signal_process/demodu/INS_dout_1_cry_38
FCITOFCO_D  ---     0.063    R38C17A.FCI to    R38C17A.FCO signal_process/demodu/SLICE_167
ROUTE         1     0.000    R38C17A.FCO to    R38C17B.FCI signal_process/demodu/INS_dout_1_cry_40
FCITOFCO_D  ---     0.063    R38C17B.FCI to    R38C17B.FCO signal_process/demodu/SLICE_168
ROUTE         1     0.000    R38C17B.FCO to    R38C17C.FCI signal_process/demodu/INS_dout_1_cry_42
FCITOFCO_D  ---     0.063    R38C17C.FCI to    R38C17C.FCO signal_process/demodu/SLICE_169
ROUTE         1     0.000    R38C17C.FCO to    R38C17D.FCI signal_process/demodu/INS_dout_1_cry_44
FCITOFCO_D  ---     0.063    R38C17D.FCI to    R38C17D.FCO signal_process/demodu/SLICE_170
ROUTE         1     0.000    R38C17D.FCO to    R38C18A.FCI signal_process/demodu/INS_dout_1_cry_46
FCITOFCO_D  ---     0.063    R38C18A.FCI to    R38C18A.FCO signal_process/demodu/SLICE_171
ROUTE         1     0.000    R38C18A.FCO to    R38C18B.FCI signal_process/demodu/INS_dout_1_cry_48
FCITOF1_DE  ---     0.412    R38C18B.FCI to     R38C18B.F1 signal_process/demodu/SLICE_172
ROUTE         1     0.000     R38C18B.F1 to    R38C18B.DI1 signal_process/demodu/INS_dout_1[50] (to clk120mhz)
                  --------
                    8.667   (83.5% logic, 16.5% route), 26 logic levels.


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         PDPW16KD   Port           signal_process/demodu/fifo/pdp_ram_0_0_1(ASIC)  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/INS_dout[49]  (to clk120mhz +)

   Delay:               8.640ns  (83.5% logic, 16.5% route), 26 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
C2Q_DEL     ---     4.979 *R_R37C13.CLKR to *R_R37C13.DO19 signal_process/demodu/fifo/pdp_ram_0_0_1 (from clk_AD)
ROUTE         1     1.426 *R_R37C13.DO19 to     R38C12B.A0 signal_process/demodu/sample_sum_DY[1]
C0TOFCO_DE  ---     0.401     R38C12B.A0 to    R38C12B.FCO signal_process/demodu/SLICE_148
ROUTE         1     0.000    R38C12B.FCO to    R38C12C.FCI signal_process/demodu/INS_dout_1_cry_2
FCITOFCO_D  ---     0.063    R38C12C.FCI to    R38C12C.FCO signal_process/demodu/SLICE_149
ROUTE         1     0.000    R38C12C.FCO to    R38C12D.FCI signal_process/demodu/INS_dout_1_cry_4
FCITOFCO_D  ---     0.063    R38C12D.FCI to    R38C12D.FCO signal_process/demodu/SLICE_150
ROUTE         1     0.000    R38C12D.FCO to    R38C13A.FCI signal_process/demodu/INS_dout_1_cry_6
FCITOFCO_D  ---     0.063    R38C13A.FCI to    R38C13A.FCO signal_process/demodu/SLICE_151
ROUTE         1     0.000    R38C13A.FCO to    R38C13B.FCI signal_process/demodu/INS_dout_1_cry_8
FCITOFCO_D  ---     0.063    R38C13B.FCI to    R38C13B.FCO signal_process/demodu/SLICE_152
ROUTE         1     0.000    R38C13B.FCO to    R38C13C.FCI signal_process/demodu/INS_dout_1_cry_10
FCITOFCO_D  ---     0.063    R38C13C.FCI to    R38C13C.FCO signal_process/demodu/SLICE_153
ROUTE         1     0.000    R38C13C.FCO to    R38C13D.FCI signal_process/demodu/INS_dout_1_cry_12
FCITOFCO_D  ---     0.063    R38C13D.FCI to    R38C13D.FCO signal_process/demodu/SLICE_154
ROUTE         1     0.000    R38C13D.FCO to    R38C14A.FCI signal_process/demodu/INS_dout_1_cry_14
FCITOFCO_D  ---     0.063    R38C14A.FCI to    R38C14A.FCO signal_process/demodu/SLICE_155
ROUTE         1     0.000    R38C14A.FCO to    R38C14B.FCI signal_process/demodu/INS_dout_1_cry_16
FCITOFCO_D  ---     0.063    R38C14B.FCI to    R38C14B.FCO signal_process/demodu/SLICE_156
ROUTE         1     0.000    R38C14B.FCO to    R38C14C.FCI signal_process/demodu/INS_dout_1_cry_18
FCITOFCO_D  ---     0.063    R38C14C.FCI to    R38C14C.FCO signal_process/demodu/SLICE_157
ROUTE         1     0.000    R38C14C.FCO to    R38C14D.FCI signal_process/demodu/INS_dout_1_cry_20
FCITOFCO_D  ---     0.063    R38C14D.FCI to    R38C14D.FCO signal_process/demodu/SLICE_158
ROUTE         1     0.000    R38C14D.FCO to    R38C15A.FCI signal_process/demodu/INS_dout_1_cry_22
FCITOFCO_D  ---     0.063    R38C15A.FCI to    R38C15A.FCO signal_process/demodu/SLICE_159
ROUTE         1     0.000    R38C15A.FCO to    R38C15B.FCI signal_process/demodu/INS_dout_1_cry_24
FCITOFCO_D  ---     0.063    R38C15B.FCI to    R38C15B.FCO signal_process/demodu/SLICE_160
ROUTE         1     0.000    R38C15B.FCO to    R38C15C.FCI signal_process/demodu/INS_dout_1_cry_26
FCITOFCO_D  ---     0.063    R38C15C.FCI to    R38C15C.FCO signal_process/demodu/SLICE_161
ROUTE         1     0.000    R38C15C.FCO to    R38C15D.FCI signal_process/demodu/INS_dout_1_cry_28
FCITOFCO_D  ---     0.063    R38C15D.FCI to    R38C15D.FCO signal_process/demodu/SLICE_162
ROUTE         1     0.000    R38C15D.FCO to    R38C16A.FCI signal_process/demodu/INS_dout_1_cry_30
FCITOFCO_D  ---     0.063    R38C16A.FCI to    R38C16A.FCO signal_process/demodu/SLICE_163
ROUTE         1     0.000    R38C16A.FCO to    R38C16B.FCI signal_process/demodu/INS_dout_1_cry_32
FCITOFCO_D  ---     0.063    R38C16B.FCI to    R38C16B.FCO signal_process/demodu/SLICE_164
ROUTE         1     0.000    R38C16B.FCO to    R38C16C.FCI signal_process/demodu/INS_dout_1_cry_34
FCITOFCO_D  ---     0.063    R38C16C.FCI to    R38C16C.FCO signal_process/demodu/SLICE_165
ROUTE         1     0.000    R38C16C.FCO to    R38C16D.FCI signal_process/demodu/INS_dout_1_cry_36
FCITOFCO_D  ---     0.063    R38C16D.FCI to    R38C16D.FCO signal_process/demodu/SLICE_166
ROUTE         1     0.000    R38C16D.FCO to    R38C17A.FCI signal_process/demodu/INS_dout_1_cry_38
FCITOFCO_D  ---     0.063    R38C17A.FCI to    R38C17A.FCO signal_process/demodu/SLICE_167
ROUTE         1     0.000    R38C17A.FCO to    R38C17B.FCI signal_process/demodu/INS_dout_1_cry_40
FCITOFCO_D  ---     0.063    R38C17B.FCI to    R38C17B.FCO signal_process/demodu/SLICE_168
ROUTE         1     0.000    R38C17B.FCO to    R38C17C.FCI signal_process/demodu/INS_dout_1_cry_42
FCITOFCO_D  ---     0.063    R38C17C.FCI to    R38C17C.FCO signal_process/demodu/SLICE_169
ROUTE         1     0.000    R38C17C.FCO to    R38C17D.FCI signal_process/demodu/INS_dout_1_cry_44
FCITOFCO_D  ---     0.063    R38C17D.FCI to    R38C17D.FCO signal_process/demodu/SLICE_170
ROUTE         1     0.000    R38C17D.FCO to    R38C18A.FCI signal_process/demodu/INS_dout_1_cry_46
FCITOFCO_D  ---     0.063    R38C18A.FCI to    R38C18A.FCO signal_process/demodu/SLICE_171
ROUTE         1     0.000    R38C18A.FCO to    R38C18B.FCI signal_process/demodu/INS_dout_1_cry_48
FCITOF0_DE  ---     0.385    R38C18B.FCI to     R38C18B.F0 signal_process/demodu/SLICE_172
ROUTE         1     0.000     R38C18B.F0 to    R38C18B.DI0 signal_process/demodu/INS_dout_1[49] (to clk120mhz)
                  --------
                    8.640   (83.5% logic, 16.5% route), 26 logic levels.


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         PDPW16KD   Port           signal_process/demodu/fifo/pdp_ram_0_0_1(ASIC)  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/INS_dout[48]  (to clk120mhz +)

   Delay:               8.604ns  (83.4% logic, 16.6% route), 25 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
C2Q_DEL     ---     4.979 *R_R37C13.CLKR to *R_R37C13.DO19 signal_process/demodu/fifo/pdp_ram_0_0_1 (from clk_AD)
ROUTE         1     1.426 *R_R37C13.DO19 to     R38C12B.A0 signal_process/demodu/sample_sum_DY[1]
C0TOFCO_DE  ---     0.401     R38C12B.A0 to    R38C12B.FCO signal_process/demodu/SLICE_148
ROUTE         1     0.000    R38C12B.FCO to    R38C12C.FCI signal_process/demodu/INS_dout_1_cry_2
FCITOFCO_D  ---     0.063    R38C12C.FCI to    R38C12C.FCO signal_process/demodu/SLICE_149
ROUTE         1     0.000    R38C12C.FCO to    R38C12D.FCI signal_process/demodu/INS_dout_1_cry_4
FCITOFCO_D  ---     0.063    R38C12D.FCI to    R38C12D.FCO signal_process/demodu/SLICE_150
ROUTE         1     0.000    R38C12D.FCO to    R38C13A.FCI signal_process/demodu/INS_dout_1_cry_6
FCITOFCO_D  ---     0.063    R38C13A.FCI to    R38C13A.FCO signal_process/demodu/SLICE_151
ROUTE         1     0.000    R38C13A.FCO to    R38C13B.FCI signal_process/demodu/INS_dout_1_cry_8
FCITOFCO_D  ---     0.063    R38C13B.FCI to    R38C13B.FCO signal_process/demodu/SLICE_152
ROUTE         1     0.000    R38C13B.FCO to    R38C13C.FCI signal_process/demodu/INS_dout_1_cry_10
FCITOFCO_D  ---     0.063    R38C13C.FCI to    R38C13C.FCO signal_process/demodu/SLICE_153
ROUTE         1     0.000    R38C13C.FCO to    R38C13D.FCI signal_process/demodu/INS_dout_1_cry_12
FCITOFCO_D  ---     0.063    R38C13D.FCI to    R38C13D.FCO signal_process/demodu/SLICE_154
ROUTE         1     0.000    R38C13D.FCO to    R38C14A.FCI signal_process/demodu/INS_dout_1_cry_14
FCITOFCO_D  ---     0.063    R38C14A.FCI to    R38C14A.FCO signal_process/demodu/SLICE_155
ROUTE         1     0.000    R38C14A.FCO to    R38C14B.FCI signal_process/demodu/INS_dout_1_cry_16
FCITOFCO_D  ---     0.063    R38C14B.FCI to    R38C14B.FCO signal_process/demodu/SLICE_156
ROUTE         1     0.000    R38C14B.FCO to    R38C14C.FCI signal_process/demodu/INS_dout_1_cry_18
FCITOFCO_D  ---     0.063    R38C14C.FCI to    R38C14C.FCO signal_process/demodu/SLICE_157
ROUTE         1     0.000    R38C14C.FCO to    R38C14D.FCI signal_process/demodu/INS_dout_1_cry_20
FCITOFCO_D  ---     0.063    R38C14D.FCI to    R38C14D.FCO signal_process/demodu/SLICE_158
ROUTE         1     0.000    R38C14D.FCO to    R38C15A.FCI signal_process/demodu/INS_dout_1_cry_22
FCITOFCO_D  ---     0.063    R38C15A.FCI to    R38C15A.FCO signal_process/demodu/SLICE_159
ROUTE         1     0.000    R38C15A.FCO to    R38C15B.FCI signal_process/demodu/INS_dout_1_cry_24
FCITOFCO_D  ---     0.063    R38C15B.FCI to    R38C15B.FCO signal_process/demodu/SLICE_160
ROUTE         1     0.000    R38C15B.FCO to    R38C15C.FCI signal_process/demodu/INS_dout_1_cry_26
FCITOFCO_D  ---     0.063    R38C15C.FCI to    R38C15C.FCO signal_process/demodu/SLICE_161
ROUTE         1     0.000    R38C15C.FCO to    R38C15D.FCI signal_process/demodu/INS_dout_1_cry_28
FCITOFCO_D  ---     0.063    R38C15D.FCI to    R38C15D.FCO signal_process/demodu/SLICE_162
ROUTE         1     0.000    R38C15D.FCO to    R38C16A.FCI signal_process/demodu/INS_dout_1_cry_30
FCITOFCO_D  ---     0.063    R38C16A.FCI to    R38C16A.FCO signal_process/demodu/SLICE_163
ROUTE         1     0.000    R38C16A.FCO to    R38C16B.FCI signal_process/demodu/INS_dout_1_cry_32
FCITOFCO_D  ---     0.063    R38C16B.FCI to    R38C16B.FCO signal_process/demodu/SLICE_164
ROUTE         1     0.000    R38C16B.FCO to    R38C16C.FCI signal_process/demodu/INS_dout_1_cry_34
FCITOFCO_D  ---     0.063    R38C16C.FCI to    R38C16C.FCO signal_process/demodu/SLICE_165
ROUTE         1     0.000    R38C16C.FCO to    R38C16D.FCI signal_process/demodu/INS_dout_1_cry_36
FCITOFCO_D  ---     0.063    R38C16D.FCI to    R38C16D.FCO signal_process/demodu/SLICE_166
ROUTE         1     0.000    R38C16D.FCO to    R38C17A.FCI signal_process/demodu/INS_dout_1_cry_38
FCITOFCO_D  ---     0.063    R38C17A.FCI to    R38C17A.FCO signal_process/demodu/SLICE_167
ROUTE         1     0.000    R38C17A.FCO to    R38C17B.FCI signal_process/demodu/INS_dout_1_cry_40
FCITOFCO_D  ---     0.063    R38C17B.FCI to    R38C17B.FCO signal_process/demodu/SLICE_168
ROUTE         1     0.000    R38C17B.FCO to    R38C17C.FCI signal_process/demodu/INS_dout_1_cry_42
FCITOFCO_D  ---     0.063    R38C17C.FCI to    R38C17C.FCO signal_process/demodu/SLICE_169
ROUTE         1     0.000    R38C17C.FCO to    R38C17D.FCI signal_process/demodu/INS_dout_1_cry_44
FCITOFCO_D  ---     0.063    R38C17D.FCI to    R38C17D.FCO signal_process/demodu/SLICE_170
ROUTE         1     0.000    R38C17D.FCO to    R38C18A.FCI signal_process/demodu/INS_dout_1_cry_46
FCITOF1_DE  ---     0.412    R38C18A.FCI to     R38C18A.F1 signal_process/demodu/SLICE_171
ROUTE         1     0.000     R38C18A.F1 to    R38C18A.DI1 signal_process/demodu/INS_dout_1[48] (to clk120mhz)
                  --------
                    8.604   (83.4% logic, 16.6% route), 25 logic levels.


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         PDPW16KD   Port           signal_process/demodu/fifo/pdp_ram_0_0_1(ASIC)  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/INS_dout[54]  (to clk120mhz +)

   Delay:               8.593ns  (85.7% logic, 14.3% route), 28 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
C2Q_DEL     ---     4.979 *R_R37C13.CLKR to *R_R37C13.DO20 signal_process/demodu/fifo/pdp_ram_0_0_1 (from clk_AD)
ROUTE         1     1.226 *R_R37C13.DO20 to     R38C12B.B1 signal_process/demodu/sample_sum_DY[2]
C1TOFCO_DE  ---     0.401     R38C12B.B1 to    R38C12B.FCO signal_process/demodu/SLICE_148
ROUTE         1     0.000    R38C12B.FCO to    R38C12C.FCI signal_process/demodu/INS_dout_1_cry_2
FCITOFCO_D  ---     0.063    R38C12C.FCI to    R38C12C.FCO signal_process/demodu/SLICE_149
ROUTE         1     0.000    R38C12C.FCO to    R38C12D.FCI signal_process/demodu/INS_dout_1_cry_4
FCITOFCO_D  ---     0.063    R38C12D.FCI to    R38C12D.FCO signal_process/demodu/SLICE_150
ROUTE         1     0.000    R38C12D.FCO to    R38C13A.FCI signal_process/demodu/INS_dout_1_cry_6
FCITOFCO_D  ---     0.063    R38C13A.FCI to    R38C13A.FCO signal_process/demodu/SLICE_151
ROUTE         1     0.000    R38C13A.FCO to    R38C13B.FCI signal_process/demodu/INS_dout_1_cry_8
FCITOFCO_D  ---     0.063    R38C13B.FCI to    R38C13B.FCO signal_process/demodu/SLICE_152
ROUTE         1     0.000    R38C13B.FCO to    R38C13C.FCI signal_process/demodu/INS_dout_1_cry_10
FCITOFCO_D  ---     0.063    R38C13C.FCI to    R38C13C.FCO signal_process/demodu/SLICE_153
ROUTE         1     0.000    R38C13C.FCO to    R38C13D.FCI signal_process/demodu/INS_dout_1_cry_12
FCITOFCO_D  ---     0.063    R38C13D.FCI to    R38C13D.FCO signal_process/demodu/SLICE_154
ROUTE         1     0.000    R38C13D.FCO to    R38C14A.FCI signal_process/demodu/INS_dout_1_cry_14
FCITOFCO_D  ---     0.063    R38C14A.FCI to    R38C14A.FCO signal_process/demodu/SLICE_155
ROUTE         1     0.000    R38C14A.FCO to    R38C14B.FCI signal_process/demodu/INS_dout_1_cry_16
FCITOFCO_D  ---     0.063    R38C14B.FCI to    R38C14B.FCO signal_process/demodu/SLICE_156
ROUTE         1     0.000    R38C14B.FCO to    R38C14C.FCI signal_process/demodu/INS_dout_1_cry_18
FCITOFCO_D  ---     0.063    R38C14C.FCI to    R38C14C.FCO signal_process/demodu/SLICE_157
ROUTE         1     0.000    R38C14C.FCO to    R38C14D.FCI signal_process/demodu/INS_dout_1_cry_20
FCITOFCO_D  ---     0.063    R38C14D.FCI to    R38C14D.FCO signal_process/demodu/SLICE_158
ROUTE         1     0.000    R38C14D.FCO to    R38C15A.FCI signal_process/demodu/INS_dout_1_cry_22
FCITOFCO_D  ---     0.063    R38C15A.FCI to    R38C15A.FCO signal_process/demodu/SLICE_159
ROUTE         1     0.000    R38C15A.FCO to    R38C15B.FCI signal_process/demodu/INS_dout_1_cry_24
FCITOFCO_D  ---     0.063    R38C15B.FCI to    R38C15B.FCO signal_process/demodu/SLICE_160
ROUTE         1     0.000    R38C15B.FCO to    R38C15C.FCI signal_process/demodu/INS_dout_1_cry_26
FCITOFCO_D  ---     0.063    R38C15C.FCI to    R38C15C.FCO signal_process/demodu/SLICE_161
ROUTE         1     0.000    R38C15C.FCO to    R38C15D.FCI signal_process/demodu/INS_dout_1_cry_28
FCITOFCO_D  ---     0.063    R38C15D.FCI to    R38C15D.FCO signal_process/demodu/SLICE_162
ROUTE         1     0.000    R38C15D.FCO to    R38C16A.FCI signal_process/demodu/INS_dout_1_cry_30
FCITOFCO_D  ---     0.063    R38C16A.FCI to    R38C16A.FCO signal_process/demodu/SLICE_163
ROUTE         1     0.000    R38C16A.FCO to    R38C16B.FCI signal_process/demodu/INS_dout_1_cry_32
FCITOFCO_D  ---     0.063    R38C16B.FCI to    R38C16B.FCO signal_process/demodu/SLICE_164
ROUTE         1     0.000    R38C16B.FCO to    R38C16C.FCI signal_process/demodu/INS_dout_1_cry_34
FCITOFCO_D  ---     0.063    R38C16C.FCI to    R38C16C.FCO signal_process/demodu/SLICE_165
ROUTE         1     0.000    R38C16C.FCO to    R38C16D.FCI signal_process/demodu/INS_dout_1_cry_36
FCITOFCO_D  ---     0.063    R38C16D.FCI to    R38C16D.FCO signal_process/demodu/SLICE_166
ROUTE         1     0.000    R38C16D.FCO to    R38C17A.FCI signal_process/demodu/INS_dout_1_cry_38
FCITOFCO_D  ---     0.063    R38C17A.FCI to    R38C17A.FCO signal_process/demodu/SLICE_167
ROUTE         1     0.000    R38C17A.FCO to    R38C17B.FCI signal_process/demodu/INS_dout_1_cry_40
FCITOFCO_D  ---     0.063    R38C17B.FCI to    R38C17B.FCO signal_process/demodu/SLICE_168
ROUTE         1     0.000    R38C17B.FCO to    R38C17C.FCI signal_process/demodu/INS_dout_1_cry_42
FCITOFCO_D  ---     0.063    R38C17C.FCI to    R38C17C.FCO signal_process/demodu/SLICE_169
ROUTE         1     0.000    R38C17C.FCO to    R38C17D.FCI signal_process/demodu/INS_dout_1_cry_44
FCITOFCO_D  ---     0.063    R38C17D.FCI to    R38C17D.FCO signal_process/demodu/SLICE_170
ROUTE         1     0.000    R38C17D.FCO to    R38C18A.FCI signal_process/demodu/INS_dout_1_cry_46
FCITOFCO_D  ---     0.063    R38C18A.FCI to    R38C18A.FCO signal_process/demodu/SLICE_171
ROUTE         1     0.000    R38C18A.FCO to    R38C18B.FCI signal_process/demodu/INS_dout_1_cry_48
FCITOFCO_D  ---     0.063    R38C18B.FCI to    R38C18B.FCO signal_process/demodu/SLICE_172
ROUTE         1     0.000    R38C18B.FCO to    R38C18C.FCI signal_process/demodu/INS_dout_1_cry_50
FCITOFCO_D  ---     0.063    R38C18C.FCI to    R38C18C.FCO signal_process/demodu/SLICE_173
ROUTE         1     0.000    R38C18C.FCO to    R38C18D.FCI signal_process/demodu/INS_dout_1_cry_52
FCITOF1_DE  ---     0.412    R38C18D.FCI to     R38C18D.F1 signal_process/demodu/SLICE_174
ROUTE         1     0.000     R38C18D.F1 to    R38C18D.DI1 signal_process/demodu/INS_dout_1[54] (to clk120mhz)
                  --------
                    8.593   (85.7% logic, 14.3% route), 28 logic levels.


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         PDPW16KD   Port           signal_process/demodu/fifo/pdp_ram_0_0_1(ASIC)  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/INS_dout[47]  (to clk120mhz +)

   Delay:               8.577ns  (83.4% logic, 16.6% route), 25 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
C2Q_DEL     ---     4.979 *R_R37C13.CLKR to *R_R37C13.DO19 signal_process/demodu/fifo/pdp_ram_0_0_1 (from clk_AD)
ROUTE         1     1.426 *R_R37C13.DO19 to     R38C12B.A0 signal_process/demodu/sample_sum_DY[1]
C0TOFCO_DE  ---     0.401     R38C12B.A0 to    R38C12B.FCO signal_process/demodu/SLICE_148
ROUTE         1     0.000    R38C12B.FCO to    R38C12C.FCI signal_process/demodu/INS_dout_1_cry_2
FCITOFCO_D  ---     0.063    R38C12C.FCI to    R38C12C.FCO signal_process/demodu/SLICE_149
ROUTE         1     0.000    R38C12C.FCO to    R38C12D.FCI signal_process/demodu/INS_dout_1_cry_4
FCITOFCO_D  ---     0.063    R38C12D.FCI to    R38C12D.FCO signal_process/demodu/SLICE_150
ROUTE         1     0.000    R38C12D.FCO to    R38C13A.FCI signal_process/demodu/INS_dout_1_cry_6
FCITOFCO_D  ---     0.063    R38C13A.FCI to    R38C13A.FCO signal_process/demodu/SLICE_151
ROUTE         1     0.000    R38C13A.FCO to    R38C13B.FCI signal_process/demodu/INS_dout_1_cry_8
FCITOFCO_D  ---     0.063    R38C13B.FCI to    R38C13B.FCO signal_process/demodu/SLICE_152
ROUTE         1     0.000    R38C13B.FCO to    R38C13C.FCI signal_process/demodu/INS_dout_1_cry_10
FCITOFCO_D  ---     0.063    R38C13C.FCI to    R38C13C.FCO signal_process/demodu/SLICE_153
ROUTE         1     0.000    R38C13C.FCO to    R38C13D.FCI signal_process/demodu/INS_dout_1_cry_12
FCITOFCO_D  ---     0.063    R38C13D.FCI to    R38C13D.FCO signal_process/demodu/SLICE_154
ROUTE         1     0.000    R38C13D.FCO to    R38C14A.FCI signal_process/demodu/INS_dout_1_cry_14
FCITOFCO_D  ---     0.063    R38C14A.FCI to    R38C14A.FCO signal_process/demodu/SLICE_155
ROUTE         1     0.000    R38C14A.FCO to    R38C14B.FCI signal_process/demodu/INS_dout_1_cry_16
FCITOFCO_D  ---     0.063    R38C14B.FCI to    R38C14B.FCO signal_process/demodu/SLICE_156
ROUTE         1     0.000    R38C14B.FCO to    R38C14C.FCI signal_process/demodu/INS_dout_1_cry_18
FCITOFCO_D  ---     0.063    R38C14C.FCI to    R38C14C.FCO signal_process/demodu/SLICE_157
ROUTE         1     0.000    R38C14C.FCO to    R38C14D.FCI signal_process/demodu/INS_dout_1_cry_20
FCITOFCO_D  ---     0.063    R38C14D.FCI to    R38C14D.FCO signal_process/demodu/SLICE_158
ROUTE         1     0.000    R38C14D.FCO to    R38C15A.FCI signal_process/demodu/INS_dout_1_cry_22
FCITOFCO_D  ---     0.063    R38C15A.FCI to    R38C15A.FCO signal_process/demodu/SLICE_159
ROUTE         1     0.000    R38C15A.FCO to    R38C15B.FCI signal_process/demodu/INS_dout_1_cry_24
FCITOFCO_D  ---     0.063    R38C15B.FCI to    R38C15B.FCO signal_process/demodu/SLICE_160
ROUTE         1     0.000    R38C15B.FCO to    R38C15C.FCI signal_process/demodu/INS_dout_1_cry_26
FCITOFCO_D  ---     0.063    R38C15C.FCI to    R38C15C.FCO signal_process/demodu/SLICE_161
ROUTE         1     0.000    R38C15C.FCO to    R38C15D.FCI signal_process/demodu/INS_dout_1_cry_28
FCITOFCO_D  ---     0.063    R38C15D.FCI to    R38C15D.FCO signal_process/demodu/SLICE_162
ROUTE         1     0.000    R38C15D.FCO to    R38C16A.FCI signal_process/demodu/INS_dout_1_cry_30
FCITOFCO_D  ---     0.063    R38C16A.FCI to    R38C16A.FCO signal_process/demodu/SLICE_163
ROUTE         1     0.000    R38C16A.FCO to    R38C16B.FCI signal_process/demodu/INS_dout_1_cry_32
FCITOFCO_D  ---     0.063    R38C16B.FCI to    R38C16B.FCO signal_process/demodu/SLICE_164
ROUTE         1     0.000    R38C16B.FCO to    R38C16C.FCI signal_process/demodu/INS_dout_1_cry_34
FCITOFCO_D  ---     0.063    R38C16C.FCI to    R38C16C.FCO signal_process/demodu/SLICE_165
ROUTE         1     0.000    R38C16C.FCO to    R38C16D.FCI signal_process/demodu/INS_dout_1_cry_36
FCITOFCO_D  ---     0.063    R38C16D.FCI to    R38C16D.FCO signal_process/demodu/SLICE_166
ROUTE         1     0.000    R38C16D.FCO to    R38C17A.FCI signal_process/demodu/INS_dout_1_cry_38
FCITOFCO_D  ---     0.063    R38C17A.FCI to    R38C17A.FCO signal_process/demodu/SLICE_167
ROUTE         1     0.000    R38C17A.FCO to    R38C17B.FCI signal_process/demodu/INS_dout_1_cry_40
FCITOFCO_D  ---     0.063    R38C17B.FCI to    R38C17B.FCO signal_process/demodu/SLICE_168
ROUTE         1     0.000    R38C17B.FCO to    R38C17C.FCI signal_process/demodu/INS_dout_1_cry_42
FCITOFCO_D  ---     0.063    R38C17C.FCI to    R38C17C.FCO signal_process/demodu/SLICE_169
ROUTE         1     0.000    R38C17C.FCO to    R38C17D.FCI signal_process/demodu/INS_dout_1_cry_44
FCITOFCO_D  ---     0.063    R38C17D.FCI to    R38C17D.FCO signal_process/demodu/SLICE_170
ROUTE         1     0.000    R38C17D.FCO to    R38C18A.FCI signal_process/demodu/INS_dout_1_cry_46
FCITOF0_DE  ---     0.385    R38C18A.FCI to     R38C18A.F0 signal_process/demodu/SLICE_171
ROUTE         1     0.000     R38C18A.F0 to    R38C18A.DI0 signal_process/demodu/INS_dout_1[47] (to clk120mhz)
                  --------
                    8.577   (83.4% logic, 16.6% route), 25 logic levels.


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         PDPW16KD   Port           signal_process/demodu/fifo/pdp_ram_0_0_1(ASIC)  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/INS_dout[54]  (to clk120mhz +)

   Delay:               8.575ns  (83.0% logic, 17.0% route), 24 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
C2Q_DEL     ---     4.979 *R_R37C13.CLKR to *R_R37C13.DO28 signal_process/demodu/fifo/pdp_ram_0_0_1 (from clk_AD)
ROUTE         1     1.460 *R_R37C13.DO28 to     R38C13B.A1 signal_process/demodu/sample_sum_DY[10]
C1TOFCO_DE  ---     0.401     R38C13B.A1 to    R38C13B.FCO signal_process/demodu/SLICE_152
ROUTE         1     0.000    R38C13B.FCO to    R38C13C.FCI signal_process/demodu/INS_dout_1_cry_10
FCITOFCO_D  ---     0.063    R38C13C.FCI to    R38C13C.FCO signal_process/demodu/SLICE_153
ROUTE         1     0.000    R38C13C.FCO to    R38C13D.FCI signal_process/demodu/INS_dout_1_cry_12
FCITOFCO_D  ---     0.063    R38C13D.FCI to    R38C13D.FCO signal_process/demodu/SLICE_154
ROUTE         1     0.000    R38C13D.FCO to    R38C14A.FCI signal_process/demodu/INS_dout_1_cry_14
FCITOFCO_D  ---     0.063    R38C14A.FCI to    R38C14A.FCO signal_process/demodu/SLICE_155
ROUTE         1     0.000    R38C14A.FCO to    R38C14B.FCI signal_process/demodu/INS_dout_1_cry_16
FCITOFCO_D  ---     0.063    R38C14B.FCI to    R38C14B.FCO signal_process/demodu/SLICE_156
ROUTE         1     0.000    R38C14B.FCO to    R38C14C.FCI signal_process/demodu/INS_dout_1_cry_18
FCITOFCO_D  ---     0.063    R38C14C.FCI to    R38C14C.FCO signal_process/demodu/SLICE_157
ROUTE         1     0.000    R38C14C.FCO to    R38C14D.FCI signal_process/demodu/INS_dout_1_cry_20
FCITOFCO_D  ---     0.063    R38C14D.FCI to    R38C14D.FCO signal_process/demodu/SLICE_158
ROUTE         1     0.000    R38C14D.FCO to    R38C15A.FCI signal_process/demodu/INS_dout_1_cry_22
FCITOFCO_D  ---     0.063    R38C15A.FCI to    R38C15A.FCO signal_process/demodu/SLICE_159
ROUTE         1     0.000    R38C15A.FCO to    R38C15B.FCI signal_process/demodu/INS_dout_1_cry_24
FCITOFCO_D  ---     0.063    R38C15B.FCI to    R38C15B.FCO signal_process/demodu/SLICE_160
ROUTE         1     0.000    R38C15B.FCO to    R38C15C.FCI signal_process/demodu/INS_dout_1_cry_26
FCITOFCO_D  ---     0.063    R38C15C.FCI to    R38C15C.FCO signal_process/demodu/SLICE_161
ROUTE         1     0.000    R38C15C.FCO to    R38C15D.FCI signal_process/demodu/INS_dout_1_cry_28
FCITOFCO_D  ---     0.063    R38C15D.FCI to    R38C15D.FCO signal_process/demodu/SLICE_162
ROUTE         1     0.000    R38C15D.FCO to    R38C16A.FCI signal_process/demodu/INS_dout_1_cry_30
FCITOFCO_D  ---     0.063    R38C16A.FCI to    R38C16A.FCO signal_process/demodu/SLICE_163
ROUTE         1     0.000    R38C16A.FCO to    R38C16B.FCI signal_process/demodu/INS_dout_1_cry_32
FCITOFCO_D  ---     0.063    R38C16B.FCI to    R38C16B.FCO signal_process/demodu/SLICE_164
ROUTE         1     0.000    R38C16B.FCO to    R38C16C.FCI signal_process/demodu/INS_dout_1_cry_34
FCITOFCO_D  ---     0.063    R38C16C.FCI to    R38C16C.FCO signal_process/demodu/SLICE_165
ROUTE         1     0.000    R38C16C.FCO to    R38C16D.FCI signal_process/demodu/INS_dout_1_cry_36
FCITOFCO_D  ---     0.063    R38C16D.FCI to    R38C16D.FCO signal_process/demodu/SLICE_166
ROUTE         1     0.000    R38C16D.FCO to    R38C17A.FCI signal_process/demodu/INS_dout_1_cry_38
FCITOFCO_D  ---     0.063    R38C17A.FCI to    R38C17A.FCO signal_process/demodu/SLICE_167
ROUTE         1     0.000    R38C17A.FCO to    R38C17B.FCI signal_process/demodu/INS_dout_1_cry_40
FCITOFCO_D  ---     0.063    R38C17B.FCI to    R38C17B.FCO signal_process/demodu/SLICE_168
ROUTE         1     0.000    R38C17B.FCO to    R38C17C.FCI signal_process/demodu/INS_dout_1_cry_42
FCITOFCO_D  ---     0.063    R38C17C.FCI to    R38C17C.FCO signal_process/demodu/SLICE_169
ROUTE         1     0.000    R38C17C.FCO to    R38C17D.FCI signal_process/demodu/INS_dout_1_cry_44
FCITOFCO_D  ---     0.063    R38C17D.FCI to    R38C17D.FCO signal_process/demodu/SLICE_170
ROUTE         1     0.000    R38C17D.FCO to    R38C18A.FCI signal_process/demodu/INS_dout_1_cry_46
FCITOFCO_D  ---     0.063    R38C18A.FCI to    R38C18A.FCO signal_process/demodu/SLICE_171
ROUTE         1     0.000    R38C18A.FCO to    R38C18B.FCI signal_process/demodu/INS_dout_1_cry_48
FCITOFCO_D  ---     0.063    R38C18B.FCI to    R38C18B.FCO signal_process/demodu/SLICE_172
ROUTE         1     0.000    R38C18B.FCO to    R38C18C.FCI signal_process/demodu/INS_dout_1_cry_50
FCITOFCO_D  ---     0.063    R38C18C.FCI to    R38C18C.FCO signal_process/demodu/SLICE_173
ROUTE         1     0.000    R38C18C.FCO to    R38C18D.FCI signal_process/demodu/INS_dout_1_cry_52
FCITOF1_DE  ---     0.412    R38C18D.FCI to     R38C18D.F1 signal_process/demodu/SLICE_174
ROUTE         1     0.000     R38C18D.F1 to    R38C18D.DI1 signal_process/demodu/INS_dout_1[54] (to clk120mhz)
                  --------
                    8.575   (83.0% logic, 17.0% route), 24 logic levels.

Report Summary
--------------
----------------------------------------------------------------------------
Preference                              |   Constraint|       Actual|Levels
----------------------------------------------------------------------------
                                        |             |             |
FREQUENCY NET "clk120mhz" 120.000000    |             |             |
MHz ;                                   |  120.000 MHz|  140.726 MHz|  11  
                                        |             |             |
FREQUENCY NET "wendu.clk_us" 1.000000   |             |             |
MHz ;                                   |    1.000 MHz|  107.285 MHz|  15  
                                        |             |             |
FREQUENCY NET "clk_in_c" 20.000000 MHz  |             |             |
;                                       |            -|            -|   0  
                                        |             |             |
FREQUENCY NET "clk_AD" 60.000000 MHz ;  |   60.000 MHz|  149.031 MHz|   2  
                                        |             |             |
FREQUENCY NET "clk_AD_t_c" 60.000000    |             |             |
MHz ;                                   |            -|            -|   0  
                                        |             |             |
----------------------------------------------------------------------------


All preferences were met.


Clock Domains Analysis
------------------------

Found 4 clocks:

Clock Domain: wendu.clk_us   Source: wendu/SLICE_241.Q0   Loads: 37
   Covered under: FREQUENCY NET "wendu.clk_us" 1.000000 MHz ;

Clock Domain: clk_in_c   Source: clk_in.PAD   Loads: 1
   No transfer within this clock domain is found

Clock Domain: clk_AD   Source: CLK120/PLLInst_0.CLKOS   Loads: 101
   Covered under: FREQUENCY NET "clk_AD" 60.000000 MHz ;

   Data transfers from:
   Clock Domain: clk120mhz   Source: CLK120/PLLInst_0.CLKOP
      Covered under: FREQUENCY NET "clk_AD" 60.000000 MHz ;   Transfers: 2

Clock Domain: clk120mhz   Source: CLK120/PLLInst_0.CLKOP   Loads: 446
   Covered under: FREQUENCY NET "clk120mhz" 120.000000 MHz ;

   Data transfers from:
   Clock Domain: wendu.clk_us   Source: wendu/SLICE_241.Q0
      Covered under: FREQUENCY NET "clk120mhz" 120.000000 MHz ;   Transfers: 16

   Clock Domain: clk_AD   Source: CLK120/PLLInst_0.CLKOS
      Blocked under: BLOCK PATH FROM CLKNET "clk_AD" TO CLKNET "clk120mhz" ;
      Blocked under: BLOCK PATH FROM CLKNET "clk_AD" TO CLKNET "clk120mhz" ;


Timing summary (Setup):
---------------

Timing errors: 0  Score: 0
Cumulative negative slack: 0

Constraints cover 38661 paths, 6 nets, and 4168 connections (99.21% coverage)

--------------------------------------------------------------------------------
Lattice TRACE Report - Hold, Version Diamond (64-bit) 3.12.1.454
Thu Sep 25 10:30:06 2025

Copyright (c) 1991-1994 by NeoCAD Inc. All rights reserved.
Copyright (c) 1995 AT&T Corp.   All rights reserved.
Copyright (c) 1995-2001 Lucent Technologies Inc.  All rights reserved.
Copyright (c) 2001 Agere Systems   All rights reserved.
Copyright (c) 2002-2020 Lattice Semiconductor Corporation,  All rights reserved.

Report Information
------------------
Command line:    trce -v 10 -gt -sethld -sp 7 -sphld m -o INS350_5J_JZ_impl1.twr -gui -msgset D:/Project/TLH50_03J_JZ_20250925/promote.xml INS350_5J_JZ_impl1.ncd INS350_5J_JZ_impl1.prf 
Design file:     ins350_5j_jz_impl1.ncd
Preference file: ins350_5j_jz_impl1.prf
Device,speed:    LFE5U-25F,m
Report level:    verbose report, limited to 10 items per preference
--------------------------------------------------------------------------------

BLOCK ASYNCPATHS
BLOCK RESETPATHS
BLOCK JTAG PATHS
--------------------------------------------------------------------------------


Derating parameters
-------------------
VCCIO Voltage:
                   3.300 V (Bank 0)
                   3.300 V (Bank 1, defined by PAR)
                   3.300 V (Bank 2)
                   3.300 V (Bank 3, defined by PAR)
                   3.300 V (Bank 6, defined by PAR)
                   3.300 V (Bank 7, defined by PAR)



================================================================================
Preference: FREQUENCY NET "clk120mhz" 120.000000 MHz ;
            4096 items scored, 0 timing errors detected.
--------------------------------------------------------------------------------


Passed: The following path meets requirements by 0.174ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              u_uart/U2/tx_data[3]  (from clk120mhz +)
   Destination:    FF         Data in        u_uart/U1/tx_data[3]  (to clk120mhz +)

   Delay:               0.292ns  (55.8% logic, 44.2% route), 1 logic levels.

 Constraint Details:

      0.292ns physical path delay u_uart/U2/SLICE_555 to u_uart/U1/SLICE_507 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.174ns

 Physical Path Details:

      Data path u_uart/U2/SLICE_555 to u_uart/U1/SLICE_507:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.163    R24C36B.CLK to     R24C36B.Q1 u_uart/U2/SLICE_555 (from clk120mhz)
ROUTE         1     0.129     R24C36B.Q1 to     R24C36A.M1 u_uart/tx_data[3] (to clk120mhz)
                  --------
                    0.292   (55.8% logic, 44.2% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to u_uart/U2/SLICE_555:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       446     0.653  PLL_BR0.CLKOP to    R24C36B.CLK clk120mhz
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to u_uart/U1/SLICE_507:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       446     0.653  PLL_BR0.CLKOP to    R24C36A.CLK clk120mhz
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.175ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              u_uart/U2/transmitdy[4]  (from clk120mhz +)
   Destination:    FF         Data in        u_uart/U2/transmitdy[5]  (to clk120mhz +)

   Delay:               0.293ns  (56.0% logic, 44.0% route), 1 logic levels.

 Constraint Details:

      0.293ns physical path delay u_uart/U2/SLICE_541 to u_uart/U2/SLICE_541 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.175ns

 Physical Path Details:

      Data path u_uart/U2/SLICE_541 to u_uart/U2/SLICE_541:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R33C37C.CLK to     R33C37C.Q0 u_uart/U2/SLICE_541 (from clk120mhz)
ROUTE         1     0.129     R33C37C.Q0 to     R33C37C.M1 u_uart/U2/transmitdy[4] (to clk120mhz)
                  --------
                    0.293   (56.0% logic, 44.0% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to u_uart/U2/SLICE_541:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       446     0.653  PLL_BR0.CLKOP to    R33C37C.CLK clk120mhz
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to u_uart/U2/SLICE_541:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       446     0.653  PLL_BR0.CLKOP to    R33C37C.CLK clk120mhz
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.175ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/modu/mudu_dy[2]  (from clk120mhz +)
   Destination:    FF         Data in        signal_process/modu/mudu_dy[3]  (to clk120mhz +)

   Delay:               0.293ns  (56.0% logic, 44.0% route), 1 logic levels.

 Constraint Details:

      0.293ns physical path delay signal_process/modu/SLICE_390 to signal_process/modu/SLICE_390 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.175ns

 Physical Path Details:

      Data path signal_process/modu/SLICE_390 to signal_process/modu/SLICE_390:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R17C12A.CLK to     R17C12A.Q0 signal_process/modu/SLICE_390 (from clk120mhz)
ROUTE         1     0.129     R17C12A.Q0 to     R17C12A.M1 signal_process/modu/mudu_dy[2] (to clk120mhz)
                  --------
                    0.293   (56.0% logic, 44.0% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/modu/SLICE_390:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       446     0.653  PLL_BR0.CLKOP to    R17C12A.CLK clk120mhz
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/modu/SLICE_390:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       446     0.653  PLL_BR0.CLKOP to    R17C12A.CLK clk120mhz
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.175ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              u_uart/U2/tx_data[4]  (from clk120mhz +)
   Destination:    FF         Data in        u_uart/U1/tx_data[4]  (to clk120mhz +)

   Delay:               0.293ns  (56.0% logic, 44.0% route), 1 logic levels.

 Constraint Details:

      0.293ns physical path delay u_uart/U2/SLICE_556 to u_uart/U1/SLICE_508 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.175ns

 Physical Path Details:

      Data path u_uart/U2/SLICE_556 to u_uart/U1/SLICE_508:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R24C34D.CLK to     R24C34D.Q0 u_uart/U2/SLICE_556 (from clk120mhz)
ROUTE         1     0.129     R24C34D.Q0 to     R24C34B.M0 u_uart/tx_data[4] (to clk120mhz)
                  --------
                    0.293   (56.0% logic, 44.0% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to u_uart/U2/SLICE_556:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       446     0.653  PLL_BR0.CLKOP to    R24C34D.CLK clk120mhz
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to u_uart/U1/SLICE_508:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       446     0.653  PLL_BR0.CLKOP to    R24C34B.CLK clk120mhz
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.176ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/modu/mudu_dy[5]  (from clk120mhz +)
   Destination:    FF         Data in        signal_process/modu/mudu_dy[6]  (to clk120mhz +)

   Delay:               0.294ns  (55.4% logic, 44.6% route), 1 logic levels.

 Constraint Details:

      0.294ns physical path delay signal_process/modu/SLICE_391 to signal_process/modu/SLICE_639 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.176ns

 Physical Path Details:

      Data path signal_process/modu/SLICE_391 to signal_process/modu/SLICE_639:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.163     R14C9A.CLK to      R14C9A.Q1 signal_process/modu/SLICE_391 (from clk120mhz)
ROUTE         2     0.131      R14C9A.Q1 to      R14C9D.M0 signal_process/modu/mudu_dy[5] (to clk120mhz)
                  --------
                    0.294   (55.4% logic, 44.6% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/modu/SLICE_391:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       446     0.653  PLL_BR0.CLKOP to     R14C9A.CLK clk120mhz
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/modu/SLICE_639:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       446     0.653  PLL_BR0.CLKOP to     R14C9D.CLK clk120mhz
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.176ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              u_uart/U2/tx_state[5]  (from clk120mhz +)
   Destination:    FF         Data in        u_uart/U2/tx_state[6]  (to clk120mhz +)

   Delay:               0.294ns  (55.4% logic, 44.6% route), 1 logic levels.

 Constraint Details:

      0.294ns physical path delay u_uart/U2/SLICE_549 to u_uart/U2/SLICE_550 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.176ns

 Physical Path Details:

      Data path u_uart/U2/SLICE_549 to u_uart/U2/SLICE_550:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.163    R30C35C.CLK to     R30C35C.Q1 u_uart/U2/SLICE_549 (from clk120mhz)
ROUTE        10     0.131     R30C35C.Q1 to     R30C35B.M0 u_uart/U2/tx_state[5] (to clk120mhz)
                  --------
                    0.294   (55.4% logic, 44.6% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to u_uart/U2/SLICE_549:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       446     0.653  PLL_BR0.CLKOP to    R30C35C.CLK clk120mhz
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to u_uart/U2/SLICE_550:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       446     0.653  PLL_BR0.CLKOP to    R30C35B.CLK clk120mhz
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.177ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              u_uart/U2/tx_state[4]  (from clk120mhz +)
   Destination:    FF         Data in        u_uart/U2/tx_state[5]  (to clk120mhz +)

   Delay:               0.295ns  (55.6% logic, 44.4% route), 1 logic levels.

 Constraint Details:

      0.295ns physical path delay u_uart/U2/SLICE_549 to u_uart/U2/SLICE_549 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.177ns

 Physical Path Details:

      Data path u_uart/U2/SLICE_549 to u_uart/U2/SLICE_549:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R30C35C.CLK to     R30C35C.Q0 u_uart/U2/SLICE_549 (from clk120mhz)
ROUTE        10     0.131     R30C35C.Q0 to     R30C35C.M1 u_uart/U2/tx_state[4] (to clk120mhz)
                  --------
                    0.295   (55.6% logic, 44.4% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to u_uart/U2/SLICE_549:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       446     0.653  PLL_BR0.CLKOP to    R30C35C.CLK clk120mhz
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to u_uart/U2/SLICE_549:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       446     0.653  PLL_BR0.CLKOP to    R30C35C.CLK clk120mhz
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.177ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/rs422/RS_dout[0]  (from clk120mhz +)
   Destination:    FF         Data in        signal_process/rs422/dout[0]  (to clk120mhz +)

   Delay:               0.295ns  (55.6% logic, 44.4% route), 1 logic levels.

 Constraint Details:

      0.295ns physical path delay signal_process/rs422/SLICE_454 to signal_process/rs422/SLICE_242 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.177ns

 Physical Path Details:

      Data path signal_process/rs422/SLICE_454 to signal_process/rs422/SLICE_242:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R26C33A.CLK to     R26C33A.Q0 signal_process/rs422/SLICE_454 (from clk120mhz)
ROUTE         2     0.131     R26C33A.Q0 to     R26C33C.M0 signal_process/rs422/dout_1[0] (to clk120mhz)
                  --------
                    0.295   (55.6% logic, 44.4% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/rs422/SLICE_454:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       446     0.653  PLL_BR0.CLKOP to    R26C33A.CLK clk120mhz
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/rs422/SLICE_242:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       446     0.653  PLL_BR0.CLKOP to    R26C33C.CLK clk120mhz
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.177ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              u_uart/U2/tx_state[6]  (from clk120mhz +)
   Destination:    FF         Data in        u_uart/U2/tx_state[7]  (to clk120mhz +)

   Delay:               0.295ns  (55.6% logic, 44.4% route), 1 logic levels.

 Constraint Details:

      0.295ns physical path delay u_uart/U2/SLICE_550 to u_uart/U2/SLICE_550 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.177ns

 Physical Path Details:

      Data path u_uart/U2/SLICE_550 to u_uart/U2/SLICE_550:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R30C35B.CLK to     R30C35B.Q0 u_uart/U2/SLICE_550 (from clk120mhz)
ROUTE        10     0.131     R30C35B.Q0 to     R30C35B.M1 u_uart/U2/tx_state[6] (to clk120mhz)
                  --------
                    0.295   (55.6% logic, 44.4% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to u_uart/U2/SLICE_550:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       446     0.653  PLL_BR0.CLKOP to    R30C35B.CLK clk120mhz
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to u_uart/U2/SLICE_550:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       446     0.653  PLL_BR0.CLKOP to    R30C35B.CLK clk120mhz
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.177ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/integ/inte_dy[0]  (from clk120mhz +)
   Destination:    FF         Data in        signal_process/integ/inte_dy[1]  (to clk120mhz +)

   Delay:               0.295ns  (55.6% logic, 44.4% route), 1 logic levels.

 Constraint Details:

      0.295ns physical path delay signal_process/integ/SLICE_386 to signal_process/integ/SLICE_386 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.177ns

 Physical Path Details:

      Data path signal_process/integ/SLICE_386 to signal_process/integ/SLICE_386:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R17C21D.CLK to     R17C21D.Q0 signal_process/integ/SLICE_386 (from clk120mhz)
ROUTE         2     0.131     R17C21D.Q0 to     R17C21D.M1 signal_process/integ/inte_dy[0] (to clk120mhz)
                  --------
                    0.295   (55.6% logic, 44.4% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/integ/SLICE_386:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       446     0.653  PLL_BR0.CLKOP to    R17C21D.CLK clk120mhz
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/integ/SLICE_386:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       446     0.653  PLL_BR0.CLKOP to    R17C21D.CLK clk120mhz
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.


================================================================================
Preference: FREQUENCY NET "wendu.clk_us" 1.000000 MHz ;
            3495 items scored, 0 timing errors detected.
--------------------------------------------------------------------------------


Passed: The following path meets requirements by 0.175ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/data_temp[0]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/data[0]  (to wendu.clk_us +)

   Delay:               0.293ns  (56.0% logic, 44.0% route), 1 logic levels.

 Constraint Details:

      0.293ns physical path delay wendu/SLICE_577 to wendu/SLICE_489 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.175ns

 Physical Path Details:

      Data path wendu/SLICE_577 to wendu/SLICE_489:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R32C34C.CLK to     R32C34C.Q0 wendu/SLICE_577 (from wendu.clk_us)
ROUTE         1     0.129     R32C34C.Q0 to     R32C34B.M0 wendu/data_temp[0] (to wendu.clk_us)
                  --------
                    0.293   (56.0% logic, 44.0% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path wendu/SLICE_241 to wendu/SLICE_577:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.897     R38C31A.Q0 to    R32C34C.CLK wendu.clk_us
                  --------
                    0.897   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path wendu/SLICE_241 to wendu/SLICE_489:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.897     R38C31A.Q0 to    R32C34B.CLK wendu.clk_us
                  --------
                    0.897   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.176ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/data_temp[11]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/data_temp[10]  (to wendu.clk_us +)

   Delay:               0.294ns  (55.4% logic, 44.6% route), 1 logic levels.

 Constraint Details:

      0.294ns physical path delay wendu/SLICE_582 to wendu/SLICE_582 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.176ns

 Physical Path Details:

      Data path wendu/SLICE_582 to wendu/SLICE_582:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.163    R31C36A.CLK to     R31C36A.Q1 wendu/SLICE_582 (from wendu.clk_us)
ROUTE         2     0.131     R31C36A.Q1 to     R31C36A.M0 wendu/data_temp[11] (to wendu.clk_us)
                  --------
                    0.294   (55.4% logic, 44.6% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path wendu/SLICE_241 to wendu/SLICE_582:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.897     R38C31A.Q0 to    R31C36A.CLK wendu.clk_us
                  --------
                    0.897   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path wendu/SLICE_241 to wendu/SLICE_582:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.897     R38C31A.Q0 to    R31C36A.CLK wendu.clk_us
                  --------
                    0.897   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.176ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/data_temp[1]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/data[1]  (to wendu.clk_us +)

   Delay:               0.294ns  (55.4% logic, 44.6% route), 1 logic levels.

 Constraint Details:

      0.294ns physical path delay wendu/SLICE_577 to wendu/SLICE_489 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.176ns

 Physical Path Details:

      Data path wendu/SLICE_577 to wendu/SLICE_489:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.163    R32C34C.CLK to     R32C34C.Q1 wendu/SLICE_577 (from wendu.clk_us)
ROUTE         2     0.131     R32C34C.Q1 to     R32C34B.M1 wendu/data_temp[1] (to wendu.clk_us)
                  --------
                    0.294   (55.4% logic, 44.6% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path wendu/SLICE_241 to wendu/SLICE_577:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.897     R38C31A.Q0 to    R32C34C.CLK wendu.clk_us
                  --------
                    0.897   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path wendu/SLICE_241 to wendu/SLICE_489:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.897     R38C31A.Q0 to    R32C34B.CLK wendu.clk_us
                  --------
                    0.897   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.176ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/data_temp[5]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/data_temp[4]  (to wendu.clk_us +)

   Delay:               0.294ns  (55.4% logic, 44.6% route), 1 logic levels.

 Constraint Details:

      0.294ns physical path delay wendu/SLICE_579 to wendu/SLICE_579 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.176ns

 Physical Path Details:

      Data path wendu/SLICE_579 to wendu/SLICE_579:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.163    R32C35C.CLK to     R32C35C.Q1 wendu/SLICE_579 (from wendu.clk_us)
ROUTE         2     0.131     R32C35C.Q1 to     R32C35C.M0 wendu/data_temp[5] (to wendu.clk_us)
                  --------
                    0.294   (55.4% logic, 44.6% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path wendu/SLICE_241 to wendu/SLICE_579:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.897     R38C31A.Q0 to    R32C35C.CLK wendu.clk_us
                  --------
                    0.897   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path wendu/SLICE_241 to wendu/SLICE_579:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.897     R38C31A.Q0 to    R32C35C.CLK wendu.clk_us
                  --------
                    0.897   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.176ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/data_temp[5]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/data[5]  (to wendu.clk_us +)

   Delay:               0.294ns  (55.4% logic, 44.6% route), 1 logic levels.

 Constraint Details:

      0.294ns physical path delay wendu/SLICE_579 to wendu/SLICE_491 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.176ns

 Physical Path Details:

      Data path wendu/SLICE_579 to wendu/SLICE_491:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.163    R32C35C.CLK to     R32C35C.Q1 wendu/SLICE_579 (from wendu.clk_us)
ROUTE         2     0.131     R32C35C.Q1 to     R32C35A.M1 wendu/data_temp[5] (to wendu.clk_us)
                  --------
                    0.294   (55.4% logic, 44.6% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path wendu/SLICE_241 to wendu/SLICE_579:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.897     R38C31A.Q0 to    R32C35C.CLK wendu.clk_us
                  --------
                    0.897   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path wendu/SLICE_241 to wendu/SLICE_491:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.897     R38C31A.Q0 to    R32C35A.CLK wendu.clk_us
                  --------
                    0.897   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.176ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/data_temp[13]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/data_temp[12]  (to wendu.clk_us +)

   Delay:               0.294ns  (55.4% logic, 44.6% route), 1 logic levels.

 Constraint Details:

      0.294ns physical path delay wendu/SLICE_583 to wendu/SLICE_583 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.176ns

 Physical Path Details:

      Data path wendu/SLICE_583 to wendu/SLICE_583:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.163    R31C35C.CLK to     R31C35C.Q1 wendu/SLICE_583 (from wendu.clk_us)
ROUTE         2     0.131     R31C35C.Q1 to     R31C35C.M0 wendu/data_temp[13] (to wendu.clk_us)
                  --------
                    0.294   (55.4% logic, 44.6% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path wendu/SLICE_241 to wendu/SLICE_583:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.897     R38C31A.Q0 to    R31C35C.CLK wendu.clk_us
                  --------
                    0.897   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path wendu/SLICE_241 to wendu/SLICE_583:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.897     R38C31A.Q0 to    R31C35C.CLK wendu.clk_us
                  --------
                    0.897   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.176ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/data_temp[1]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/data_temp[0]  (to wendu.clk_us +)

   Delay:               0.294ns  (55.4% logic, 44.6% route), 1 logic levels.

 Constraint Details:

      0.294ns physical path delay wendu/SLICE_577 to wendu/SLICE_577 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.176ns

 Physical Path Details:

      Data path wendu/SLICE_577 to wendu/SLICE_577:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.163    R32C34C.CLK to     R32C34C.Q1 wendu/SLICE_577 (from wendu.clk_us)
ROUTE         2     0.131     R32C34C.Q1 to     R32C34C.M0 wendu/data_temp[1] (to wendu.clk_us)
                  --------
                    0.294   (55.4% logic, 44.6% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path wendu/SLICE_241 to wendu/SLICE_577:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.897     R38C31A.Q0 to    R32C34C.CLK wendu.clk_us
                  --------
                    0.897   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path wendu/SLICE_241 to wendu/SLICE_577:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.897     R38C31A.Q0 to    R32C34C.CLK wendu.clk_us
                  --------
                    0.897   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.176ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/data_temp[3]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/data_temp[2]  (to wendu.clk_us +)

   Delay:               0.294ns  (55.4% logic, 44.6% route), 1 logic levels.

 Constraint Details:

      0.294ns physical path delay wendu/SLICE_578 to wendu/SLICE_578 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.176ns

 Physical Path Details:

      Data path wendu/SLICE_578 to wendu/SLICE_578:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.163    R32C35B.CLK to     R32C35B.Q1 wendu/SLICE_578 (from wendu.clk_us)
ROUTE         2     0.131     R32C35B.Q1 to     R32C35B.M0 wendu/data_temp[3] (to wendu.clk_us)
                  --------
                    0.294   (55.4% logic, 44.6% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path wendu/SLICE_241 to wendu/SLICE_578:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.897     R38C31A.Q0 to    R32C35B.CLK wendu.clk_us
                  --------
                    0.897   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path wendu/SLICE_241 to wendu/SLICE_578:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.897     R38C31A.Q0 to    R32C35B.CLK wendu.clk_us
                  --------
                    0.897   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.176ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/data_temp[7]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/data_temp[6]  (to wendu.clk_us +)

   Delay:               0.294ns  (55.4% logic, 44.6% route), 1 logic levels.

 Constraint Details:

      0.294ns physical path delay wendu/SLICE_580 to wendu/SLICE_580 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.176ns

 Physical Path Details:

      Data path wendu/SLICE_580 to wendu/SLICE_580:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.163    R32C36C.CLK to     R32C36C.Q1 wendu/SLICE_580 (from wendu.clk_us)
ROUTE         2     0.131     R32C36C.Q1 to     R32C36C.M0 wendu/data_temp[7] (to wendu.clk_us)
                  --------
                    0.294   (55.4% logic, 44.6% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path wendu/SLICE_241 to wendu/SLICE_580:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.897     R38C31A.Q0 to    R32C36C.CLK wendu.clk_us
                  --------
                    0.897   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path wendu/SLICE_241 to wendu/SLICE_580:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.897     R38C31A.Q0 to    R32C36C.CLK wendu.clk_us
                  --------
                    0.897   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.176ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/data_temp[9]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/data_temp[8]  (to wendu.clk_us +)

   Delay:               0.294ns  (55.4% logic, 44.6% route), 1 logic levels.

 Constraint Details:

      0.294ns physical path delay wendu/SLICE_581 to wendu/SLICE_581 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.176ns

 Physical Path Details:

      Data path wendu/SLICE_581 to wendu/SLICE_581:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.163    R31C35A.CLK to     R31C35A.Q1 wendu/SLICE_581 (from wendu.clk_us)
ROUTE         2     0.131     R31C35A.Q1 to     R31C35A.M0 wendu/data_temp[9] (to wendu.clk_us)
                  --------
                    0.294   (55.4% logic, 44.6% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path wendu/SLICE_241 to wendu/SLICE_581:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.897     R38C31A.Q0 to    R31C35A.CLK wendu.clk_us
                  --------
                    0.897   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path wendu/SLICE_241 to wendu/SLICE_581:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.897     R38C31A.Q0 to    R31C35A.CLK wendu.clk_us
                  --------
                    0.897   (0.0% logic, 100.0% route), 0 logic levels.


================================================================================
Preference: FREQUENCY NET "clk_in_c" 20.000000 MHz ;
            0 items scored, 0 timing errors detected.
--------------------------------------------------------------------------------


================================================================================
Preference: FREQUENCY NET "clk_AD" 60.000000 MHz ;
            3104 items scored, 0 timing errors detected.
--------------------------------------------------------------------------------


Passed: The following path meets requirements by 0.178ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/sample_sum[0]  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/sample_sum[0]  (to clk_AD +)

   Delay:               0.297ns  (80.8% logic, 19.2% route), 2 logic levels.

 Constraint Details:

      0.297ns physical path delay signal_process/demodu/SLICE_355 to signal_process/demodu/SLICE_355 meets
      0.119ns DIN_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.119ns) by 0.178ns

 Physical Path Details:

      Data path signal_process/demodu/SLICE_355 to signal_process/demodu/SLICE_355:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R32C11A.CLK to     R32C11A.Q0 signal_process/demodu/SLICE_355 (from clk_AD)
ROUTE         4     0.057     R32C11A.Q0 to     R32C11A.D0 signal_process/demodu/sample_sum[0]
CTOF_DEL    ---     0.076     R32C11A.D0 to     R32C11A.F0 signal_process/demodu/SLICE_355
ROUTE         1     0.000     R32C11A.F0 to    R32C11A.DI0 signal_process/demodu/un3_sample_sum_axb_0 (to clk_AD)
                  --------
                    0.297   (80.8% logic, 19.2% route), 2 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_355:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653  PLL_BR0.CLKOS to    R32C11A.CLK clk_AD
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_355:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653  PLL_BR0.CLKOS to    R32C11A.CLK clk_AD
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.179ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/AD_valid_dy[0]  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/AD_valid_dy[1]  (to clk_AD +)

   Delay:               0.297ns  (55.2% logic, 44.8% route), 1 logic levels.

 Constraint Details:

      0.297ns physical path delay signal_process/demodu/SLICE_284 to signal_process/demodu/SLICE_284 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.179ns

 Physical Path Details:

      Data path signal_process/demodu/SLICE_284 to signal_process/demodu/SLICE_284:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R22C23D.CLK to     R22C23D.Q0 signal_process/demodu/SLICE_284 (from clk_AD)
ROUTE         2     0.133     R22C23D.Q0 to     R22C23D.M1 signal_process/demodu/AD_valid_dy[0] (to clk_AD)
                  --------
                    0.297   (55.2% logic, 44.8% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_284:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653  PLL_BR0.CLKOS to    R22C23D.CLK clk_AD
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_284:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653  PLL_BR0.CLKOS to    R22C23D.CLK clk_AD
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.223ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/latch_sample_sum[34]  (from clk_AD +)
   Destination:    PDPW16KD   Port           signal_process/demodu/fifo/pdp_ram_0_0_1(ASIC)  (to clk_AD +)

   Delay:               0.325ns  (50.5% logic, 49.5% route), 1 logic levels.

 Constraint Details:

      0.325ns physical path delay signal_process/demodu/SLICE_316 to signal_process/demodu/fifo/pdp_ram_0_0_1 meets
      0.059ns DATA_HLD and
      0.000ns delay constraint less
     -0.043ns skew requirement (totaling 0.102ns) by 0.223ns

 Physical Path Details:

      Data path signal_process/demodu/SLICE_316 to signal_process/demodu/fifo/pdp_ram_0_0_1:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R36C15C.CLK to     R36C15C.Q0 signal_process/demodu/SLICE_316 (from clk_AD)
ROUTE         1     0.161     R36C15C.Q0 to *R_R37C13.DI34 signal_process/demodu/latch_sample_sum[34] (to clk_AD)
                  --------
                    0.325   (50.5% logic, 49.5% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_316:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653  PLL_BR0.CLKOS to    R36C15C.CLK clk_AD
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/demodu/fifo/pdp_ram_0_0_1:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.696  PLL_BR0.CLKOS to *R_R37C13.CLKW clk_AD
                  --------
                    0.696   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.251ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/latch_sample_sum[51]  (from clk_AD +)
   Destination:    PDPW16KD   Port           signal_process/demodu/fifo/pdp_ram_0_1_0(ASIC)  (to clk_AD +)

   Delay:               0.345ns  (47.2% logic, 52.8% route), 1 logic levels.

 Constraint Details:

      0.345ns physical path delay signal_process/demodu/SLICE_324 to signal_process/demodu/fifo/pdp_ram_0_1_0 meets
      0.051ns DATA_HLD and
      0.000ns delay constraint less
     -0.043ns skew requirement (totaling 0.094ns) by 0.251ns

 Physical Path Details:

      Data path signal_process/demodu/SLICE_324 to signal_process/demodu/fifo/pdp_ram_0_1_0:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.163    R36C17B.CLK to     R36C17B.Q1 signal_process/demodu/SLICE_324 (from clk_AD)
ROUTE         1     0.182     R36C17B.Q1 to *R_R37C15.DI15 signal_process/demodu/latch_sample_sum[51] (to clk_AD)
                  --------
                    0.345   (47.2% logic, 52.8% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_324:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653  PLL_BR0.CLKOS to    R36C17B.CLK clk_AD
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/demodu/fifo/pdp_ram_0_1_0:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.696  PLL_BR0.CLKOS to *R_R37C15.CLKW clk_AD
                  --------
                    0.696   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.252ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/latch_sample_sum[50]  (from clk_AD +)
   Destination:    PDPW16KD   Port           signal_process/demodu/fifo/pdp_ram_0_1_0(ASIC)  (to clk_AD +)

   Delay:               0.346ns  (47.4% logic, 52.6% route), 1 logic levels.

 Constraint Details:

      0.346ns physical path delay signal_process/demodu/SLICE_324 to signal_process/demodu/fifo/pdp_ram_0_1_0 meets
      0.051ns DATA_HLD and
      0.000ns delay constraint less
     -0.043ns skew requirement (totaling 0.094ns) by 0.252ns

 Physical Path Details:

      Data path signal_process/demodu/SLICE_324 to signal_process/demodu/fifo/pdp_ram_0_1_0:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R36C17B.CLK to     R36C17B.Q0 signal_process/demodu/SLICE_324 (from clk_AD)
ROUTE         1     0.182     R36C17B.Q0 to *R_R37C15.DI14 signal_process/demodu/latch_sample_sum[50] (to clk_AD)
                  --------
                    0.346   (47.4% logic, 52.6% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_324:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653  PLL_BR0.CLKOS to    R36C17B.CLK clk_AD
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/demodu/fifo/pdp_ram_0_1_0:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.696  PLL_BR0.CLKOS to *R_R37C15.CLKW clk_AD
                  --------
                    0.696   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.256ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/AD_valid_dy[1]  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/AD_validcnt[0]  (to clk_AD +)

   Delay:               0.375ns  (63.7% logic, 36.3% route), 2 logic levels.

 Constraint Details:

      0.375ns physical path delay signal_process/demodu/SLICE_284 to signal_process/demodu/SLICE_285 meets
      0.119ns DIN_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.119ns) by 0.256ns

 Physical Path Details:

      Data path signal_process/demodu/SLICE_284 to signal_process/demodu/SLICE_285:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.163    R22C23D.CLK to     R22C23D.Q1 signal_process/demodu/SLICE_284 (from clk_AD)
ROUTE         1     0.134     R22C23D.Q1 to     R22C23C.C0 signal_process/demodu/AD_valid_dy[1]
CTOF_DEL    ---     0.076     R22C23C.C0 to     R22C23C.F0 signal_process/demodu/SLICE_285
ROUTE        34     0.002     R22C23C.F0 to    R22C23C.DI0 signal_process/demodu/AD_validcnt7 (to clk_AD)
                  --------
                    0.375   (63.7% logic, 36.3% route), 2 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_284:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653  PLL_BR0.CLKOS to    R22C23D.CLK clk_AD
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_285:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653  PLL_BR0.CLKOS to    R22C23C.CLK clk_AD
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.257ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/AD_validcnt[1]  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/Latch_sum  (to clk_AD +)

   Delay:               0.376ns  (63.8% logic, 36.2% route), 2 logic levels.

 Constraint Details:

      0.376ns physical path delay signal_process/demodu/SLICE_205 to signal_process/demodu/SLICE_287 meets
      0.119ns DIN_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.119ns) by 0.257ns

 Physical Path Details:

      Data path signal_process/demodu/SLICE_205 to signal_process/demodu/SLICE_287:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R22C24B.CLK to     R22C24B.Q0 signal_process/demodu/SLICE_205 (from clk_AD)
ROUTE         3     0.136     R22C24B.Q0 to     R23C24A.D0 signal_process/demodu/AD_validcnt[1]
CTOF_DEL    ---     0.076     R23C24A.D0 to     R23C24A.F0 signal_process/demodu/SLICE_287
ROUTE         1     0.000     R23C24A.F0 to    R23C24A.DI0 signal_process/demodu/Latch_sumc (to clk_AD)
                  --------
                    0.376   (63.8% logic, 36.2% route), 2 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_205:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653  PLL_BR0.CLKOS to    R22C24B.CLK clk_AD
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_287:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653  PLL_BR0.CLKOS to    R23C24A.CLK clk_AD
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.261ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/sample_sum[48]  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/latch_sample_sum[48]  (to clk_AD +)

   Delay:               0.379ns  (43.0% logic, 57.0% route), 1 logic levels.

 Constraint Details:

      0.379ns physical path delay signal_process/demodu/SLICE_199 to signal_process/demodu/SLICE_323 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.261ns

 Physical Path Details:

      Data path signal_process/demodu/SLICE_199 to signal_process/demodu/SLICE_323:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.163    R33C17A.CLK to     R33C17A.Q1 signal_process/demodu/SLICE_199 (from clk_AD)
ROUTE         3     0.216     R33C17A.Q1 to     R35C17C.M0 signal_process/demodu/sample_sum[48] (to clk_AD)
                  --------
                    0.379   (43.0% logic, 57.0% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_199:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653  PLL_BR0.CLKOS to    R33C17A.CLK clk_AD
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_323:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653  PLL_BR0.CLKOS to    R35C17C.CLK clk_AD
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.262ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/sample_sum[7]  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/latch_sample_sum[7]  (to clk_AD +)

   Delay:               0.380ns  (43.2% logic, 56.8% route), 1 logic levels.

 Constraint Details:

      0.380ns physical path delay signal_process/demodu/SLICE_179 to signal_process/demodu/SLICE_302 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.262ns

 Physical Path Details:

      Data path signal_process/demodu/SLICE_179 to signal_process/demodu/SLICE_302:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R33C12A.CLK to     R33C12A.Q0 signal_process/demodu/SLICE_179 (from clk_AD)
ROUTE         3     0.216     R33C12A.Q0 to     R35C12A.M1 signal_process/demodu/sample_sum[7] (to clk_AD)
                  --------
                    0.380   (43.2% logic, 56.8% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_179:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653  PLL_BR0.CLKOS to    R33C12A.CLK clk_AD
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_302:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653  PLL_BR0.CLKOS to    R35C12A.CLK clk_AD
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.263ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/sample_sum[10]  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/latch_sample_sum[10]  (to clk_AD +)

   Delay:               0.381ns  (42.8% logic, 57.2% route), 1 logic levels.

 Constraint Details:

      0.381ns physical path delay signal_process/demodu/SLICE_180 to signal_process/demodu/SLICE_304 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.263ns

 Physical Path Details:

      Data path signal_process/demodu/SLICE_180 to signal_process/demodu/SLICE_304:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.163    R33C12B.CLK to     R33C12B.Q1 signal_process/demodu/SLICE_180 (from clk_AD)
ROUTE         3     0.218     R33C12B.Q1 to     R34C12A.M0 signal_process/demodu/sample_sum[10] (to clk_AD)
                  --------
                    0.381   (42.8% logic, 57.2% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_180:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653  PLL_BR0.CLKOS to    R33C12B.CLK clk_AD
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_304:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653  PLL_BR0.CLKOS to    R34C12A.CLK clk_AD
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.


================================================================================
Preference: FREQUENCY NET "clk_AD_t_c" 60.000000 MHz ;
            0 items scored, 0 timing errors detected.
--------------------------------------------------------------------------------


================================================================================
Preference: BLOCK PATH FROM CLKNET "clk_AD" TO CLKNET "clk120mhz" ;
            1596 items scored.
--------------------------------------------------------------------------------


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/fifo/FF_19  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/Read_en  (to clk120mhz +)

   Delay:               0.381ns  (63.0% logic, 37.0% route), 2 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.194    R40C20C.CLK to     R40C20C.Q0 signal_process/demodu/fifo/SLICE_298 (from clk_AD)
ROUTE         6     0.199     R40C20C.Q0 to     R41C20D.D0 signal_process/demodu/fifo/empty_flag
CTOF_DEL    ---     0.089     R41C20D.D0 to     R41C20D.F0 signal_process/demodu/SLICE_288
ROUTE         1     0.000     R41C20D.F0 to    R41C20D.DI0 signal_process/demodu/empty_flag_i (to clk120mhz)
                  --------
                    0.482   (58.7% logic, 41.3% route), 2 logic levels.


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/sample_sum[39]  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/median_sum_n[39]  (to clk120mhz +)

   Delay:               0.380ns  (43.2% logic, 56.8% route), 1 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.194    R33C16A.CLK to     R33C16A.Q0 signal_process/demodu/SLICE_195 (from clk_AD)
ROUTE         3     0.263     R33C16A.Q0 to     R35C16B.M1 signal_process/demodu/sample_sum[39] (to clk120mhz)
                  --------
                    0.457   (42.5% logic, 57.5% route), 1 logic levels.


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/sample_sum[10]  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/median_sum_n[10]  (to clk120mhz +)

   Delay:               0.381ns  (42.8% logic, 57.2% route), 1 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.193    R33C12B.CLK to     R33C12B.Q1 signal_process/demodu/SLICE_180 (from clk_AD)
ROUTE         3     0.266     R33C12B.Q1 to     R34C12D.M0 signal_process/demodu/sample_sum[10] (to clk120mhz)
                  --------
                    0.459   (42.0% logic, 58.0% route), 1 logic levels.


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/sample_sum[2]  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/median_sum_n[2]  (to clk120mhz +)

   Delay:               0.381ns  (42.8% logic, 57.2% route), 1 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.193    R33C11B.CLK to     R33C11B.Q1 signal_process/demodu/SLICE_176 (from clk_AD)
ROUTE         3     0.266     R33C11B.Q1 to     R35C11C.M0 signal_process/demodu/sample_sum[2] (to clk120mhz)
                  --------
                    0.459   (42.0% logic, 58.0% route), 1 logic levels.


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/sample_sum[4]  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/median_sum_n[4]  (to clk120mhz +)

   Delay:               0.381ns  (42.8% logic, 57.2% route), 1 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.193    R33C11C.CLK to     R33C11C.Q1 signal_process/demodu/SLICE_177 (from clk_AD)
ROUTE         3     0.266     R33C11C.Q1 to     R34C11A.M0 signal_process/demodu/sample_sum[4] (to clk120mhz)
                  --------
                    0.459   (42.0% logic, 58.0% route), 1 logic levels.


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/sample_sum[40]  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/median_sum_n[40]  (to clk120mhz +)

   Delay:               0.381ns  (42.8% logic, 57.2% route), 1 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.193    R33C16A.CLK to     R33C16A.Q1 signal_process/demodu/SLICE_195 (from clk_AD)
ROUTE         3     0.266     R33C16A.Q1 to     R34C16C.M0 signal_process/demodu/sample_sum[40] (to clk120mhz)
                  --------
                    0.459   (42.0% logic, 58.0% route), 1 logic levels.


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/sample_sum[15]  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/median_sum_n[15]  (to clk120mhz +)

   Delay:               0.382ns  (42.9% logic, 57.1% route), 1 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.194    R33C13A.CLK to     R33C13A.Q0 signal_process/demodu/SLICE_183 (from clk_AD)
ROUTE         3     0.265     R33C13A.Q0 to     R35C13D.M1 signal_process/demodu/sample_sum[15] (to clk120mhz)
                  --------
                    0.459   (42.3% logic, 57.7% route), 1 logic levels.


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/sample_sum[5]  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/median_sum_n[5]  (to clk120mhz +)

   Delay:               0.382ns  (42.9% logic, 57.1% route), 1 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.194    R33C11D.CLK to     R33C11D.Q0 signal_process/demodu/SLICE_178 (from clk_AD)
ROUTE         3     0.265     R33C11D.Q0 to     R34C11A.M1 signal_process/demodu/sample_sum[5] (to clk120mhz)
                  --------
                    0.459   (42.3% logic, 57.7% route), 1 logic levels.


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/sample_sum[11]  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/median_sum_n[11]  (to clk120mhz +)

   Delay:               0.382ns  (42.9% logic, 57.1% route), 1 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.194    R33C12C.CLK to     R33C12C.Q0 signal_process/demodu/SLICE_181 (from clk_AD)
ROUTE         3     0.265     R33C12C.Q0 to     R34C12D.M1 signal_process/demodu/sample_sum[11] (to clk120mhz)
                  --------
                    0.459   (42.3% logic, 57.7% route), 1 logic levels.


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/sample_sum[3]  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/median_sum_n[3]  (to clk120mhz +)

   Delay:               0.382ns  (42.9% logic, 57.1% route), 1 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.194    R33C11C.CLK to     R33C11C.Q0 signal_process/demodu/SLICE_177 (from clk_AD)
ROUTE         3     0.265     R33C11C.Q0 to     R35C11C.M1 signal_process/demodu/sample_sum[3] (to clk120mhz)
                  --------
                    0.459   (42.3% logic, 57.7% route), 1 logic levels.

Report Summary
--------------
----------------------------------------------------------------------------
Preference(MIN Delays)                  |   Constraint|       Actual|Levels
----------------------------------------------------------------------------
                                        |             |             |
FREQUENCY NET "clk120mhz" 120.000000    |             |             |
MHz ;                                   |     0.000 ns|     0.174 ns|   1  
                                        |             |             |
FREQUENCY NET "wendu.clk_us" 1.000000   |             |             |
MHz ;                                   |     0.000 ns|     0.175 ns|   1  
                                        |             |             |
FREQUENCY NET "clk_in_c" 20.000000 MHz  |             |             |
;                                       |            -|            -|   0  
                                        |             |             |
FREQUENCY NET "clk_AD" 60.000000 MHz ;  |     0.000 ns|     0.178 ns|   2  
                                        |             |             |
FREQUENCY NET "clk_AD_t_c" 60.000000    |             |             |
MHz ;                                   |            -|            -|   0  
                                        |             |             |
----------------------------------------------------------------------------


All preferences were met.


Clock Domains Analysis
------------------------

Found 4 clocks:

Clock Domain: wendu.clk_us   Source: wendu/SLICE_241.Q0   Loads: 37
   Covered under: FREQUENCY NET "wendu.clk_us" 1.000000 MHz ;

Clock Domain: clk_in_c   Source: clk_in.PAD   Loads: 1
   No transfer within this clock domain is found

Clock Domain: clk_AD   Source: CLK120/PLLInst_0.CLKOS   Loads: 101
   Covered under: FREQUENCY NET "clk_AD" 60.000000 MHz ;

   Data transfers from:
   Clock Domain: clk120mhz   Source: CLK120/PLLInst_0.CLKOP
      Covered under: FREQUENCY NET "clk_AD" 60.000000 MHz ;   Transfers: 2

Clock Domain: clk120mhz   Source: CLK120/PLLInst_0.CLKOP   Loads: 446
   Covered under: FREQUENCY NET "clk120mhz" 120.000000 MHz ;

   Data transfers from:
   Clock Domain: wendu.clk_us   Source: wendu/SLICE_241.Q0
      Covered under: FREQUENCY NET "clk120mhz" 120.000000 MHz ;   Transfers: 16

   Clock Domain: clk_AD   Source: CLK120/PLLInst_0.CLKOS
      Blocked under: BLOCK PATH FROM CLKNET "clk_AD" TO CLKNET "clk120mhz" ;
      Blocked under: BLOCK PATH FROM CLKNET "clk_AD" TO CLKNET "clk120mhz" ;


Timing summary (Hold):
---------------

Timing errors: 0  Score: 0
Cumulative negative slack: 0

Constraints cover 38661 paths, 6 nets, and 4168 connections (99.21% coverage)



Timing summary (Setup and Hold):
---------------

Timing errors: 0 (setup), 0 (hold)
Score: 0 (setup), 0 (hold)
Cumulative negative slack: 0 (0+0)
--------------------------------------------------------------------------------

--------------------------------------------------------------------------------

