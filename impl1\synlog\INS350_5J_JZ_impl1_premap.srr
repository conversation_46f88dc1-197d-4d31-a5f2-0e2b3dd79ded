# Thu Sep 25 10:29:25 2025


Copyright (C) 1994-2021 Synopsys, Inc.
This Synopsys software and all associated documentation are proprietary to Synopsys, Inc.
and may only be used pursuant to the terms and conditions of a written license agreement
with Synopsys, Inc. All other use, reproduction, modification, or distribution of the
Synopsys software or the associated documentation is strictly prohibited.
Tool: Synplify Pro (R)
Build: R-2021.03L-SP1-1
Install: D:\Software\lscc\diamond\3.12\synpbase
OS: Windows 6.2

Hostname: TLH-022

Implementation : impl1
Synopsys Lattice Technology Pre-mapping, Version map202103lat, Build 107R, Built Dec 22 2021 00:40:26, @


Mapper Startup Complete (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 132MB peak: 132MB)


Done reading skeleton netlist (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 137MB peak: 144MB)

@A: MF827 |No constraint file specified.
@L: D:\Project\TLH50_03J_JZ_20250925\impl1\INS350_5J_JZ_impl1_scck.rpt 
See clock summary report "D:\Project\TLH50_03J_JZ_20250925\impl1\INS350_5J_JZ_impl1_scck.rpt"
@N: MF916 |Option synthesis_strategy=base is enabled. 
@N: MF248 |Running in 64-bit mode.
@N: MF666 |Clock conversion enabled. (Command "set_option -fix_gated_and_generated_clocks 1" in the project file.)

Design Input Complete (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 143MB peak: 144MB)


Mapper Initialization Complete (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 143MB peak: 144MB)


Start loading timing files (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 155MB peak: 155MB)


Finished loading timing files (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 155MB peak: 157MB)

@W: BN132 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\uart_tx.v":33:0:33:5|Removing sequential instance u_uart.U1.bps_start_r because it is equivalent to instance u_uart.U1.tx_en. To keep the instance, apply constraint syn_preserve=1 on the instance.
@N: MO111 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\uart_control.v":31:16:31:22|Tristate driver rx_data_1 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) on net rx_data_1 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) has its enable tied to GND.
@N: MO111 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\uart_control.v":31:16:31:22|Tristate driver rx_data_2 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) on net rx_data_2 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) has its enable tied to GND.
@N: MO111 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\uart_control.v":31:16:31:22|Tristate driver rx_data_3 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) on net rx_data_3 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) has its enable tied to GND.
@N: MO111 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\uart_control.v":31:16:31:22|Tristate driver rx_data_4 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) on net rx_data_4 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) has its enable tied to GND.
@N: MO111 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\uart_control.v":31:16:31:22|Tristate driver rx_data_5 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) on net rx_data_5 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) has its enable tied to GND.
@N: MO111 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\uart_control.v":31:16:31:22|Tristate driver rx_data_6 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) on net rx_data_6 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) has its enable tied to GND.
@N: MO111 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\uart_control.v":31:16:31:22|Tristate driver rx_data_7 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) on net rx_data_7 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) has its enable tied to GND.
@N: MO111 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\uart_control.v":31:16:31:22|Tristate driver rx_data_8 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) on net rx_data_8 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) has its enable tied to GND.
@N: BN362 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\ds18b20.v":157:0:157:5|Removing sequential instance rd_done (in view: work.DS18B20(verilog)) of type view:PrimLib.dffre(prim) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":182:13:182:18|Removing instance LUT4_1 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.ROM16X1A(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":186:13:186:18|Removing instance LUT4_0 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.ROM16X1A(PRIM) because it does not drive other instances.
@N: BN362 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":350:12:350:15|Removing sequential instance FF_1 (in view: work.Asys_fifo56X16(verilog)) of type view:LUCENT.FD1S3BX(PRIM) because it does not drive other instances.
@N: BN362 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":353:12:353:15|Removing sequential instance FF_0 (in view: work.Asys_fifo56X16(verilog)) of type view:LUCENT.FD1S3DX(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":576:10:576:24|Removing instance ae_set_cmp_ci_a (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":584:10:584:21|Removing instance ae_set_cmp_0 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":592:10:592:21|Removing instance ae_set_cmp_1 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":600:10:600:21|Removing instance ae_set_cmp_2 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":608:10:608:21|Removing instance ae_set_cmp_3 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":616:10:616:11|Removing instance a2 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":624:10:624:24|Removing instance ae_clr_cmp_ci_a (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":632:10:632:21|Removing instance ae_clr_cmp_0 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":640:10:640:21|Removing instance ae_clr_cmp_1 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":648:10:648:21|Removing instance ae_clr_cmp_2 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":656:10:656:21|Removing instance ae_clr_cmp_3 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":664:10:664:11|Removing instance a3 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":672:10:672:24|Removing instance af_set_cmp_ci_a (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":680:10:680:21|Removing instance af_set_cmp_0 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":688:10:688:21|Removing instance af_set_cmp_1 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":696:10:696:21|Removing instance af_set_cmp_2 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":704:10:704:21|Removing instance af_set_cmp_3 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":712:10:712:11|Removing instance a4 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":720:10:720:24|Removing instance af_clr_cmp_ci_a (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":728:10:728:21|Removing instance af_clr_cmp_0 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":736:10:736:21|Removing instance af_clr_cmp_1 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":744:10:744:21|Removing instance af_clr_cmp_2 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":752:10:752:21|Removing instance af_clr_cmp_3 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
@W: BN114 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":764:10:764:11|Removing instance a5 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.
Encoding state machine cur_state[5:0] (in view: work.DS18B20(verilog))
original code -> new code
   000001 -> 000001
   000010 -> 000010
   000100 -> 000100
   001000 -> 001000
   010000 -> 010000
   100000 -> 100000
Encoding state machine tx_state[10:0] (in view: work.Ctrl_Data(verilog))
original code -> new code
   0000 -> 00000000001
   0001 -> 00000000010
   0010 -> 00000000100
   0011 -> 00000001000
   0100 -> 00000010000
   0101 -> 00000100000
   0110 -> 00001000000
   0111 -> 00010000000
   1000 -> 00100000000
   1001 -> 01000000000
   1010 -> 10000000000
Encoding state machine trans_state[5:0] (in view: work.Rs422Output_Z2(verilog))
original code -> new code
   0000 -> 000001
   0001 -> 000010
   0010 -> 000100
   0011 -> 001000
   0100 -> 010000
   0101 -> 100000

Starting clock optimization phase (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 191MB peak: 191MB)

@N: MF578 |Incompatible asynchronous control logic preventing generated clock conversion.

Finished clock optimization phase (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 191MB peak: 192MB)


Starting clock optimization report phase (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 191MB peak: 192MB)


Finished clock optimization report phase (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 191MB peak: 192MB)

@N: FX1184 |Applying syn_allowed_resources blockrams=56 on top level netlist INS350_5J_JZ 

Finished netlist restructuring (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 191MB peak: 192MB)



Clock Summary
******************

          Start                                 Requested     Requested     Clock                                                Clock                   Clock
Level     Clock                                 Frequency     Period        Type                                                 Group                   Load 
--------------------------------------------------------------------------------------------------------------------------------------------------------------
0 <USER>       <GROUP>                                200.0 MHz     5.000         system                                               system_clkgroup         0    
                                                                                                                                                              
0 -       global_clock|CLKOP_inferred_clock     200.0 MHz     5.000         inferred                                             Inferred_clkgroup_0     855  
1 .         DS18B20|clk_us_derived_clock        200.0 MHz     5.000         derived (from global_clock|CLKOP_inferred_clock)     Inferred_clkgroup_0     64   
                                                                                                                                                              
0 -       global_clock|CLKOS_inferred_clock     200.0 MHz     5.000         inferred                                             Inferred_clkgroup_1     178  
==============================================================================================================================================================



Clock Load Summary
***********************

                                      Clock     Source                              Clock Pin                                  Non-clock Pin     Non-clock Pin
Clock                                 Load      Pin                                 Seq Example                                Seq Example       Comb Example 
--------------------------------------------------------------------------------------------------------------------------------------------------------------
System                                0         -                                   -                                          -                 -            
                                                                                                                                                              
global_clock|CLKOP_inferred_clock     855       CLK120.PLLInst_0.CLKOP(EHXPLLL)     signal_process.trans.clk_out.C             -                 -            
DS18B20|clk_us_derived_clock          64        wendu.clk_us.Q[0](dffre)            wendu.data_temp[15:0].C                    -                 -            
                                                                                                                                                              
global_clock|CLKOS_inferred_clock     178       CLK120.PLLInst_0.CLKOS(EHXPLLL)     signal_process.demodu.din_reg0[11:0].C     -                 -            
==============================================================================================================================================================

@W: MT529 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\ds18b20.v":69:0:69:5|Found inferred clock global_clock|CLKOP_inferred_clock which controls 855 sequential elements including wendu.cnt[7:0]. This clock has no specified timing constraint which may prevent conversion of gated or generated clocks and may adversely impact design performance. 
@W: MT529 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":198:13:198:25|Found inferred clock global_clock|CLKOS_inferred_clock which controls 178 sequential elements including signal_process.demodu.fifo.pdp_ram_0_0_1. This clock has no specified timing constraint which may prevent conversion of gated or generated clocks and may adversely impact design performance. 

ICG Latch Removal Summary:
Number of ICG latches removed: 0
Number of ICG latches not removed:	0
For details review file gcc_ICG_report.rpt


@S |Clock Optimization Summary



#### START OF PREMAP CLOCK OPTIMIZATION REPORT #####[

0 non-gated/non-generated clock tree(s) driving 0 clock pin(s) of sequential element(s)
3 gated/generated clock tree(s) driving 1097 clock pin(s) of sequential element(s)
0 instances converted, 1097 sequential instances remain driven by gated/generated clocks

============================================================================ Gated/Generated Clocks ============================================================================
Clock Tree ID     Driving Element            Drive Element Type     Unconverted Fanout     Sample Instance                            Explanation                               
--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
@KP:ckid0_1       CLK120.PLLInst_0.CLKOP     EHXPLLL                855                    wendu.cnt[7:0]                             Clock source is invalid for GCC           
@KP:ckid0_4       CLK120.PLLInst_0.CLKOS     EHXPLLL                178                    signal_process.demodu.AD_validcnt[7:0]     Clock source is invalid for GCC           
@KP:ckid0_6       wendu.clk_us.Q[0]          dffre                  64                     wendu.cur_state[5]                         Derived clock on input (not legal for GCC)
================================================================================================================================================================================


##### END OF CLOCK OPTIMIZATION REPORT ######

@N: FX1143 |Skipping assigning INTERNAL_VREF to iobanks, because the table of mapping from pin to iobank is not initialized.
Finished Pre Mapping Phase.

Starting constraint checker (Real Time elapsed 0h:00m:01s; CPU Time elapsed 0h:00m:01s; Memory used current: 193MB peak: 193MB)


Finished constraint checker preprocessing (Real Time elapsed 0h:00m:01s; CPU Time elapsed 0h:00m:01s; Memory used current: 193MB peak: 193MB)


Finished constraint checker (Real Time elapsed 0h:00m:01s; CPU Time elapsed 0h:00m:01s; Memory used current: 194MB peak: 194MB)

Pre-mapping successful!

At Mapper Exit (Real Time elapsed 0h:00m:01s; CPU Time elapsed 0h:00m:01s; Memory used current: 112MB peak: 195MB)

Process took 0h:00m:01s realtime, 0h:00m:01s cputime
# Thu Sep 25 10:29:26 2025

###########################################################]
