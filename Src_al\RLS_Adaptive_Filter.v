`timescale 1ns / 1ps
//////////////////////////////////////////////////////////////////////////////////
// Company: tianhailu
// Engineer: Andrew
// 
// Create Date:    15:33:33 09/11/2025
// Design Name:    IFOG501_2B
// Module Name:    RLS_Adaptive_Filter
// Project Name:   IFOG501_2B
// Target Devices: 
// Tool versions:  TD5.6.2-64bit
// Description:    RLS(Recursive Least Squares)自适应滤波器
//                 用于IFOG系统的噪声抑制和信号增强
//
// RLS算法原理：
// 1. 递归最小二乘法，自适应调整滤波器系数
// 2. 快速收敛，适合非平稳信号处理
// 3. 最小化误差平方和的加权累积
// 4. 适用于光纤陀螺的动态噪声环境
//
// 算法核心方程：
// e(n) = d(n) - w^T(n-1) * x(n)     // 误差计算
// k(n) = P(n-1)*x(n) / (λ + x^T(n)*P(n-1)*x(n))  // 增益向量
// w(n) = w(n-1) + k(n)*e(n)         // 权重更新
// P(n) = (P(n-1) - k(n)*x^T(n)*P(n-1)) / λ       // 协方差矩阵更新
//
// 参数说明：
// - FILTER_ORDER: 滤波器阶数，影响滤波效果和资源消耗
// - LAMBDA_SHIFT: 遗忘因子λ的移位表示，控制收敛速度
// - DATA_WIDTH: 数据位宽，与IFOG系统匹配
// - FRAC_WIDTH: 小数位宽，影响计算精度
//
// Revision 1.01 - File Created
// Additional Comments: 
// - 针对IFOG系统优化的RLS实现
// - 支持可配置的滤波器阶数
// - 采用定点运算，适合FPGA实现
// - 兼容现有信号处理时序
//////////////////////////////////////////////////////////////////////////////////

module RLS_Adaptive_Filter
#(
    parameter FILTER_ORDER = 8,        // 滤波器阶数：8阶FIR滤波器
    parameter DATA_WIDTH = 56,          // 数据位宽：与IFOG系统匹配
    parameter FRAC_WIDTH = 24,          // 小数位宽：定点运算精度
    parameter LAMBDA_SHIFT = 12,        // 遗忘因子λ=1-2^(-12)≈0.9998
    parameter COEFF_WIDTH = 32,         // 系数位宽：滤波器系数精度
    parameter GAIN_WIDTH = 40           // 增益计算位宽：中间计算精度
)
(
    // 时钟和复位
    input                           clk,            // 系统时钟
    input                           rst_n,          // 异步复位，低有效
    
    // 控制信号
    input                           enable,         // 滤波器使能
    input                           data_valid,     // 输入数据有效
    
    // 数据接口
    input  signed [DATA_WIDTH-1:0]  data_in,        // 输入信号x(n)
    input  signed [DATA_WIDTH-1:0]  desired_in,     // 期望信号d(n)
    output signed [DATA_WIDTH-1:0]  data_out,       // 滤波输出y(n)
    output signed [DATA_WIDTH-1:0]  error_out,      // 误差输出e(n)
    
    // 状态输出
    output                          output_valid,   // 输出数据有效
    output                          converged,      // 收敛指示
    output [15:0]                   mse_out         // 均方误差输出
);

//////////////////////////////////////////////////
// 内部信号定义
//////////////////////////////////////////////////

// 延迟线寄存器：存储输入信号的历史值（优化布局）
(* ram_style = "distributed" *) reg signed [DATA_WIDTH-1:0] delay_line [0:FILTER_ORDER-1];

// 滤波器系数：自适应调整的权重（使用BRAM）
(* ram_style = "block" *) reg signed [COEFF_WIDTH-1:0] coefficients [0:FILTER_ORDER-1];

// 协方差矩阵P的对角元素（简化实现，使用分布式RAM）
(* ram_style = "distributed" *) reg signed [GAIN_WIDTH-1:0] P_diag [0:FILTER_ORDER-1];

// 中间计算信号（添加流水线寄存器）
reg signed [DATA_WIDTH-1:0]     desired_reg;       // 期望信号寄存器
reg signed [DATA_WIDTH+16-1:0]  filter_output;     // 滤波器输出累加
reg signed [DATA_WIDTH+16-1:0]  filter_output_reg; // 滤波器输出寄存器
reg signed [DATA_WIDTH-1:0]     error_signal;      // 误差信号
reg signed [DATA_WIDTH-1:0]     error_signal_reg;  // 误差信号寄存器
(* ram_style = "distributed" *) reg signed [GAIN_WIDTH-1:0] gain_vector [0:FILTER_ORDER-1]; // 增益向量

// 控制信号
reg [2:0]                       state;             // 状态机
reg                             calc_valid;        // 计算有效
reg                             output_valid_reg;  // 输出有效寄存器
reg [7:0]                       update_counter;    // 更新计数器

// 收敛检测
reg [31:0]                      mse_accumulator;   // 均方误差累加器
reg [7:0]                       mse_counter;       // 均方误差计数器
reg                             converged_reg;     // 收敛标志

//////////////////////////////////////////////////
// 状态机定义
//////////////////////////////////////////////////
localparam IDLE         = 3'b000;
localparam CALC_OUTPUT  = 3'b001;
localparam CALC_ERROR   = 3'b010;
localparam CALC_GAIN    = 3'b011;
localparam UPDATE_COEFF = 3'b100;
localparam UPDATE_P     = 3'b101;
localparam DONE         = 3'b110;

//////////////////////////////////////////////////
// 初始化
//////////////////////////////////////////////////
integer i;
initial begin
    // 初始化滤波器系数为小随机值
    for (i = 0; i < FILTER_ORDER; i = i + 1) begin
        coefficients[i] = 32'h00001000;  // 小的初始值
        P_diag[i] = 40'h0100000000;      // 大的初始协方差
        delay_line[i] = 0;
        gain_vector[i] = 0;
    end
    
    state = IDLE;
    calc_valid = 1'b0;
    output_valid_reg = 1'b0;
    update_counter = 0;
    mse_accumulator = 0;
    mse_counter = 0;
    converged_reg = 1'b0;
end

//////////////////////////////////////////////////
// 主状态机：RLS算法实现
//////////////////////////////////////////////////
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        state <= IDLE;
        calc_valid <= 1'b0;
        output_valid_reg <= 1'b0;
        update_counter <= 0;
        
        // 重新初始化
        for (i = 0; i < FILTER_ORDER; i = i + 1) begin
            coefficients[i] <= 32'h00001000;
            P_diag[i] <= 40'h0100000000;
            delay_line[i] <= 0;
            gain_vector[i] <= 0;
        end
    end
    else if (enable) begin
        case (state)
            IDLE: begin
                output_valid_reg <= 1'b0;
                if (data_valid) begin
                    // 更新延迟线
                    for (i = FILTER_ORDER-1; i > 0; i = i - 1) begin
                        delay_line[i] <= delay_line[i-1];
                    end
                    delay_line[0] <= data_in;
                    desired_reg <= desired_in;
                    
                    state <= CALC_OUTPUT;
                    update_counter <= 0;
                end
            end
            
            CALC_OUTPUT: begin
                // 计算滤波器输出：y(n) = w^T(n-1) * x(n)
                // 分步骤计算，减少组合逻辑复杂度
                if (update_counter == 0) begin
                    filter_output <= 0;
                    update_counter <= 1;
                end
                else if (update_counter <= FILTER_ORDER) begin
                    // 使用流水线乘法，减少时序压力
                    filter_output <= filter_output +
                        ((delay_line[update_counter-1] >>> 4) * (coefficients[update_counter-1] >>> 4));
                    update_counter <= update_counter + 1;
                end
                else begin
                    filter_output_reg <= filter_output;  // 寄存器输出
                    state <= CALC_ERROR;
                    update_counter <= 0;
                end
            end
            
            CALC_ERROR: begin
                // 计算误差：e(n) = d(n) - y(n)
                error_signal <= desired_reg - filter_output_reg[DATA_WIDTH-1:0];
                error_signal_reg <= desired_reg - filter_output_reg[DATA_WIDTH-1:0];
                state <= CALC_GAIN;
                update_counter <= 0;
            end
            
            CALC_GAIN: begin
                // 计算增益向量：k(n) = P(n-1)*x(n) / (λ + x^T(n)*P(n-1)*x(n))
                // 简化实现：k_i(n) = P_ii(n-1)*x_i(n) / (λ + sum(x_i^2 * P_ii))
                if (update_counter < FILTER_ORDER) begin
                    // 简化的增益计算
                    gain_vector[update_counter] <= 
                        (P_diag[update_counter] * delay_line[update_counter]) >>> LAMBDA_SHIFT;
                    update_counter <= update_counter + 1;
                end
                else begin
                    state <= UPDATE_COEFF;
                    update_counter <= 0;
                end
            end
            
            UPDATE_COEFF: begin
                // 更新系数：w(n) = w(n-1) + k(n)*e(n)
                if (update_counter < FILTER_ORDER) begin
                    coefficients[update_counter] <= coefficients[update_counter] + 
                        ((gain_vector[update_counter] * error_signal) >>> FRAC_WIDTH);
                    update_counter <= update_counter + 1;
                end
                else begin
                    state <= UPDATE_P;
                    update_counter <= 0;
                end
            end
            
            UPDATE_P: begin
                // 更新协方差矩阵：P(n) = (P(n-1) - k(n)*x^T(n)*P(n-1)) / λ
                // 简化实现：P_ii(n) = P_ii(n-1) * (1 - k_i*x_i) / λ
                if (update_counter < FILTER_ORDER) begin
                    P_diag[update_counter] <= 
                        (P_diag[update_counter] - 
                         ((gain_vector[update_counter] * delay_line[update_counter]) >>> FRAC_WIDTH)) >>> 1;
                    update_counter <= update_counter + 1;
                end
                else begin
                    state <= DONE;
                end
            end
            
            DONE: begin
                output_valid_reg <= 1'b1;
                calc_valid <= 1'b1;
                
                // 更新均方误差
                mse_accumulator <= mse_accumulator + (error_signal * error_signal);
                mse_counter <= mse_counter + 1;
                
                // 收敛检测：连续64个样本的MSE小于阈值
                if (mse_counter == 8'hFF) begin
                    if (mse_accumulator < 32'h00010000) begin  // 阈值可调
                        converged_reg <= 1'b1;
                    end
                    mse_accumulator <= 0;
                    mse_counter <= 0;
                end
                
                state <= IDLE;
            end
            
            default: state <= IDLE;
        endcase
    end
    else begin
        // 滤波器禁用时保持空闲状态
        state <= IDLE;
        output_valid_reg <= 1'b0;
        calc_valid <= 1'b0;
    end
end

//////////////////////////////////////////////////
// 输出信号分配
//////////////////////////////////////////////////
assign data_out = filter_output[DATA_WIDTH-1:0];
assign error_out = error_signal;
assign output_valid = output_valid_reg;
assign converged = converged_reg;
assign mse_out = mse_accumulator[31:16];  // 输出MSE的高16位

endmodule
