# Thu Sep 25 10:29:26 2025


Copyright (C) 1994-2021 Synopsys, Inc.
This Synopsys software and all associated documentation are proprietary to Synopsys, Inc.
and may only be used pursuant to the terms and conditions of a written license agreement
with Synopsys, Inc. All other use, reproduction, modification, or distribution of the
Synopsys software or the associated documentation is strictly prohibited.
Tool: Synplify Pro (R)
Build: R-2021.03L-SP1-1
Install: D:\Software\lscc\diamond\3.12\synpbase
OS: Windows 6.2

Hostname: TLH-022

Implementation : impl1
Synopsys Lattice Technology Mapper, Version map202103lat, Build 107R, Built Dec 22 2021 00:40:26, @


Mapper Startup Complete (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 132MB peak: 132MB)

@N: MF916 |Option synthesis_strategy=base is enabled. 
@N: MF248 |Running in 64-bit mode.
@N: MF666 |Clock conversion enabled. (Command "set_option -fix_gated_and_generated_clocks 1" in the project file.)

Design Input Complete (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 134MB peak: 144MB)


Mapper Initialization Complete (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 134MB peak: 144MB)


Start loading timing files (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 148MB peak: 148MB)


Finished loading timing files (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 150MB peak: 151MB)



Starting Optimization and Mapping (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 185MB peak: 185MB)

@W: FA239 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\ds18b20.v":269:11:269:33|ROM RD_CMD_DATA_pmux (in view: work.DS18B20(verilog)) mapped in logic. To map to a technology ROM, apply attribute syn_romstyle on this instance.
@N: MO106 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\ds18b20.v":269:11:269:33|Found ROM RD_CMD_DATA_pmux (in view: work.DS18B20(verilog)) with 16 words by 1 bit.
@W: FA239 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\ds18b20.v":211:11:211:33|ROM WR_CMD_DATA_pmux (in view: work.DS18B20(verilog)) mapped in logic. To map to a technology ROM, apply attribute syn_romstyle on this instance.
@N: MO106 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\ds18b20.v":211:11:211:33|Found ROM WR_CMD_DATA_pmux (in view: work.DS18B20(verilog)) with 16 words by 1 bit.

Finished RTL optimizations (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 190MB peak: 190MB)

@N: MO231 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\ds18b20.v":157:0:157:5|Found counter in view:work.DS18B20(verilog) instance bit_cnt[3:0] 
@N: MO231 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\speed_select_tx.v":37:0:37:5|Found counter in view:work.speed_select_Tx_120000000s_115200s_1041s_520s(verilog) instance cnt[12:0] 
@N: MO231 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\rs422output.v":150:0:150:5|Found counter in view:work.SignalProcessing_Z3(verilog) instance rs422.count_pos[7:0] 
@N: BN362 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\integration.v":76:0:76:5|Removing sequential instance integ.dout[55] (in view: work.SignalProcessing_Z3(verilog)) of type view:PrimLib.dff(prim) because it does not drive other instances.
@W: BN132 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\rs422output.v":88:0:88:5|Removing instance signal_process.rs422.output_dy[0] because it is equivalent to instance signal_process.modu.mudu_dy[0]. To keep the instance, apply constraint syn_preserve=1 on the instance.
@W: BN132 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\rs422output.v":88:0:88:5|Removing instance signal_process.rs422.output_dy[1] because it is equivalent to instance signal_process.modu.mudu_dy[1]. To keep the instance, apply constraint syn_preserve=1 on the instance.

Starting factoring (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 194MB peak: 194MB)


Finished factoring (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 195MB peak: 195MB)


Available hyper_sources - for debug and ip models
	None Found


Finished generic timing optimizations - Pass 1 (Real Time elapsed 0h:00m:01s; CPU Time elapsed 0h:00m:01s; Memory used current: 200MB peak: 200MB)

@N: BN362 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\integration.v":66:0:66:5|Removing sequential instance signal_process.integ.DA_dout[55] (in view: work.INS350_5J_JZ(verilog)) because it does not drive other instances.
@N: BN362 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":181:0:181:5|Removing sequential instance signal_process.demodu.dout[55] (in view: work.INS350_5J_JZ(verilog)) because it does not drive other instances.
@N: BN362 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":163:0:163:5|Removing sequential instance signal_process.demodu.median_sum_n[55] (in view: work.INS350_5J_JZ(verilog)) because it does not drive other instances.
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":163:0:163:5|Boundary register signal_process.demodu.median_sum_n[55] (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@N: BN362 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":163:0:163:5|Removing sequential instance signal_process.demodu.INS_dout[55] (in view: work.INS350_5J_JZ(verilog)) because it does not drive other instances.
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":163:0:163:5|Boundary register signal_process.demodu.INS_dout[55] (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 

Starting Early Timing Optimization (Real Time elapsed 0h:00m:03s; CPU Time elapsed 0h:00m:03s; Memory used current: 205MB peak: 205MB)


Finished Early Timing Optimization (Real Time elapsed 0h:00m:03s; CPU Time elapsed 0h:00m:03s; Memory used current: 205MB peak: 206MB)


Finished generic timing optimizations - Pass 2 (Real Time elapsed 0h:00m:03s; CPU Time elapsed 0h:00m:03s; Memory used current: 205MB peak: 206MB)


Finished preparing to map (Real Time elapsed 0h:00m:03s; CPU Time elapsed 0h:00m:03s; Memory used current: 206MB peak: 206MB)


Finished technology mapping (Real Time elapsed 0h:00m:03s; CPU Time elapsed 0h:00m:03s; Memory used current: 210MB peak: 210MB)

Pass		 CPU time		Worst Slack		Luts / Registers
------------------------------------------------------------
   1		0h:00m:03s		     0.80ns		 285 /      1018

Finished technology timing optimizations and critical path resynthesis (Real Time elapsed 0h:00m:04s; CPU Time elapsed 0h:00m:04s; Memory used current: 211MB peak: 211MB)

@N: FX164 |The option to pack registers in the IOB has not been specified. Please set syn_useioff attribute.  
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_55_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_54_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_53_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_52_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_51_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_50_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_49_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_48_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_47_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_46_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_45_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_44_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_43_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_42_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_41_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_40_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_39_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_38_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_37_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_36_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_35_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_34_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_33_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_32_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_31_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_30_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_29_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_28_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_27_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_26_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_25_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_24_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_23_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_22_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_21_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_20_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_19_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_18_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_17_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_16_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_15_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_14_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_13_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_12_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_11_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_10_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_9_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_8_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_7_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_6_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_5_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_4_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_3_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_2_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_1_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A: BN291 :"d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v":110:0:110:5|Boundary register signal_process.demodu.sample_sum_0_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 

Finished restoring hierarchy (Real Time elapsed 0h:00m:04s; CPU Time elapsed 0h:00m:04s; Memory used current: 211MB peak: 211MB)


Start Writing Netlists (Real Time elapsed 0h:00m:04s; CPU Time elapsed 0h:00m:04s; Memory used current: 172MB peak: 212MB)

Writing Analyst data base D:\Project\TLH50_03J_JZ_20250925\impl1\synwork\INS350_5J_JZ_impl1_m.srm

Finished Writing Netlist Databases (Real Time elapsed 0h:00m:07s; CPU Time elapsed 0h:00m:06s; Memory used current: 214MB peak: 214MB)

Writing EDIF Netlist and constraint files
@N: FX1056 |Writing EDF file: D:\Project\TLH50_03J_JZ_20250925\impl1\INS350_5J_JZ_impl1.edi
@N: BW106 |Synplicity Constraint File capacitance units using default value of 1pF 

Finished Writing EDIF Netlist and constraint files (Real Time elapsed 0h:00m:07s; CPU Time elapsed 0h:00m:07s; Memory used current: 219MB peak: 219MB)


Finished Writing Netlists (Real Time elapsed 0h:00m:07s; CPU Time elapsed 0h:00m:07s; Memory used current: 219MB peak: 220MB)


Start final timing analysis (Real Time elapsed 0h:00m:07s; CPU Time elapsed 0h:00m:07s; Memory used current: 212MB peak: 220MB)

@W: MT246 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\global_clock\global_clock.v":59:12:59:20|Blackbox EHXPLLL is missing a user supplied timing model. This may have a negative effect on timing analysis and optimizations (Quality of Results)
@W: MT420 |Found inferred clock global_clock|CLKOP_inferred_clock with period 5.00ns. Please declare a user-defined clock on net CLK120.clk120mhz.
@W: MT420 |Found inferred clock global_clock|CLKOS_inferred_clock with period 5.00ns. Please declare a user-defined clock on net CLK120.clk_AD.
@N: MT615 |Found clock DS18B20|clk_us_derived_clock with period 5.00ns 


##### START OF TIMING REPORT #####[
# Timing report written on Thu Sep 25 10:29:34 2025
#


Top view:               INS350_5J_JZ
Requested Frequency:    200.0 MHz
Wire load mode:         top
Paths requested:        5
Constraint File(s):    
@N: MT320 |This timing report is an estimate of place and route data. For final timing results, use the FPGA vendor place and route report.

@N: MT322 |Clock constraints include only register-to-register paths associated with each individual clock.



Performance Summary
*******************


Worst slack in design: 0.058

                                      Requested     Estimated      Requested     Estimated               Clock                                                Clock              
Starting Clock                        Frequency     Frequency      Period        Period        Slack     Type                                                 Group              
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
DS18B20|clk_us_derived_clock          200.0 MHz     238.7 MHz      5.000         4.189         1.621     derived (from global_clock|CLKOP_inferred_clock)     Inferred_clkgroup_0
global_clock|CLKOP_inferred_clock     200.0 MHz     202.3 MHz      5.000         4.942         0.058     inferred                                             Inferred_clkgroup_0
global_clock|CLKOS_inferred_clock     200.0 MHz     233.5 MHz      5.000         4.284         0.717     inferred                                             Inferred_clkgroup_1
System                                200.0 MHz     2347.4 MHz     5.000         0.426         4.574     system                                               system_clkgroup    
=================================================================================================================================================================================





Clock Relationships
*******************

Clocks                                                                |    rise  to  rise   |    fall  to  fall   |    rise  to  fall   |    fall  to  rise 
------------------------------------------------------------------------------------------------------------------------------------------------------------
Starting                           Ending                             |  constraint  slack  |  constraint  slack  |  constraint  slack  |  constraint  slack
------------------------------------------------------------------------------------------------------------------------------------------------------------
System                             System                             |  5.000       4.574  |  No paths    -      |  No paths    -      |  No paths    -    
System                             global_clock|CLKOP_inferred_clock  |  5.000       2.861  |  No paths    -      |  No paths    -      |  No paths    -    
System                             global_clock|CLKOS_inferred_clock  |  5.000       2.317  |  No paths    -      |  No paths    -      |  No paths    -    
global_clock|CLKOP_inferred_clock  System                             |  5.000       0.856  |  No paths    -      |  No paths    -      |  No paths    -    
global_clock|CLKOP_inferred_clock  global_clock|CLKOP_inferred_clock  |  5.000       0.058  |  No paths    -      |  No paths    -      |  No paths    -    
global_clock|CLKOP_inferred_clock  global_clock|CLKOS_inferred_clock  |  Diff grp    -      |  No paths    -      |  No paths    -      |  No paths    -    
global_clock|CLKOS_inferred_clock  System                             |  5.000       3.619  |  No paths    -      |  No paths    -      |  No paths    -    
global_clock|CLKOS_inferred_clock  global_clock|CLKOP_inferred_clock  |  Diff grp    -      |  No paths    -      |  No paths    -      |  No paths    -    
global_clock|CLKOS_inferred_clock  global_clock|CLKOS_inferred_clock  |  5.000       0.717  |  No paths    -      |  No paths    -      |  No paths    -    
DS18B20|clk_us_derived_clock       global_clock|CLKOP_inferred_clock  |  5.000       4.093  |  No paths    -      |  No paths    -      |  No paths    -    
DS18B20|clk_us_derived_clock       DS18B20|clk_us_derived_clock       |  5.000       1.622  |  No paths    -      |  No paths    -      |  No paths    -    
============================================================================================================================================================
 Note: 'No paths' indicates there are no paths in the design for that pair of clock edges.
       'Diff grp' indicates that paths exist but the starting clock and ending clock are in different clock groups.



Interface Information 
*********************

No IO constraint found



====================================
Detailed Report for Clock: DS18B20|clk_us_derived_clock
====================================



Starting Points with Worst Slack
********************************

                     Starting                                                            Arrival          
Instance             Reference                        Type        Pin     Net            Time        Slack
                     Clock                                                                                
----------------------------------------------------------------------------------------------------------
wendu.cnt_us[10]     DS18B20|clk_us_derived_clock     FD1S3DX     Q       cnt_us[10]     0.955       1.621
wendu.cnt_us[13]     DS18B20|clk_us_derived_clock     FD1S3DX     Q       cnt_us[13]     0.955       1.621
wendu.cnt_us[16]     DS18B20|clk_us_derived_clock     FD1S3DX     Q       cnt_us[16]     0.955       1.621
wendu.cnt_us[19]     DS18B20|clk_us_derived_clock     FD1S3DX     Q       cnt_us[19]     0.955       1.621
wendu.cnt_us[14]     DS18B20|clk_us_derived_clock     FD1S3DX     Q       cnt_us[14]     0.955       2.228
wendu.cnt_us[15]     DS18B20|clk_us_derived_clock     FD1S3DX     Q       cnt_us[15]     0.955       2.228
wendu.cnt_us[17]     DS18B20|clk_us_derived_clock     FD1S3DX     Q       cnt_us[17]     0.955       2.228
wendu.cnt_us[0]      DS18B20|clk_us_derived_clock     FD1S3DX     Q       cnt_us[0]      0.955       2.797
wendu.cnt_us[11]     DS18B20|clk_us_derived_clock     FD1S3DX     Q       cnt_us[11]     0.955       2.797
wendu.cnt_us[12]     DS18B20|clk_us_derived_clock     FD1S3DX     Q       cnt_us[12]     0.955       2.797
==========================================================================================================


Ending Points with Worst Slack
******************************

                     Starting                                                               Required          
Instance             Reference                        Type        Pin     Net               Time         Slack
                     Clock                                                                                    
--------------------------------------------------------------------------------------------------------------
wendu.cnt_us[19]     DS18B20|clk_us_derived_clock     FD1S3DX     D       cnt_us_12[19]     9.946        1.621
wendu.cnt_us[17]     DS18B20|clk_us_derived_clock     FD1S3DX     D       cnt_us_12[17]     9.946        1.683
wendu.cnt_us[15]     DS18B20|clk_us_derived_clock     FD1S3DX     D       cnt_us_12[15]     9.946        1.744
wendu.cnt_us[16]     DS18B20|clk_us_derived_clock     FD1S3DX     D       cnt_us_12[16]     9.946        1.744
wendu.cnt_us[13]     DS18B20|clk_us_derived_clock     FD1S3DX     D       cnt_us_12[13]     9.946        1.804
wendu.cnt_us[14]     DS18B20|clk_us_derived_clock     FD1S3DX     D       cnt_us_12[14]     9.946        1.804
wendu.cnt_us[9]      DS18B20|clk_us_derived_clock     FD1S3DX     D       cnt_us_12[9]      9.946        1.927
wendu.cnt_us[10]     DS18B20|clk_us_derived_clock     FD1S3DX     D       cnt_us_12[10]     9.946        1.927
wendu.cnt_us[7]      DS18B20|clk_us_derived_clock     FD1S3DX     D       cnt_us_12[7]      9.946        1.988
wendu.cnt_us[8]      DS18B20|clk_us_derived_clock     FD1S3DX     D       cnt_us_12[8]      9.946        1.988
==============================================================================================================



Worst Path Information
***********************


Path information for path number 1: 
      Requested Period:                      10.000
    - Setup time:                            0.054
    + Clock delay at ending point:           0.000 (ideal)
    = Required time:                         9.946

    - Propagation time:                      8.325
    - Clock delay at starting point:         0.000 (ideal)
    = Slack (non-critical) :                 1.621

    Number of logic level(s):                19
    Starting point:                          wendu.cnt_us[10] / Q
    Ending point:                            wendu.cnt_us[19] / D
    The start point is clocked by            DS18B20|clk_us_derived_clock [rising] (rise=0.000 fall=2.500 period=5.000) on pin CK
    The end   point is clocked by            DS18B20|clk_us_derived_clock [rising] (rise=0.000 fall=2.500 period=5.000) on pin CK
    -Timing constraint applied as multi cycle path with factor 2 (from c:DS18B20|clk_us_derived_clock to c:DS18B20|clk_us_derived_clock)

Instance / Net                                           Pin      Pin               Arrival     No. of    
Name                                        Type         Name     Dir     Delay     Time        Fan Out(s)
----------------------------------------------------------------------------------------------------------
wendu.cnt_us[10]                            FD1S3DX      Q        Out     0.955     0.955 r     -         
cnt_us[10]                                  Net          -        -       -         -           3         
wendu.un1_cur_state_3_i_a2_3_o3_5_4         ORCALUT4     A        In      0.000     0.955 r     -         
wendu.un1_cur_state_3_i_a2_3_o3_5_4         ORCALUT4     Z        Out     0.606     1.561 r     -         
un1_cur_state_3_i_a2_3_o3_5_4               Net          -        -       -         -           1         
wendu.un1_cur_state_3_i_a2_3_o3_5           ORCALUT4     D        In      0.000     1.561 r     -         
wendu.un1_cur_state_3_i_a2_3_o3_5           ORCALUT4     Z        Out     0.708     2.269 r     -         
un1_cur_state_3_i_a2_3_o3_5                 Net          -        -       -         -           3         
wendu.un1_cur_state_3_i_a2_3_o3_0           ORCALUT4     A        In      0.000     2.269 r     -         
wendu.un1_cur_state_3_i_a2_3_o3_0           ORCALUT4     Z        Out     0.660     2.929 r     -         
un1_cur_state_3_i_a2_3_o3_0                 Net          -        -       -         -           2         
wendu.data_temp_1_sqmuxa_0_o3_0             ORCALUT4     B        In      0.000     2.929 r     -         
wendu.data_temp_1_sqmuxa_0_o3_0             ORCALUT4     Z        Out     0.708     3.637 r     -         
data_temp_1_sqmuxa_0_o3_0                   Net          -        -       -         -           3         
wendu.data_temp_1_sqmuxa_0_o3_0_RNIT8UQ     ORCALUT4     A        In      0.000     3.637 r     -         
wendu.data_temp_1_sqmuxa_0_o3_0_RNIT8UQ     ORCALUT4     Z        Out     0.708     4.345 r     -         
data_temp_1_sqmuxa_0_o3_0_RNIT8UQ           Net          -        -       -         -           3         
wendu.cur_state_RNIG5RS[5]                  ORCALUT4     B        In      0.000     4.345 r     -         
wendu.cur_state_RNIG5RS[5]                  ORCALUT4     Z        Out     0.837     5.182 f     -         
N_93_i                                      Net          -        -       -         -           20        
wendu.un1_cnt_us_18_cry_0_0_RNO             ORCALUT4     A        In      0.000     5.182 f     -         
wendu.un1_cnt_us_18_cry_0_0_RNO             ORCALUT4     Z        Out     0.606     5.788 r     -         
un1_cnt_us_0_sqmuxa_2_i                     Net          -        -       -         -           1         
wendu.un1_cnt_us_18_cry_0_0                 CCU2C        B0       In      0.000     5.788 r     -         
wendu.un1_cnt_us_18_cry_0_0                 CCU2C        COUT     Out     0.900     6.688 r     -         
un1_cnt_us_18_cry_0                         Net          -        -       -         -           1         
wendu.un1_cnt_us_18_cry_1_0                 CCU2C        CIN      In      0.000     6.688 r     -         
wendu.un1_cnt_us_18_cry_1_0                 CCU2C        COUT     Out     0.061     6.749 r     -         
un1_cnt_us_18_cry_2                         Net          -        -       -         -           1         
wendu.un1_cnt_us_18_cry_3_0                 CCU2C        CIN      In      0.000     6.749 r     -         
wendu.un1_cnt_us_18_cry_3_0                 CCU2C        COUT     Out     0.061     6.810 r     -         
un1_cnt_us_18_cry_4                         Net          -        -       -         -           1         
wendu.un1_cnt_us_18_cry_5_0                 CCU2C        CIN      In      0.000     6.810 r     -         
wendu.un1_cnt_us_18_cry_5_0                 CCU2C        COUT     Out     0.061     6.871 r     -         
un1_cnt_us_18_cry_6                         Net          -        -       -         -           1         
wendu.un1_cnt_us_18_cry_7_0                 CCU2C        CIN      In      0.000     6.871 r     -         
wendu.un1_cnt_us_18_cry_7_0                 CCU2C        COUT     Out     0.061     6.932 r     -         
un1_cnt_us_18_cry_8                         Net          -        -       -         -           1         
wendu.un1_cnt_us_18_cry_9_0                 CCU2C        CIN      In      0.000     6.932 r     -         
wendu.un1_cnt_us_18_cry_9_0                 CCU2C        COUT     Out     0.061     6.993 r     -         
un1_cnt_us_18_cry_10                        Net          -        -       -         -           1         
wendu.un1_cnt_us_18_cry_11_0                CCU2C        CIN      In      0.000     6.993 r     -         
wendu.un1_cnt_us_18_cry_11_0                CCU2C        COUT     Out     0.061     7.054 r     -         
un1_cnt_us_18_cry_12                        Net          -        -       -         -           1         
wendu.un1_cnt_us_18_cry_13_0                CCU2C        CIN      In      0.000     7.054 r     -         
wendu.un1_cnt_us_18_cry_13_0                CCU2C        COUT     Out     0.061     7.115 r     -         
un1_cnt_us_18_cry_14                        Net          -        -       -         -           1         
wendu.un1_cnt_us_18_cry_15_0                CCU2C        CIN      In      0.000     7.115 r     -         
wendu.un1_cnt_us_18_cry_15_0                CCU2C        COUT     Out     0.061     7.176 r     -         
un1_cnt_us_18_cry_16                        Net          -        -       -         -           1         
wendu.un1_cnt_us_18_cry_17_0                CCU2C        CIN      In      0.000     7.176 r     -         
wendu.un1_cnt_us_18_cry_17_0                CCU2C        COUT     Out     0.061     7.237 r     -         
un1_cnt_us_18_cry_18                        Net          -        -       -         -           1         
wendu.un1_cnt_us_18_s_19_0                  CCU2C        CIN      In      0.000     7.237 r     -         
wendu.un1_cnt_us_18_s_19_0                  CCU2C        S0       Out     0.698     7.934 r     -         
un1_cnt_us_18_s_19_0_S0                     Net          -        -       -         -           1         
wendu.cnt_us_12[19]                         ORCALUT4     C        In      0.000     7.934 r     -         
wendu.cnt_us_12[19]                         ORCALUT4     Z        Out     0.390     8.325 r     -         
cnt_us_12[19]                               Net          -        -       -         -           1         
wendu.cnt_us[19]                            FD1S3DX      D        In      0.000     8.325 r     -         
==========================================================================================================




====================================
Detailed Report for Clock: global_clock|CLKOP_inferred_clock
====================================



Starting Points with Worst Slack
********************************

                      Starting                                                              Arrival          
Instance              Reference                             Type        Pin     Net         Time        Slack
                      Clock                                                                                  
-------------------------------------------------------------------------------------------------------------
u_uart.U0.cnt[9]      global_clock|CLKOP_inferred_clock     FD1S3DX     Q       cnt[9]      0.955       0.058
u_uart.U0.cnt[10]     global_clock|CLKOP_inferred_clock     FD1S3DX     Q       cnt[10]     0.955       0.058
u_uart.U0.cnt[1]      global_clock|CLKOP_inferred_clock     FD1S3DX     Q       cnt[1]      0.907       0.657
u_uart.U0.cnt[2]      global_clock|CLKOP_inferred_clock     FD1S3DX     Q       cnt[2]      0.907       0.657
u_uart.U0.cnt[5]      global_clock|CLKOP_inferred_clock     FD1S3DX     Q       cnt[5]      0.907       0.657
u_uart.U0.cnt[6]      global_clock|CLKOP_inferred_clock     FD1S3DX     Q       cnt[6]      0.907       0.657
u_uart.U0.cnt[7]      global_clock|CLKOP_inferred_clock     FD1S3DX     Q       cnt[7]      0.907       0.657
u_uart.U0.cnt[8]      global_clock|CLKOP_inferred_clock     FD1S3DX     Q       cnt[8]      0.907       0.657
u_uart.U0.cnt[11]     global_clock|CLKOP_inferred_clock     FD1S3DX     Q       cnt[11]     0.907       0.657
u_uart.U0.cnt[12]     global_clock|CLKOP_inferred_clock     FD1S3DX     Q       cnt[12]     0.907       0.657
=============================================================================================================


Ending Points with Worst Slack
******************************

                      Starting                                                                Required          
Instance              Reference                             Type        Pin     Net           Time         Slack
                      Clock                                                                                     
----------------------------------------------------------------------------------------------------------------
u_uart.U0.cnt[11]     global_clock|CLKOP_inferred_clock     FD1S3DX     D       cnt_s[11]     4.946        0.058
u_uart.U0.cnt[12]     global_clock|CLKOP_inferred_clock     FD1S3DX     D       cnt_s[12]     4.946        0.058
u_uart.U0.cnt[9]      global_clock|CLKOP_inferred_clock     FD1S3DX     D       cnt_s[9]      4.946        0.118
u_uart.U0.cnt[10]     global_clock|CLKOP_inferred_clock     FD1S3DX     D       cnt_s[10]     4.946        0.118
u_uart.U0.cnt[7]      global_clock|CLKOP_inferred_clock     FD1S3DX     D       cnt_s[7]      4.946        0.179
u_uart.U0.cnt[8]      global_clock|CLKOP_inferred_clock     FD1S3DX     D       cnt_s[8]      4.946        0.179
u_uart.U0.cnt[5]      global_clock|CLKOP_inferred_clock     FD1S3DX     D       cnt_s[5]      4.946        0.240
u_uart.U0.cnt[6]      global_clock|CLKOP_inferred_clock     FD1S3DX     D       cnt_s[6]      4.946        0.240
u_uart.U0.cnt[3]      global_clock|CLKOP_inferred_clock     FD1S3DX     D       cnt_s[3]      4.946        0.301
u_uart.U0.cnt[4]      global_clock|CLKOP_inferred_clock     FD1S3DX     D       cnt_s[4]      4.946        0.301
================================================================================================================



Worst Path Information
***********************


Path information for path number 1: 
      Requested Period:                      5.000
    - Setup time:                            0.054
    + Clock delay at ending point:           0.000 (ideal)
    = Required time:                         4.946

    - Propagation time:                      4.888
    - Clock delay at starting point:         0.000 (ideal)
    = Slack (critical) :                     0.058

    Number of logic level(s):                10
    Starting point:                          u_uart.U0.cnt[9] / Q
    Ending point:                            u_uart.U0.cnt[12] / D
    The start point is clocked by            global_clock|CLKOP_inferred_clock [rising] (rise=0.000 fall=2.500 period=5.000) on pin CK
    The end   point is clocked by            global_clock|CLKOP_inferred_clock [rising] (rise=0.000 fall=2.500 period=5.000) on pin CK

Instance / Net                           Pin      Pin               Arrival     No. of    
Name                        Type         Name     Dir     Delay     Time        Fan Out(s)
------------------------------------------------------------------------------------------
u_uart.U0.cnt[9]            FD1S3DX      Q        Out     0.955     0.955 r     -         
cnt[9]                      Net          -        -       -         -           3         
u_uart.U0.cnt9_i_RNO_0      ORCALUT4     A        In      0.000     0.955 r     -         
u_uart.U0.cnt9_i_RNO_0      ORCALUT4     Z        Out     0.606     1.561 f     -         
m16_1                       Net          -        -       -         -           1         
u_uart.U0.cnt9_i_RNO        ORCALUT4     D        In      0.000     1.561 f     -         
u_uart.U0.cnt9_i_RNO        ORCALUT4     Z        Out     0.606     2.167 f     -         
m16_3                       Net          -        -       -         -           1         
u_uart.U0.cnt9_i            ORCALUT4     D        In      0.000     2.167 f     -         
u_uart.U0.cnt9_i            ORCALUT4     Z        Out     0.819     2.986 r     -         
cnt                         Net          -        -       -         -           14        
u_uart.U0.cnt_cry_0[0]      CCU2C        A1       In      0.000     2.986 r     -         
u_uart.U0.cnt_cry_0[0]      CCU2C        COUT     Out     0.900     3.886 r     -         
cnt_cry[0]                  Net          -        -       -         -           1         
u_uart.U0.cnt_cry_0[1]      CCU2C        CIN      In      0.000     3.886 r     -         
u_uart.U0.cnt_cry_0[1]      CCU2C        COUT     Out     0.061     3.947 r     -         
cnt_cry[2]                  Net          -        -       -         -           1         
u_uart.U0.cnt_cry_0[3]      CCU2C        CIN      In      0.000     3.947 r     -         
u_uart.U0.cnt_cry_0[3]      CCU2C        COUT     Out     0.061     4.008 r     -         
cnt_cry[4]                  Net          -        -       -         -           1         
u_uart.U0.cnt_cry_0[5]      CCU2C        CIN      In      0.000     4.008 r     -         
u_uart.U0.cnt_cry_0[5]      CCU2C        COUT     Out     0.061     4.069 r     -         
cnt_cry[6]                  Net          -        -       -         -           1         
u_uart.U0.cnt_cry_0[7]      CCU2C        CIN      In      0.000     4.069 r     -         
u_uart.U0.cnt_cry_0[7]      CCU2C        COUT     Out     0.061     4.130 r     -         
cnt_cry[8]                  Net          -        -       -         -           1         
u_uart.U0.cnt_cry_0[9]      CCU2C        CIN      In      0.000     4.130 r     -         
u_uart.U0.cnt_cry_0[9]      CCU2C        COUT     Out     0.061     4.191 r     -         
cnt_cry[10]                 Net          -        -       -         -           1         
u_uart.U0.cnt_cry_0[11]     CCU2C        CIN      In      0.000     4.191 r     -         
u_uart.U0.cnt_cry_0[11]     CCU2C        S1       Out     0.698     4.888 r     -         
cnt_s[12]                   Net          -        -       -         -           1         
u_uart.U0.cnt[12]           FD1S3DX      D        In      0.000     4.888 r     -         
==========================================================================================




====================================
Detailed Report for Clock: global_clock|CLKOS_inferred_clock
====================================



Starting Points with Worst Slack
********************************

                                         Starting                                                                     Arrival          
Instance                                 Reference                             Type        Pin     Net                Time        Slack
                                         Clock                                                                                         
---------------------------------------------------------------------------------------------------------------------------------------
signal_process.demodu.sample_sum[0]      global_clock|CLKOS_inferred_clock     FD1P3IX     Q       sample_sum[0]      0.985       0.717
signal_process.demodu.din_reg1[0]        global_clock|CLKOS_inferred_clock     FD1S3AX     Q       din_reg1[0]        0.907       0.794
signal_process.demodu.sample_sum[1]      global_clock|CLKOS_inferred_clock     FD1P3IX     Q       sample_sum[1]      0.955       0.807
signal_process.demodu.sample_sum[2]      global_clock|CLKOS_inferred_clock     FD1P3IX     Q       sample_sum[2]      0.955       0.807
signal_process.demodu.sample_sum[3]      global_clock|CLKOS_inferred_clock     FD1P3IX     Q       sample_sum[3]      0.955       0.869
signal_process.demodu.sample_sum[4]      global_clock|CLKOS_inferred_clock     FD1P3IX     Q       sample_sum[4]      0.955       0.869
signal_process.demodu.AD_validcnt[3]     global_clock|CLKOS_inferred_clock     FD1S3IX     Q       AD_validcnt[3]     0.955       0.896
signal_process.demodu.AD_validcnt[5]     global_clock|CLKOS_inferred_clock     FD1S3IX     Q       AD_validcnt[5]     0.955       0.896
signal_process.demodu.din_reg1[1]        global_clock|CLKOS_inferred_clock     FD1S3AX     Q       din_reg1[1]        0.853       0.909
signal_process.demodu.din_reg1[2]        global_clock|CLKOS_inferred_clock     FD1S3AX     Q       din_reg1[2]        0.853       0.909
=======================================================================================================================================


Ending Points with Worst Slack
******************************

                                         Starting                                                                                 Required          
Instance                                 Reference                             Type        Pin     Net                            Time         Slack
                                         Clock                                                                                                      
----------------------------------------------------------------------------------------------------------------------------------------------------
signal_process.demodu.sample_sum[55]     global_clock|CLKOS_inferred_clock     FD1P3IX     D       un3_sample_sum_s_55_0_S0       4.946        0.717
signal_process.demodu.sample_sum[53]     global_clock|CLKOS_inferred_clock     FD1P3IX     D       un3_sample_sum_cry_53_0_S0     4.946        0.777
signal_process.demodu.sample_sum[54]     global_clock|CLKOS_inferred_clock     FD1P3IX     D       un3_sample_sum_cry_53_0_S1     4.946        0.777
signal_process.demodu.sample_sum[51]     global_clock|CLKOS_inferred_clock     FD1P3IX     D       un3_sample_sum_cry_51_0_S0     4.946        0.839
signal_process.demodu.sample_sum[52]     global_clock|CLKOS_inferred_clock     FD1P3IX     D       un3_sample_sum_cry_51_0_S1     4.946        0.839
signal_process.demodu.AD_validcnt[7]     global_clock|CLKOS_inferred_clock     FD1S3IX     D       un1_AD_validcnt_1[7]           4.946        0.896
signal_process.demodu.sample_sum[49]     global_clock|CLKOS_inferred_clock     FD1P3IX     D       un3_sample_sum_cry_49_0_S0     4.946        0.899
signal_process.demodu.sample_sum[50]     global_clock|CLKOS_inferred_clock     FD1P3IX     D       un3_sample_sum_cry_49_0_S1     4.946        0.899
signal_process.demodu.AD_validcnt[5]     global_clock|CLKOS_inferred_clock     FD1S3IX     D       un1_AD_validcnt_1[5]           4.946        0.958
signal_process.demodu.AD_validcnt[6]     global_clock|CLKOS_inferred_clock     FD1S3IX     D       un1_AD_validcnt_1[6]           4.946        0.958
====================================================================================================================================================



Worst Path Information
***********************


Path information for path number 1: 
      Requested Period:                      5.000
    - Setup time:                            0.054
    + Clock delay at ending point:           0.000 (ideal)
    = Required time:                         4.946

    - Propagation time:                      4.229
    - Clock delay at starting point:         0.000 (ideal)
    = Slack (non-critical) :                 0.716

    Number of logic level(s):                29
    Starting point:                          signal_process.demodu.sample_sum[0] / Q
    Ending point:                            signal_process.demodu.sample_sum[55] / D
    The start point is clocked by            global_clock|CLKOS_inferred_clock [rising] (rise=0.000 fall=2.500 period=5.000) on pin CK
    The end   point is clocked by            global_clock|CLKOS_inferred_clock [rising] (rise=0.000 fall=2.500 period=5.000) on pin CK

Instance / Net                                                Pin      Pin               Arrival     No. of    
Name                                              Type        Name     Dir     Delay     Time        Fan Out(s)
---------------------------------------------------------------------------------------------------------------
signal_process.demodu.sample_sum[0]               FD1P3IX     Q        Out     0.985     0.985 r     -         
sample_sum[0]                                     Net         -        -       -         -           4         
signal_process.demodu.un3_sample_sum_cry_0_0      CCU2C       A1       In      0.000     0.985 r     -         
signal_process.demodu.un3_sample_sum_cry_0_0      CCU2C       COUT     Out     0.900     1.885 r     -         
un3_sample_sum_cry_0                              Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_1_0      CCU2C       CIN      In      0.000     1.885 r     -         
signal_process.demodu.un3_sample_sum_cry_1_0      CCU2C       COUT     Out     0.061     1.946 r     -         
un3_sample_sum_cry_2                              Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_3_0      CCU2C       CIN      In      0.000     1.946 r     -         
signal_process.demodu.un3_sample_sum_cry_3_0      CCU2C       COUT     Out     0.061     2.007 r     -         
un3_sample_sum_cry_4                              Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_5_0      CCU2C       CIN      In      0.000     2.007 r     -         
signal_process.demodu.un3_sample_sum_cry_5_0      CCU2C       COUT     Out     0.061     2.068 r     -         
un3_sample_sum_cry_6                              Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_7_0      CCU2C       CIN      In      0.000     2.068 r     -         
signal_process.demodu.un3_sample_sum_cry_7_0      CCU2C       COUT     Out     0.061     2.129 r     -         
un3_sample_sum_cry_8                              Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_9_0      CCU2C       CIN      In      0.000     2.129 r     -         
signal_process.demodu.un3_sample_sum_cry_9_0      CCU2C       COUT     Out     0.061     2.190 r     -         
un3_sample_sum_cry_10                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_11_0     CCU2C       CIN      In      0.000     2.190 r     -         
signal_process.demodu.un3_sample_sum_cry_11_0     CCU2C       COUT     Out     0.061     2.251 r     -         
un3_sample_sum_cry_12                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_13_0     CCU2C       CIN      In      0.000     2.251 r     -         
signal_process.demodu.un3_sample_sum_cry_13_0     CCU2C       COUT     Out     0.061     2.312 r     -         
un3_sample_sum_cry_14                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_15_0     CCU2C       CIN      In      0.000     2.312 r     -         
signal_process.demodu.un3_sample_sum_cry_15_0     CCU2C       COUT     Out     0.061     2.373 r     -         
un3_sample_sum_cry_16                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_17_0     CCU2C       CIN      In      0.000     2.373 r     -         
signal_process.demodu.un3_sample_sum_cry_17_0     CCU2C       COUT     Out     0.061     2.434 r     -         
un3_sample_sum_cry_18                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_19_0     CCU2C       CIN      In      0.000     2.434 r     -         
signal_process.demodu.un3_sample_sum_cry_19_0     CCU2C       COUT     Out     0.061     2.495 r     -         
un3_sample_sum_cry_20                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_21_0     CCU2C       CIN      In      0.000     2.495 r     -         
signal_process.demodu.un3_sample_sum_cry_21_0     CCU2C       COUT     Out     0.061     2.556 r     -         
un3_sample_sum_cry_22                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_23_0     CCU2C       CIN      In      0.000     2.556 r     -         
signal_process.demodu.un3_sample_sum_cry_23_0     CCU2C       COUT     Out     0.061     2.617 r     -         
un3_sample_sum_cry_24                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_25_0     CCU2C       CIN      In      0.000     2.617 r     -         
signal_process.demodu.un3_sample_sum_cry_25_0     CCU2C       COUT     Out     0.061     2.678 r     -         
un3_sample_sum_cry_26                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_27_0     CCU2C       CIN      In      0.000     2.678 r     -         
signal_process.demodu.un3_sample_sum_cry_27_0     CCU2C       COUT     Out     0.061     2.739 r     -         
un3_sample_sum_cry_28                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_29_0     CCU2C       CIN      In      0.000     2.739 r     -         
signal_process.demodu.un3_sample_sum_cry_29_0     CCU2C       COUT     Out     0.061     2.800 r     -         
un3_sample_sum_cry_30                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_31_0     CCU2C       CIN      In      0.000     2.800 r     -         
signal_process.demodu.un3_sample_sum_cry_31_0     CCU2C       COUT     Out     0.061     2.861 r     -         
un3_sample_sum_cry_32                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_33_0     CCU2C       CIN      In      0.000     2.861 r     -         
signal_process.demodu.un3_sample_sum_cry_33_0     CCU2C       COUT     Out     0.061     2.922 r     -         
un3_sample_sum_cry_34                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_35_0     CCU2C       CIN      In      0.000     2.922 r     -         
signal_process.demodu.un3_sample_sum_cry_35_0     CCU2C       COUT     Out     0.061     2.983 r     -         
un3_sample_sum_cry_36                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_37_0     CCU2C       CIN      In      0.000     2.983 r     -         
signal_process.demodu.un3_sample_sum_cry_37_0     CCU2C       COUT     Out     0.061     3.044 r     -         
un3_sample_sum_cry_38                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_39_0     CCU2C       CIN      In      0.000     3.044 r     -         
signal_process.demodu.un3_sample_sum_cry_39_0     CCU2C       COUT     Out     0.061     3.105 r     -         
un3_sample_sum_cry_40                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_41_0     CCU2C       CIN      In      0.000     3.105 r     -         
signal_process.demodu.un3_sample_sum_cry_41_0     CCU2C       COUT     Out     0.061     3.166 r     -         
un3_sample_sum_cry_42                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_43_0     CCU2C       CIN      In      0.000     3.166 r     -         
signal_process.demodu.un3_sample_sum_cry_43_0     CCU2C       COUT     Out     0.061     3.227 r     -         
un3_sample_sum_cry_44                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_45_0     CCU2C       CIN      In      0.000     3.227 r     -         
signal_process.demodu.un3_sample_sum_cry_45_0     CCU2C       COUT     Out     0.061     3.288 r     -         
un3_sample_sum_cry_46                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_47_0     CCU2C       CIN      In      0.000     3.288 r     -         
signal_process.demodu.un3_sample_sum_cry_47_0     CCU2C       COUT     Out     0.061     3.349 r     -         
un3_sample_sum_cry_48                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_49_0     CCU2C       CIN      In      0.000     3.349 r     -         
signal_process.demodu.un3_sample_sum_cry_49_0     CCU2C       COUT     Out     0.061     3.410 r     -         
un3_sample_sum_cry_50                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_51_0     CCU2C       CIN      In      0.000     3.410 r     -         
signal_process.demodu.un3_sample_sum_cry_51_0     CCU2C       COUT     Out     0.061     3.471 r     -         
un3_sample_sum_cry_52                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_53_0     CCU2C       CIN      In      0.000     3.471 r     -         
signal_process.demodu.un3_sample_sum_cry_53_0     CCU2C       COUT     Out     0.061     3.532 r     -         
un3_sample_sum_cry_54                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_s_55_0       CCU2C       CIN      In      0.000     3.532 r     -         
signal_process.demodu.un3_sample_sum_s_55_0       CCU2C       S0       Out     0.698     4.229 r     -         
un3_sample_sum_s_55_0_S0                          Net         -        -       -         -           1         
signal_process.demodu.sample_sum[55]              FD1P3IX     D        In      0.000     4.229 r     -         
===============================================================================================================




====================================
Detailed Report for Clock: System
====================================



Starting Points with Worst Slack
********************************

                                             Starting                                                   Arrival          
Instance                                     Reference     Type           Pin     Net                   Time        Slack
                                             Clock                                                                       
-------------------------------------------------------------------------------------------------------------------------
signal_process.demodu.fifo.AND2_t4           System        AND2           Z       wren_i                0.000       2.317
signal_process.demodu.fifo.AND2_t3           System        AND2           Z       rden_i                0.000       2.559
signal_process.rs422.un3_p_sum[36:71]        System        MULT18X18D     P0      un3_p_sum_add         0.000       2.861
signal_process.rs422.un3_p_sum[36:71]        System        MULT18X18D     P0      un3_p_sum_add         0.000       2.861
signal_process.rs422.un3_p_sum_add[0:53]     System        ALU54B         R36     un3_p_sum_add[36]     0.000       2.861
signal_process.rs422.un3_p_sum[36:71]        System        MULT18X18D     P1      un3_p_sum_2[1]        0.000       2.921
signal_process.rs422.un3_p_sum[36:71]        System        MULT18X18D     P1      un3_p_sum_2[1]        0.000       2.921
signal_process.rs422.un3_p_sum[36:71]        System        MULT18X18D     P2      un3_p_sum_2[2]        0.000       2.921
signal_process.rs422.un3_p_sum[36:71]        System        MULT18X18D     P2      un3_p_sum_2[2]        0.000       2.921
signal_process.rs422.un3_p_sum_add[0:53]     System        ALU54B         R37     un3_p_sum_add[37]     0.000       2.921
=========================================================================================================================


Ending Points with Worst Slack
******************************

                                     Starting                                            Required          
Instance                             Reference     Type        Pin     Net               Time         Slack
                                     Clock                                                                 
-----------------------------------------------------------------------------------------------------------
signal_process.demodu.fifo.FF_18     System        FD1S3DX     D       full_d            4.946        2.317
signal_process.demodu.fifo.FF_19     System        FD1S3BX     D       empty_d           4.946        2.559
signal_process.rs422.p_sum[54]       System        FD1P3DX     D       un3_p_sum[53]     4.946        2.861
signal_process.rs422.p_sum[55]       System        FD1P3DX     D       un3_p_sum[54]     4.946        2.861
signal_process.rs422.p_sum[52]       System        FD1P3DX     D       un3_p_sum[51]     4.946        2.921
signal_process.rs422.p_sum[53]       System        FD1P3DX     D       un3_p_sum[52]     4.946        2.921
signal_process.rs422.p_sum[50]       System        FD1P3DX     D       un3_p_sum[49]     4.946        2.982
signal_process.rs422.p_sum[51]       System        FD1P3DX     D       un3_p_sum[50]     4.946        2.982
signal_process.rs422.p_sum[48]       System        FD1P3DX     D       un3_p_sum[47]     4.946        3.043
signal_process.rs422.p_sum[49]       System        FD1P3DX     D       un3_p_sum[48]     4.946        3.043
===========================================================================================================



Worst Path Information
***********************


Path information for path number 1: 
      Requested Period:                      5.000
    - Setup time:                            0.054
    + Clock delay at ending point:           0.000 (ideal)
    = Required time:                         4.946

    - Propagation time:                      2.630
    - Clock delay at starting point:         0.000 (ideal)
    - Estimated clock delay at start point:  -0.000
    = Slack (non-critical) :                 2.316

    Number of logic level(s):                4
    Starting point:                          signal_process.demodu.fifo.AND2_t4 / Z
    Ending point:                            signal_process.demodu.fifo.FF_18 / D
    The start point is clocked by            System [rising]
    The end   point is clocked by            global_clock|CLKOS_inferred_clock [rising] (rise=0.000 fall=2.500 period=5.000) on pin CK

Instance / Net                                      Pin      Pin               Arrival     No. of    
Name                                   Type         Name     Dir     Delay     Time        Fan Out(s)
-----------------------------------------------------------------------------------------------------
signal_process.demodu.fifo.AND2_t4     AND2         Z        Out     0.000     0.000 r     -         
wren_i                                 Net          -        -       -         -           21        
signal_process.demodu.fifo.INV_5       INV          A        In      0.000     0.000 r     -         
signal_process.demodu.fifo.INV_5       INV          Z        Out     0.426     0.426 f     -         
wren_i_inv                             Net          -        -       -         -           1         
signal_process.demodu.fifo.g_cmp_3     CCU2C        B1       In      0.000     0.426 f     -         
signal_process.demodu.fifo.g_cmp_3     CCU2C        COUT     Out     0.900     1.326 r     -         
cmp_ge_d1_c                            Net          -        -       -         -           1         
signal_process.demodu.fifo.a1          CCU2C        CIN      In      0.000     1.326 r     -         
signal_process.demodu.fifo.a1          CCU2C        S0       Out     0.698     2.023 r     -         
cmp_ge_d1                              Net          -        -       -         -           1         
signal_process.demodu.fifo.LUT4_2      ROM16X1A     AD2      In      0.000     2.023 r     -         
signal_process.demodu.fifo.LUT4_2      ROM16X1A     DO0      Out     0.606     2.630 r     -         
full_d                                 Net          -        -       -         -           1         
signal_process.demodu.fifo.FF_18       FD1S3DX      D        In      0.000     2.630 r     -         
=====================================================================================================



##### END OF TIMING REPORT #####]

Timing exceptions that could not be applied

Finished final timing analysis (Real Time elapsed 0h:00m:08s; CPU Time elapsed 0h:00m:07s; Memory used current: 213MB peak: 220MB)


Finished timing report (Real Time elapsed 0h:00m:08s; CPU Time elapsed 0h:00m:07s; Memory used current: 213MB peak: 220MB)

---------------------------------------
Resource Usage Report
Part: lfe5u_25f-7

Register bits: 1044 of 24288 (4%)
PIC Latch:       0
I/O cells:       32
Block Rams : 2 of 56 (3%)

DSP primitives:       5 of 42 (11%)

Details:
ALU54B:         1
AND2:           3
BB:             1
CCU2C:          241
EHXPLLL:        1
FD1P3AX:        371
FD1P3BX:        3
FD1P3DX:        154
FD1P3IX:        111
FD1S3AX:        250
FD1S3BX:        3
FD1S3DX:        109
FD1S3IX:        9
FD1S3JX:        6
GSR:            1
IB:             13
IFS1P3DX:       13
INV:            9
MULT18X18D:     4
OB:             18
OFS1P3DX:       15
ORCALUT4:       281
PDPW16KD:       2
PFUMX:          3
PUR:            1
ROM16X1A:       2
VHI:            15
VLO:            15
XOR2:           1
Mapper successful!

At Mapper Exit (Real Time elapsed 0h:00m:08s; CPU Time elapsed 0h:00m:07s; Memory used current: 89MB peak: 220MB)

Process took 0h:00m:08s realtime, 0h:00m:08s cputime
# Thu Sep 25 10:29:35 2025

###########################################################]
