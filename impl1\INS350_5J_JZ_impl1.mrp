
         Lattice Mapping Report File for Design Module 'INS350_5J_<PERSON><PERSON>'


Design Information
------------------

Command line:   map -a ECP5U -p LFE5U-25F -t CABGA256 -s 7 -oc Commercial
     INS350_5J_JZ_impl1.ngd -o INS350_5J_JZ_impl1_map.ncd -pr
     INS350_5J_JZ_impl1.prf -mp INS350_5J_JZ_impl1.mrp -lpf
     D:/Project/TLH50_03J_JZ_20250925/impl1/INS350_5J_JZ_impl1_synplify.lpf -lpf
     D:/Project/TLH50_03J_JZ_20250925/INS350_5J_JZ.lpf -gui -msgset
     D:/Project/TLH50_03J_JZ_20250925/promote.xml 
Target Vendor:  LATTICE
Target Device:  LFE5U-25FCABGA256
Target Performance:   7
Mapper:  sa5p00,  version:  Diamond (64-bit) 3.12.1.454
Mapped on:  09/25/25  10:29:38

Design Summary
--------------

   Number of registers:   1044 out of 24879 (4%)
      PFU registers:         1016 out of 24288 (4%)
      PIO registers:           28 out of   591 (5%)
   Number of SLICEs:       697 out of 12144 (6%)
      SLICEs as Logic/ROM:    697 out of 12144 (6%)
      SLICEs as RAM:            0 out of  9108 (0%)
      SLICEs as Carry:        241 out of 12144 (2%)
   Number of LUT4s:        775 out of 24288 (3%)
      Number used as logic LUTs:        293
      Number used as distributed RAM:     0
      Number used as ripple logic:      482
      Number used as shift registers:     0
   Number of PIO sites used: 32 out of 197 (16%)
   Number of block RAMs:  2 out of 56 (4%)
   Number of GSRs:  1 out of 1 (100%)
   JTAG used :      No
   Readback used :  No
   Oscillator used :  No
   Startup used :   No
   DTR used :   No
   Number of Dynamic Bank Controller (BCINRD):  0 out of 4 (0%)
   Number of Dynamic Bank Controller (BCLVDSOB):  0 out of 4 (0%)
   Number of DCC:  0 out of 60 (0%)
   Number of DCS:  0 out of 2 (0%)
   Number of PLLs:  1 out of 2 (50%)
   Number of DDRDLLs:  0 out of 4 (0%)
   Number of CLKDIV:  0 out of 4 (0%)
   Number of ECLKSYNC:  0 out of 10 (0%)
   Number of ECLKBRIDGECS:  0 out of 2 (0%)
   Notes:-
      1. Total number of LUT4s = (Number of logic LUT4s) + 2*(Number of
     distributed RAMs) + 2*(Number of ripple logic)
      2. Number of logic LUT4s does not include count of distributed RAM and
     ripple logic.

        Number Of Mapped DSP Components:
   --------------------------------
   MULT18X18D          4
   MULT9X9D            0
   ALU54B              1

                                    Page 1




Design:  INS350_5J_JZ                                  Date:  09/25/25  10:29:38

Design Summary (cont)
---------------------
   ALU24B              0
   PRADD18A            0
   PRADD9A             0
   --------------------------------
   Number of Used DSP MULT Sites:  8 out of 56 (14 %)
   Number of Used DSP ALU Sites:  2 out of 28 (7 %)
   Number of Used DSP PRADD Sites:  0 out of 56 (0 %)
   Number of clocks:  4
     Net clk_in_c: 1 loads, 1 rising, 0 falling (Driver: PIO clk_in )
     Net clk120mhz: 446 loads, 446 rising, 0 falling (Driver: CLK120/PLLInst_0 )
     
     Net clk_AD: 101 loads, 101 rising, 0 falling (Driver: CLK120/PLLInst_0 )
     Net wendu.clk_us: 36 loads, 36 rising, 0 falling (Driver: wendu/clk_us )
   Number of Clock Enables:  29
     Net signal_process/trans/un1_clk_out6: 1 loads, 1 LSLICEs
     Net signal_process/rs422/trans_state[4]: 17 loads, 17 LSLICEs
     Net signal_process/rs422/N_80_i: 31 loads, 28 LSLICEs
     Net signal_process/rs422/p_sum13: 17 loads, 17 LSLICEs
     Net signal_process/polarity: 28 loads, 28 LSLICEs
     Net signal_process/modu/dout_mult13: 1 loads, 0 LSLICEs
     Net signal_process/modu/square10: 14 loads, 14 LSLICEs
     Net signal_process/modu/DA_dout3: 7 loads, 7 LSLICEs
     Net signal_process/integ/DA_dout5: 28 loads, 28 LSLICEs
     Net signal_process/demodu/N_410_i: 29 loads, 29 LSLICEs
     Net signal_process/demodu/fifo/wren_i: 6 loads, 4 LSLICEs
     Net signal_process/demodu/fifo/rden_i: 8 loads, 4 LSLICEs
     Net signal_process/demodu/fifo/fcnt_en: 4 loads, 4 LSLICEs
     Net signal_process/demodu/median_sum_n_1_sqmuxa: 28 loads, 28 LSLICEs
     Net signal_process/demodu/Latch_sum: 28 loads, 28 LSLICEs
     Net signal_process/demodu/N_417_i: 28 loads, 28 LSLICEs
     Net u_uart/U2/crc_data60: 4 loads, 4 LSLICEs
     Net u_uart/U2/N_52_i: 4 loads, 4 LSLICEs
     Net u_uart/tx_wr: 4 loads, 4 LSLICEs
     Net u_uart/Tx_done: 4 loads, 4 LSLICEs
     Net u_uart/U2/un3_temp_datady: 24 loads, 24 LSLICEs
     Net u_uart/U1/tx_en: 2 loads, 2 LSLICEs
     Net u_uart/U1/un1_tx_wr_1: 1 loads, 1 LSLICEs
     Net wendu/N_93_i: 2 loads, 2 LSLICEs
     Net wendu/N_167_i: 3 loads, 3 LSLICEs
     Net wendu/next_state_0_sqmuxa_1: 8 loads, 8 LSLICEs
     Net wendu/un1_cnt_us_0_sqmuxa_i: 1 loads, 1 LSLICEs
     Net wendu/N_117: 1 loads, 1 LSLICEs
     Net wendu.data_temp_1_sqmuxa: 9 loads, 8 LSLICEs
   Number of local set/reset loads for net CLK120/locked_out merged into GSR:
     270
   Number of LSRs:  10
     Net signal_process/rs422/trans_state[5]: 31 loads, 28 LSLICEs
     Net signal_process/demodu/AD_validcnt7: 33 loads, 33 LSLICEs
     Net signal_process/demodu/un1_AD_validcntlto7_i_o4: 2 loads, 2 LSLICEs
     Net signal_process/demodu/un1_AD_validcnt_1[0]: 1 loads, 1 LSLICEs
     Net signal_process/ctrl_signal/fb: 1 loads, 1 LSLICEs
     Net signal_process/ctrl_signal/fb_0: 1 loads, 1 LSLICEs
     Net signal_process/ctrl_signal/fb_1: 1 loads, 1 LSLICEs
     Net signal_process/ctrl_signal/fb_2: 1 loads, 1 LSLICEs
     Net signal_process/ctrl_signal/fb_3: 1 loads, 1 LSLICEs
     Net CLK120/locked_out: 2 loads, 0 LSLICEs

                                    Page 2




Design:  INS350_5J_JZ                                  Date:  09/25/25  10:29:38

Design Summary (cont)
---------------------
   Number of nets driven by tri-state buffers:  0
   Top 10 highest fanout non-clock nets:
     Net signal_process/polarity: 35 loads
     Net signal_process/demodu/AD_validcnt7: 34 loads
     Net signal_process/rs422/trans_state[5]: 33 loads
     Net signal_process/rs422/N_80_i: 31 loads
     Net signal_process/demodu/N_410_i: 29 loads
     Net signal_process/demodu/Latch_sum: 28 loads
     Net signal_process/demodu/median_sum_n_1_sqmuxa: 28 loads
     Net signal_process/demodu/N_417_i: 28 loads
     Net signal_process/integ/DA_dout5: 28 loads
     Net u_uart/U2/un3_temp_datady: 24 loads




   Number of warnings:  26
   Number of errors:    0
     

Design Errors/Warnings
----------------------

WARNING - map: signal_process/rs422/un3_p_sum_add[0:53] : removed the input
     signal of SIGNEDCIN pin because it's not legal.
WARNING - map: Using local reset signal 'CLK120/locked_out' to infer global GSR
     net.
WARNING - map: signal_process/rs422/un3_p_sum_add[0:53] : A/B data pipe not
     consistent with non-pipelined OPCODEOP0 control.
WARNING - map: signal_process/rs422/un3_p_sum_add[0:53] : A/B data pipe not
     consistent with non-pipelined OPCODEOP1 control.
WARNING - map: signal_process/rs422/un3_p_sum_add[0:53] : A/B data pipe not
     consistent with non-pipelined OPCODEIN control.
WARNING - map: signal_process/rs422/un3_p_sum_add[0:53] : R53 pin may contain
     garbage data if ternary 36 bit operation.
WARNING - map: signal_process/rs422/un3_p_sum_add[0:53] : R52 pin may contain
     garbage data if ternary 36 bit operation.
WARNING - map: signal_process/rs422/un3_p_sum_add[0:53] : R51 pin may contain
     garbage data if ternary 36 bit operation.
WARNING - map: signal_process/rs422/un3_p_sum_add[0:53] : R50 pin may contain
     garbage data if ternary 36 bit operation.
WARNING - map: signal_process/rs422/un3_p_sum_add[0:53] : R49 pin may contain
     garbage data if ternary 36 bit operation.
WARNING - map: signal_process/rs422/un3_p_sum_add[0:53] : R48 pin may contain
     garbage data if ternary 36 bit operation.
WARNING - map: signal_process/rs422/un3_p_sum_add[0:53] : R47 pin may contain
     garbage data if ternary 36 bit operation.
WARNING - map: signal_process/rs422/un3_p_sum_add[0:53] : R46 pin may contain
     garbage data if ternary 36 bit operation.
WARNING - map: signal_process/rs422/un3_p_sum_add[0:53] : R45 pin may contain
     garbage data if ternary 36 bit operation.
WARNING - map: signal_process/rs422/un3_p_sum_add[0:53] : R44 pin may contain
     garbage data if ternary 36 bit operation.
WARNING - map: signal_process/rs422/un3_p_sum_add[0:53] : R43 pin may contain
     garbage data if ternary 36 bit operation.
WARNING - map: signal_process/rs422/un3_p_sum_add[0:53] : R42 pin may contain

                                    Page 3




Design:  INS350_5J_JZ                                  Date:  09/25/25  10:29:38

Design Errors/Warnings (cont)
-----------------------------
     garbage data if ternary 36 bit operation.
WARNING - map: signal_process/rs422/un3_p_sum_add[0:53] : R41 pin may contain
     garbage data if ternary 36 bit operation.
WARNING - map: signal_process/rs422/un3_p_sum_add[0:53] : R40 pin may contain
     garbage data if ternary 36 bit operation.
WARNING - map: signal_process/rs422/un3_p_sum_add[0:53] : R39 pin may contain
     garbage data if ternary 36 bit operation.
WARNING - map: signal_process/rs422/un3_p_sum_add[0:53] : R38 pin may contain
     garbage data if ternary 36 bit operation.
WARNING - map: signal_process/rs422/un3_p_sum_add[0:53] : R37 pin may contain
     garbage data if ternary 36 bit operation.
WARNING - map: signal_process/rs422/un3_p_sum_add[0:53] : R36 pin may contain
     garbage data if ternary 36 bit operation.
WARNING - map: IO buffer missing for top level port RXD...logic will be
     discarded.
WARNING - map: IO buffer missing for top level port RxTransmit...logic will be
     discarded.
WARNING - map: Semantic error in "LOCATE COMP "wendu/SLICE_241" SITE "CLKOS3"
     ;": Unable to find site CLKOS3 in the device. This preference has been
     disabled.

IO (PIO) Attributes
-------------------

+---------------------+-----------+-----------+------------+
| IO Name             | Direction | Levelmode | IO         |
|                     |           |  IO_TYPE  | Register   |
+---------------------+-----------+-----------+------------+
| dq                  | BIDIR     | LVCMOS33  | IN         |
+---------------------+-----------+-----------+------------+
| TXD                 | OUTPUT    | LVCMOS33  | OUT        |
+---------------------+-----------+-----------+------------+
| clk_in              | INPUT     | LVCMOS33  |            |
+---------------------+-----------+-----------+------------+
| clk_DA              | OUTPUT    | LVCMOS33  |            |
+---------------------+-----------+-----------+------------+
| clk_AD_t            | OUTPUT    | LVCMOS33  |            |
+---------------------+-----------+-----------+------------+
| DA_DATA[13]         | OUTPUT    | LVCMOS33  | OUT        |
+---------------------+-----------+-----------+------------+
| DA_DATA[12]         | OUTPUT    | LVCMOS33  | OUT        |
+---------------------+-----------+-----------+------------+
| DA_DATA[11]         | OUTPUT    | LVCMOS33  | OUT        |
+---------------------+-----------+-----------+------------+
| DA_DATA[10]         | OUTPUT    | LVCMOS33  | OUT        |
+---------------------+-----------+-----------+------------+
| DA_DATA[9]          | OUTPUT    | LVCMOS33  | OUT        |
+---------------------+-----------+-----------+------------+
| DA_DATA[8]          | OUTPUT    | LVCMOS33  | OUT        |
+---------------------+-----------+-----------+------------+
| DA_DATA[7]          | OUTPUT    | LVCMOS33  | OUT        |
+---------------------+-----------+-----------+------------+
| DA_DATA[6]          | OUTPUT    | LVCMOS33  | OUT        |
+---------------------+-----------+-----------+------------+
| DA_DATA[5]          | OUTPUT    | LVCMOS33  | OUT        |
+---------------------+-----------+-----------+------------+

                                    Page 4




Design:  INS350_5J_JZ                                  Date:  09/25/25  10:29:38

IO (PIO) Attributes (cont)
--------------------------
| DA_DATA[4]          | OUTPUT    | LVCMOS33  | OUT        |
+---------------------+-----------+-----------+------------+
| DA_DATA[3]          | OUTPUT    | LVCMOS33  | OUT        |
+---------------------+-----------+-----------+------------+
| DA_DATA[2]          | OUTPUT    | LVCMOS33  | OUT        |
+---------------------+-----------+-----------+------------+
| DA_DATA[1]          | OUTPUT    | LVCMOS33  | OUT        |
+---------------------+-----------+-----------+------------+
| DA_DATA[0]          | OUTPUT    | LVCMOS33  | OUT        |
+---------------------+-----------+-----------+------------+
| AD_DATA[11]         | INPUT     | LVCMOS33  | IN         |
+---------------------+-----------+-----------+------------+
| AD_DATA[10]         | INPUT     | LVCMOS33  | IN         |
+---------------------+-----------+-----------+------------+
| AD_DATA[9]          | INPUT     | LVCMOS33  | IN         |
+---------------------+-----------+-----------+------------+
| AD_DATA[8]          | INPUT     | LVCMOS33  | IN         |
+---------------------+-----------+-----------+------------+
| AD_DATA[7]          | INPUT     | LVCMOS33  | IN         |
+---------------------+-----------+-----------+------------+
| AD_DATA[6]          | INPUT     | LVCMOS33  | IN         |
+---------------------+-----------+-----------+------------+
| AD_DATA[5]          | INPUT     | LVCMOS33  | IN         |
+---------------------+-----------+-----------+------------+
| AD_DATA[4]          | INPUT     | LVCMOS33  | IN         |
+---------------------+-----------+-----------+------------+
| AD_DATA[3]          | INPUT     | LVCMOS33  | IN         |
+---------------------+-----------+-----------+------------+
| AD_DATA[2]          | INPUT     | LVCMOS33  | IN         |
+---------------------+-----------+-----------+------------+
| AD_DATA[1]          | INPUT     | LVCMOS33  | IN         |
+---------------------+-----------+-----------+------------+
| AD_DATA[0]          | INPUT     | LVCMOS33  | IN         |
+---------------------+-----------+-----------+------------+
| TxTransmit          | OUTPUT    | LVCMOS33  |            |
+---------------------+-----------+-----------+------------+

Removed logic
-------------

Block VCC undriven or does not drive anything - clipped.
Block CLK120/VCC undriven or does not drive anything - clipped.
Block wendu/GND undriven or does not drive anything - clipped.
Block u_uart/GND undriven or does not drive anything - clipped.
Block u_uart/VCC undriven or does not drive anything - clipped.
Block u_uart/U0/GND undriven or does not drive anything - clipped.
Block u_uart/U1/GND undriven or does not drive anything - clipped.
Block u_uart/U1/VCC undriven or does not drive anything - clipped.
Block u_uart/U2/GND undriven or does not drive anything - clipped.
Block u_uart/U2/VCC undriven or does not drive anything - clipped.
Block signal_process/GND undriven or does not drive anything - clipped.
Block signal_process/VCC undriven or does not drive anything - clipped.
Block signal_process/ctrl_signal/GND undriven or does not drive anything -
     clipped.
Block signal_process/demodu/GND undriven or does not drive anything - clipped.
Block signal_process/integ/GND undriven or does not drive anything - clipped.

                                    Page 5




Design:  INS350_5J_JZ                                  Date:  09/25/25  10:29:38

Removed logic (cont)
--------------------
Block signal_process/trans/GND undriven or does not drive anything - clipped.
Signal locked_out_i was merged into signal CLK120/locked_out
Signal signal_process/demodu/fifo/rden_i_inv was merged into signal
     signal_process/demodu/fifo/rden_i
Signal signal_process/demodu/fifo/invout_1 was merged into signal
     signal_process/demodu/fifo/empty_flag
Signal signal_process/demodu/fifo/invout_2 was merged into signal
     signal_process/demodu/fifo/Full
Signal GND undriven or does not drive anything - clipped.
Signal wendu/VCC undriven or does not drive anything - clipped.
Signal u_uart/U0/VCC undriven or does not drive anything - clipped.
Signal signal_process/ctrl_signal/VCC undriven or does not drive anything -
     clipped.
Signal signal_process/demodu/VCC undriven or does not drive anything - clipped.
Signal signal_process/demodu/fifo/VCC undriven or does not drive anything -
     clipped.
Signal signal_process/integ/VCC undriven or does not drive anything - clipped.
Signal signal_process/modu/GND undriven or does not drive anything - clipped.
Signal signal_process/modu/VCC undriven or does not drive anything - clipped.
Signal signal_process/rs422/GND undriven or does not drive anything - clipped.
Signal signal_process/rs422/VCC undriven or does not drive anything - clipped.
Signal signal_process/trans/VCC undriven or does not drive anything - clipped.
Signal VCC undriven or does not drive anything - clipped.
Signal CLK120/CLKINTFB undriven or does not drive anything - clipped.
Signal CLK120/REFCLK undriven or does not drive anything - clipped.
Signal CLK120/INTLOCK undriven or does not drive anything - clipped.
Signal CLK120/CLKOS3 undriven or does not drive anything - clipped.
Signal wendu/un1_cnt_us_18_cry_0_0_S0 undriven or does not drive anything -
     clipped.
Signal wendu/N_8 undriven or does not drive anything - clipped.
Signal wendu/un4_cnt_s_7_0_S1 undriven or does not drive anything - clipped.
Signal wendu/un4_cnt_s_7_0_COUT undriven or does not drive anything - clipped.
Signal wendu/un4_cnt_cry_0_0_S1 undriven or does not drive anything - clipped.
Signal wendu/un4_cnt_cry_0_0_S0 undriven or does not drive anything - clipped.
Signal wendu/N_9 undriven or does not drive anything - clipped.
Signal wendu/un1_cnt_us_18_s_19_0_S1 undriven or does not drive anything -
     clipped.
Signal wendu/un1_cnt_us_18_s_19_0_COUT undriven or does not drive anything -
     clipped.
Signal u_uart/U0/cnt_cry_0_COUT[11] undriven or does not drive anything -
     clipped.
Signal u_uart/U0/cnt_cry_0_S0[0] undriven or does not drive anything - clipped.
Signal u_uart/U0/N_1 undriven or does not drive anything - clipped.
Signal signal_process/ctrl_signal/un6_count_s_15_0_S1 undriven or does not drive
     anything - clipped.
Signal signal_process/ctrl_signal/un6_count_s_15_0_COUT undriven or does not
     drive anything - clipped.
Signal signal_process/ctrl_signal/un6_count_cry_0_0_S1_0 undriven or does not
     drive anything - clipped.
Signal signal_process/ctrl_signal/un6_count_cry_0_0_S0_0 undriven or does not
     drive anything - clipped.
Signal signal_process/ctrl_signal/N_1 undriven or does not drive anything -
     clipped.
Signal signal_process/demodu/un1_AD_validcnt_1_s_7_0_S1 undriven or does not
     drive anything - clipped.
Signal signal_process/demodu/un1_AD_validcnt_1_s_7_0_COUT undriven or does not

                                    Page 6




Design:  INS350_5J_JZ                                  Date:  09/25/25  10:29:38

Removed logic (cont)
--------------------
     drive anything - clipped.
Signal signal_process/demodu/un1_AD_validcnt_1_cry_0_0_S0 undriven or does not
     drive anything - clipped.
Signal signal_process/demodu/N_1 undriven or does not drive anything - clipped.
Signal signal_process/demodu/un3_sample_sum_s_55_0_S1 undriven or does not drive
     anything - clipped.
Signal signal_process/demodu/un3_sample_sum_s_55_0_COUT undriven or does not
     drive anything - clipped.
Signal signal_process/demodu/un3_sample_sum_cry_0_0_S1 undriven or does not
     drive anything - clipped.
Signal signal_process/demodu/un3_sample_sum_cry_0_0_S0 undriven or does not
     drive anything - clipped.
Signal signal_process/demodu/N_2 undriven or does not drive anything - clipped.
Signal signal_process/demodu/INS_dout_1_cry_53_0_COUT undriven or does not drive
     anything - clipped.
Signal signal_process/demodu/INS_dout_1_cry_0_0_S1 undriven or does not drive
     anything - clipped.
Signal signal_process/demodu/INS_dout_1_cry_0_0_S0 undriven or does not drive
     anything - clipped.
Signal signal_process/demodu/N_3 undriven or does not drive anything - clipped.
Signal signal_process/demodu/fifo/co3_2 undriven or does not drive anything -
     clipped.
Signal signal_process/demodu/fifo/r_ctr_cia_S1 undriven or does not drive
     anything - clipped.
Signal signal_process/demodu/fifo/r_ctr_cia_S0 undriven or does not drive
     anything - clipped.
Signal signal_process/demodu/fifo/CIN undriven or does not drive anything -
     clipped.
Signal signal_process/demodu/fifo/co3_1 undriven or does not drive anything -
     clipped.
Signal signal_process/demodu/fifo/w_ctr_cia_S1 undriven or does not drive
     anything - clipped.
Signal signal_process/demodu/fifo/w_ctr_cia_S0 undriven or does not drive
     anything - clipped.
Signal signal_process/demodu/fifo/CIN_0 undriven or does not drive anything -
     clipped.
Signal signal_process/demodu/fifo/a1_S1 undriven or does not drive anything -
     clipped.
Signal signal_process/demodu/fifo/a1_COUT undriven or does not drive anything -
     clipped.
Signal signal_process/demodu/fifo/g_cmp_3_S1 undriven or does not drive anything
     - clipped.
Signal signal_process/demodu/fifo/g_cmp_3_S0 undriven or does not drive anything
     - clipped.
Signal signal_process/demodu/fifo/g_cmp_2_S1 undriven or does not drive anything
     - clipped.
Signal signal_process/demodu/fifo/g_cmp_2_S0 undriven or does not drive anything
     - clipped.
Signal signal_process/demodu/fifo/g_cmp_1_S1 undriven or does not drive anything
     - clipped.
Signal signal_process/demodu/fifo/g_cmp_1_S0 undriven or does not drive anything
     - clipped.
Signal signal_process/demodu/fifo/g_cmp_0_S1 undriven or does not drive anything
     - clipped.
Signal signal_process/demodu/fifo/g_cmp_0_S0 undriven or does not drive anything
     - clipped.

                                    Page 7




Design:  INS350_5J_JZ                                  Date:  09/25/25  10:29:38

Removed logic (cont)
--------------------
Signal signal_process/demodu/fifo/g_cmp_ci_a_S1 undriven or does not drive
     anything - clipped.
Signal signal_process/demodu/fifo/g_cmp_ci_a_S0 undriven or does not drive
     anything - clipped.
Signal signal_process/demodu/fifo/CIN_1 undriven or does not drive anything -
     clipped.
Signal signal_process/demodu/fifo/a0_S1 undriven or does not drive anything -
     clipped.
Signal signal_process/demodu/fifo/a0_COUT undriven or does not drive anything -
     clipped.
Signal signal_process/demodu/fifo/e_cmp_3_S1 undriven or does not drive anything
     - clipped.
Signal signal_process/demodu/fifo/e_cmp_3_S0 undriven or does not drive anything
     - clipped.
Signal signal_process/demodu/fifo/e_cmp_2_S1 undriven or does not drive anything
     - clipped.
Signal signal_process/demodu/fifo/e_cmp_2_S0 undriven or does not drive anything
     - clipped.
Signal signal_process/demodu/fifo/e_cmp_1_S1 undriven or does not drive anything
     - clipped.
Signal signal_process/demodu/fifo/e_cmp_1_S0 undriven or does not drive anything
     - clipped.
Signal signal_process/demodu/fifo/e_cmp_0_S1 undriven or does not drive anything
     - clipped.
Signal signal_process/demodu/fifo/e_cmp_0_S0 undriven or does not drive anything
     - clipped.
Signal signal_process/demodu/fifo/e_cmp_ci_a_S1 undriven or does not drive
     anything - clipped.
Signal signal_process/demodu/fifo/e_cmp_ci_a_S0 undriven or does not drive
     anything - clipped.
Signal signal_process/demodu/fifo/CIN_2 undriven or does not drive anything -
     clipped.
Signal signal_process/demodu/fifo/co3 undriven or does not drive anything -
     clipped.
Signal signal_process/demodu/fifo/bdcnt_bctr_cia_S1 undriven or does not drive
     anything - clipped.
Signal signal_process/demodu/fifo/bdcnt_bctr_cia_S0 undriven or does not drive
     anything - clipped.
Signal signal_process/demodu/fifo/CIN_3 undriven or does not drive anything -
     clipped.
Signal signal_process/demodu/fifo/pdp_ram_0_1_0_DO17 undriven or does not drive
     anything - clipped.
Signal signal_process/demodu/fifo/pdp_ram_0_1_0_DO16 undriven or does not drive
     anything - clipped.
Signal signal_process/demodu/fifo/pdp_ram_0_1_0_DO15 undriven or does not drive
     anything - clipped.
Signal signal_process/demodu/fifo/pdp_ram_0_1_0_DO14 undriven or does not drive
     anything - clipped.
Signal signal_process/demodu/fifo/pdp_ram_0_1_0_DO13 undriven or does not drive
     anything - clipped.
Signal signal_process/demodu/fifo/pdp_ram_0_1_0_DO12 undriven or does not drive
     anything - clipped.
Signal signal_process/demodu/fifo/pdp_ram_0_1_0_DO11 undriven or does not drive
     anything - clipped.
Signal signal_process/demodu/fifo/pdp_ram_0_1_0_DO10 undriven or does not drive
     anything - clipped.

                                    Page 8




Design:  INS350_5J_JZ                                  Date:  09/25/25  10:29:38

Removed logic (cont)
--------------------
Signal signal_process/demodu/fifo/pdp_ram_0_1_0_DO9 undriven or does not drive
     anything - clipped.
Signal signal_process/demodu/fifo/pdp_ram_0_1_0_DO8 undriven or does not drive
     anything - clipped.
Signal signal_process/demodu/fifo/pdp_ram_0_1_0_DO7 undriven or does not drive
     anything - clipped.
Signal signal_process/demodu/fifo/pdp_ram_0_1_0_DO6 undriven or does not drive
     anything - clipped.
Signal signal_process/demodu/fifo/pdp_ram_0_1_0_DO5 undriven or does not drive
     anything - clipped.
Signal signal_process/demodu/fifo/pdp_ram_0_1_0_DO4 undriven or does not drive
     anything - clipped.
Signal signal_process/demodu/fifo/pdp_ram_0_1_0_DO3 undriven or does not drive
     anything - clipped.
Signal signal_process/demodu/fifo/pdp_ram_0_1_0_DO2 undriven or does not drive
     anything - clipped.
Signal signal_process/demodu/fifo/sample_sum_DY[55] undriven or does not drive
     anything - clipped.
Signal signal_process/integ/DA_dout_1_cry_53_0_COUT undriven or does not drive
     anything - clipped.
Signal signal_process/integ/DA_dout_1_cry_0_0_S1 undriven or does not drive
     anything - clipped.
Signal signal_process/integ/DA_dout_1_cry_0_0_S0 undriven or does not drive
     anything - clipped.
Signal signal_process/integ/N_1 undriven or does not drive anything - clipped.
Signal signal_process/modu/stair_1_s_23_0_S1 undriven or does not drive anything
     - clipped.
Signal signal_process/modu/stair_1_s_23_0_COUT undriven or does not drive
     anything - clipped.
Signal signal_process/modu/stair_1_cry_0_0_S1 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/stair_1_cry_0_0_S0 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/N_1 undriven or does not drive anything - clipped.
Signal signal_process/modu/dout_reg_1_s_13_0_S1 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/dout_reg_1_s_13_0_COUT undriven or does not drive
     anything - clipped.
Signal signal_process/modu/dout_reg_1_cry_0_0_S1 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/dout_reg_1_cry_0_0_S0 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/N_2 undriven or does not drive anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROC0 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROC1 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROC2 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROC3 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROC4 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROC5 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROC6 undriven or does not drive

                                    Page 9




Design:  INS350_5J_JZ                                  Date:  09/25/25  10:29:38

Removed logic (cont)
--------------------
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROC7 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROC8 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROC9 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROC10 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROC11 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROC12 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROC13 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROC14 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROC15 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROC16 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROC17 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROB0 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROB1 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROB2 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROB3 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROB4 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROB5 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROB6 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROB7 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROB8 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROB9 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROB10 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROB11 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROB12 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROB13 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROB14 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROB15 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROB16 undriven or does not drive

                                   Page 10




Design:  INS350_5J_JZ                                  Date:  09/25/25  10:29:38

Removed logic (cont)
--------------------
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROB17 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROA0 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROA1 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROA2 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROA3 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROA4 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROA5 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROA6 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROA7 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROA8 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROA9 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROA10 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROA11 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROA12 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROA13 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROA14 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROA15 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROA16 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_ROA17 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_SROB0 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_SROB1 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_SROB2 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_SROB3 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_SROB4 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_SROB5 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_SROB6 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_SROB7 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_SROB8 undriven or does not drive

                                   Page 11




Design:  INS350_5J_JZ                                  Date:  09/25/25  10:29:38

Removed logic (cont)
--------------------
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_SROB9 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_SROB10 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_SROB11 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_SROB12 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_SROB13 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_SROB14 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_SROB15 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_SROB16 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_SROB17 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_SROA0 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_SROA1 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_SROA2 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_SROA3 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_SROA4 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_SROA5 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_SROA6 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_SROA7 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_SROA8 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_SROA9 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_SROA10 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_SROA11 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_SROA12 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_SROA13 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_SROA14 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_SROA15 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_SROA16 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_SROA17 undriven or does not drive
     anything - clipped.
Signal signal_process/modu/un1_dout_mult_P0 undriven or does not drive anything

                                   Page 12




Design:  INS350_5J_JZ                                  Date:  09/25/25  10:29:38

Removed logic (cont)
--------------------
     - clipped.
Signal signal_process/modu/un1_dout_mult_P1 undriven or does not drive anything
     - clipped.
Signal signal_process/modu/un1_dout_mult_P2 undriven or does not drive anything
     - clipped.
Signal signal_process/modu/un1_dout_mult_P3 undriven or does not drive anything
     - clipped.
Signal signal_process/modu/un1_dout_mult_P4 undriven or does not drive anything
     - clipped.
Signal signal_process/modu/un1_dout_mult_P5 undriven or does not drive anything
     - clipped.
Signal signal_process/modu/un1_dout_mult_P6 undriven or does not drive anything
     - clipped.
Signal signal_process/modu/un1_dout_mult_P7 undriven or does not drive anything
     - clipped.
Signal signal_process/modu/un1_dout_mult_P8 undriven or does not drive anything
     - clipped.
Signal signal_process/modu/un1_dout_mult_P9 undriven or does not drive anything
     - clipped.
Signal signal_process/modu/un1_dout_mult_P10 undriven or does not drive anything
     - clipped.
Signal signal_process/modu/un1_dout_mult_P11 undriven or does not drive anything
     - clipped.
Signal signal_process/modu/un1_dout_mult_P12 undriven or does not drive anything
     - clipped.
Signal signal_process/modu/un1_dout_mult_P27 undriven or does not drive anything
     - clipped.
Signal signal_process/modu/un1_dout_mult_P28 undriven or does not drive anything
     - clipped.
Signal signal_process/modu/un1_dout_mult_P29 undriven or does not drive anything
     - clipped.
Signal signal_process/modu/un1_dout_mult_P30 undriven or does not drive anything
     - clipped.
Signal signal_process/modu/un1_dout_mult_P31 undriven or does not drive anything
     - clipped.
Signal signal_process/modu/un1_dout_mult_P32 undriven or does not drive anything
     - clipped.
Signal signal_process/modu/un1_dout_mult_P33 undriven or does not drive anything
     - clipped.
Signal signal_process/modu/un1_dout_mult_P34 undriven or does not drive anything
     - clipped.
Signal signal_process/modu/un1_dout_mult_P35 undriven or does not drive anything
     - clipped.
Signal signal_process/modu/un1_dout_mult_SIGNEDP undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_ROC0_1 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROC1_1 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROC2_1 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROC3_1 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROC4_1 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROC5_1 undriven or does not drive anything

                                   Page 13




Design:  INS350_5J_JZ                                  Date:  09/25/25  10:29:38

Removed logic (cont)
--------------------
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROC6_1 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROC7_1 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROC8_1 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROC9_1 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROC10_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_ROC11_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_ROC12_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_ROC13_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_ROC14_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_ROC15_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_ROC16_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_ROC17_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_ROB0_1 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROB1_1 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROB2_1 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROB3_1 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROB4_1 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROB5_1 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROB6_1 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROB7_1 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROB8_1 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROB9_1 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROB10_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_ROB11_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_ROB12_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_ROB13_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_ROB14_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_ROB15_1 undriven or does not drive

                                   Page 14




Design:  INS350_5J_JZ                                  Date:  09/25/25  10:29:38

Removed logic (cont)
--------------------
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_ROB16_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_ROB17_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_ROA0_1 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROA1_1 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROA2_1 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROA3_1 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROA4_1 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROA5_1 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROA6_1 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROA7_1 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROA8_1 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROA9_1 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROA10_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_ROA11_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_ROA12_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_ROA13_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_ROA14_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_ROA15_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_ROA16_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_ROA17_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROB0_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROB1_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROB2_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROB3_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROB4_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROB5_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROB6_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROB7_1 undriven or does not drive

                                   Page 15




Design:  INS350_5J_JZ                                  Date:  09/25/25  10:29:38

Removed logic (cont)
--------------------
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROB8_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROB9_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROB10_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROB11_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROB12_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROB13_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROB14_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROB15_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROB16_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROB17_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROA0_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROA1_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROA2_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROA3_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROA4_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROA5_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROA6_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROA7_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROA8_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROA9_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROA10_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROA11_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROA12_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROA13_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROA14_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROA15_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROA16_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROA17_1 undriven or does not drive

                                   Page 16




Design:  INS350_5J_JZ                                  Date:  09/25/25  10:29:38

Removed logic (cont)
--------------------
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_2[19] undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_2[20] undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_2[21] undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_2[22] undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_2[23] undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_2[24] undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_2[25] undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_2[26] undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_2[27] undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_2[28] undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_2[29] undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_2[30] undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_2[31] undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_2[32] undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_2[33] undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_2[34] undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_2[35] undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_SIGNEDP_1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_ROC0_0 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROC1_0 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROC2_0 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROC3_0 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROC4_0 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROC5_0 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROC6_0 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROC7_0 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROC8_0 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROC9_0 undriven or does not drive anything

                                   Page 17




Design:  INS350_5J_JZ                                  Date:  09/25/25  10:29:38

Removed logic (cont)
--------------------
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROC10_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_ROC11_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_ROC12_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_ROC13_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_ROC14_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_ROC15_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_ROC16_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_ROC17_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROB0_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROB1_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROB2_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROB3_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROB4_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROB5_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROB6_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROB7_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROB8_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROB9_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROB10_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROB11_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROB12_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROB13_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROB14_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROB15_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROB16_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROB17_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROA0_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROA1_0 undriven or does not drive

                                   Page 18




Design:  INS350_5J_JZ                                  Date:  09/25/25  10:29:38

Removed logic (cont)
--------------------
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROA2_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROA3_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROA4_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROA5_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROA6_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROA7_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROA8_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROA9_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROA10_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROA11_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROA12_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROA13_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROA14_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROA15_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROA16_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_SROA17_0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_ROC0 undriven or does not drive anything -
     clipped.
Signal signal_process/rs422/un3_p_sum_ROC1 undriven or does not drive anything -
     clipped.
Signal signal_process/rs422/un3_p_sum_ROC2 undriven or does not drive anything -
     clipped.
Signal signal_process/rs422/un3_p_sum_ROC3 undriven or does not drive anything -
     clipped.
Signal signal_process/rs422/un3_p_sum_ROC4 undriven or does not drive anything -
     clipped.
Signal signal_process/rs422/un3_p_sum_ROC5 undriven or does not drive anything -
     clipped.
Signal signal_process/rs422/un3_p_sum_ROC6 undriven or does not drive anything -
     clipped.
Signal signal_process/rs422/un3_p_sum_ROC7 undriven or does not drive anything -
     clipped.
Signal signal_process/rs422/un3_p_sum_ROC8 undriven or does not drive anything -
     clipped.
Signal signal_process/rs422/un3_p_sum_ROC9 undriven or does not drive anything -
     clipped.
Signal signal_process/rs422/un3_p_sum_ROC10 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROC11 undriven or does not drive anything

                                   Page 19




Design:  INS350_5J_JZ                                  Date:  09/25/25  10:29:38

Removed logic (cont)
--------------------
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROC12 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROC13 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROC14 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROC15 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROC16 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_ROC17 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_SROB0 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_SROB1 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_SROB2 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_SROB3 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_SROB4 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_SROB5 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_SROB6 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_SROB7 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_SROB8 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_SROB9 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_SROB10 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_SROB11 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_SROB12 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_SROB13 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_SROB14 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_SROB15 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_SROB16 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_SROB17 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_SROA0 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_SROA1 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_SROA2 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_SROA3 undriven or does not drive anything

                                   Page 20




Design:  INS350_5J_JZ                                  Date:  09/25/25  10:29:38

Removed logic (cont)
--------------------
     - clipped.
Signal signal_process/rs422/un3_p_sum_SROA4 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_SROA5 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_SROA6 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_SROA7 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_SROA8 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_SROA9 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_SROA10 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_SROA11 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_SROA12 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_SROA13 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_SROA14 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_SROA15 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_SROA16 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_SROA17 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_add_cry_17_0_COUT undriven or does not
     drive anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_cry_0_0_S1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_cry_0_0_S0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/N_7 undriven or does not drive anything - clipped.
Signal signal_process/rs422/un2_sum_cry_53_0_COUT undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un2_sum_cry_0_0_S1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un2_sum_cry_0_0_S0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/N_8 undriven or does not drive anything - clipped.
Signal signal_process/rs422/dout_1_s_31_0_S1 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/dout_1_s_31_0_COUT undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/dout_1_cry_0_0_S1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/dout_1_cry_0_0_S0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/N_9 undriven or does not drive anything - clipped.
Signal signal_process/rs422/count_pos_s_0_S1[7] undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/count_pos_s_0_COUT[7] undriven or does not drive
     anything - clipped.

                                   Page 21




Design:  INS350_5J_JZ                                  Date:  09/25/25  10:29:38

Removed logic (cont)
--------------------
Signal signal_process/rs422/count_pos_cry_0_S0[0] undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/N_10 undriven or does not drive anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_OVERUNDER undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_UNDER undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_OVER undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_EQPATB undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_EQPAT undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_EQOM undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_EQZM undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_EQZ undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO0 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO1 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO2 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO3 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO4 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO5 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO6 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO7 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO8 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO9 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO10 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO11 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO12 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO13 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO14 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO15 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO16 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO17 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO18 undriven or does not drive

                                   Page 22




Design:  INS350_5J_JZ                                  Date:  09/25/25  10:29:38

Removed logic (cont)
--------------------
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO19 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO20 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO21 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO22 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO23 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO24 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO25 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO26 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO27 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO28 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO29 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO30 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO31 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO32 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO33 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO34 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO35 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO36 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO37 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO38 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO39 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO40 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO41 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO42 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO43 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO44 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO45 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO46 undriven or does not drive

                                   Page 23




Design:  INS350_5J_JZ                                  Date:  09/25/25  10:29:38

Removed logic (cont)
--------------------
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO47 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO48 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO49 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO50 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO51 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO52 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_CO53 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_R0 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_add_R1 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_add_R2 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_add_R3 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_add_R4 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_add_R5 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_add_R6 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_add_R7 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_add_R8 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_add_R9 undriven or does not drive anything
     - clipped.
Signal signal_process/rs422/un3_p_sum_add_R10 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_R11 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_R12 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_R13 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_R14 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_R15 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_R16 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_R17 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_R18 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_R19 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_R20 undriven or does not drive

                                   Page 24




Design:  INS350_5J_JZ                                  Date:  09/25/25  10:29:38

Removed logic (cont)
--------------------
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_R21 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_R22 undriven or does not drive
     anything - clipped.
Signal signal_process/rs422/un3_p_sum_add_SIGNEDR undriven or does not drive
     anything - clipped.
Signal signal_process/trans/un6_count_s_19_0_S1 undriven or does not drive
     anything - clipped.
Signal signal_process/trans/un6_count_s_19_0_COUT undriven or does not drive
     anything - clipped.
Signal signal_process/trans/un6_count_cry_0_0_S1 undriven or does not drive
     anything - clipped.
Signal signal_process/trans/un6_count_cry_0_0_S0 undriven or does not drive
     anything - clipped.
Signal signal_process/trans/N_1 undriven or does not drive anything - clipped.
Block CLK120/PLLInst_0_RNIGQC was optimized away.
Block signal_process/demodu/fifo/INV_6 was optimized away.
Block signal_process/demodu/fifo/INV_7 was optimized away.
Block signal_process/demodu/fifo/INV_8 was optimized away.
Block GND was optimized away.
Block wendu/VCC was optimized away.
Block u_uart/U0/VCC was optimized away.
Block signal_process/ctrl_signal/VCC was optimized away.
Block signal_process/demodu/VCC was optimized away.
Block signal_process/demodu/fifo/VCC was optimized away.
Block signal_process/integ/VCC was optimized away.
Block signal_process/modu/GND was optimized away.
Block signal_process/modu/VCC was optimized away.
Block signal_process/rs422/GND was optimized away.
Block signal_process/rs422/VCC was optimized away.
Block signal_process/trans/VCC was optimized away.

Memory Usage
------------

/signal_process/demodu/fifo:
    EBRs: 2
    RAM SLICEs: 0
    Logic SLICEs: 33
    PFU Registers: 26
    -Contains EBR pdp_ram_0_1_0:  TYPE= PDPW16KD,  Width= 19,  Depth_R= 128,
         Depth_W= 128,  REGMODE= NOREG,  RESETMODE= ASYNC,  ASYNC_RESET_RELEASE=
         SYNC,  GSR= DISABLED,  MEM_LPC_FILE= Asys_fifo56X16.lpc
    -Contains EBR pdp_ram_0_0_1:  TYPE= PDPW16KD,  Width= 36,  Depth_R= 128,
         Depth_W= 128,  REGMODE= NOREG,  RESETMODE= ASYNC,  ASYNC_RESET_RELEASE=
         SYNC,  GSR= DISABLED,  MEM_LPC_FILE= Asys_fifo56X16.lpc

DSP Component Details
---------------------


     . ALU54B  signal_process/rs422/un3_p_sum_add[0:53]:

     54-Bit ALU
	Opcode  0		1

                                   Page 25




Design:  INS350_5J_JZ                                  Date:  09/25/25  10:29:38

DSP Component Details (cont)
----------------------------
	Opcode  1		0
	Opcode  2		0
	Opcode  3		0
	Opcode  4		0
	Opcode  5		0
	Opcode  6		0
	Opcode  7		0
	Opcode  8		0
	Opcode  9		1
	Opcode 10		0

     	OpcodeOP0 Registers	CLK	CE	RST
	--------------------------------------------
		Input		    	   	    
		Pipeline	    	   	    

     	OpcodeOP1 Registers	CLK	CE	RST
	--------------------------------------------
		Input		    	--	--
		Pipeline	    	--	--

     	OpcodeIN Registers	CLK	CE	RST
	--------------------------------------------
		Input		    	   	    
		Pipeline	    	   	    
Data
	Input Registers		CLK	CE	RST
	--------------------------------------------
		C0		    	   	    
		C1		    	   	    
		CFB		    	   	    

     	Output Register		CLK	CE	RST
	--------------------------------------------
		Output0		    	   	    
		Output1		    	   	    

     	Flag Register		CLK	CE	RST
	--------------------------------------------
		Flag		    	   	    
Other
	MCPAT_SOURCE	STATIC
	MASKPAT_SOURCE	STATIC
	MASK01		0x00000000000000
	MCPAT		0x00000000000000
	MASKPAT		0x00000000000000
	RNDPAT		0x00000000000000
	PSE17		0b00000000000000000
	PSE44		0b00000000000000000000000000
	PSE53		0b00000000
	GSR		DISABLED
	RESETMODE	ASYNC
	MULT9_MODE	DISABLED
     
	LEGACY  	DISABLED
     

                                   Page 26




Design:  INS350_5J_JZ                                  Date:  09/25/25  10:29:38

DSP Component Details (cont)
----------------------------
	CLK0_DIV	ENABLED
	CLK1_DIV	ENABLED
	CLK2_DIV	ENABLED
	CLK3_DIV	ENABLED

     . MULT18X18D  signal_process/rs422/un3_p_sum[0:35]:

     Multiplier
	Operation A		Unsigned
	Operation A Registers	CLK	CE	RST
	--------------------------------------------
		Input		CLK0	CE0	RST0
		Pipeline	    	   	    

     	Operation B		Unsigned
	Operation B Registers	CLK	CE	RST
	--------------------------------------------
		Input		    	   	    
		Pipeline	    	   	    
Data
	Input Registers		CLK	CE	RST	Source
	-------------------------------------------------------
		A		CLK0	CE0	RST0	A Data In
		B		    	   	    	B Data In
		C		    	   	    	N/A
	Cascaded Match

     	Pipeline Registers	CLK	CE	RST
	--------------------------------------------
		Pipe		    	   	    

     	Output Register		CLK	CE	RST
	--------------------------------------------
		Output		    	   	    
Other
	SOURCEB_MODE	B_SHIFT
	MULT_BYPASS	DISABLED
	CAS_MATCH_REG	FALSE
	GSR		DISABLED
	RESETMODE	SYNC
	CLK0_DIV	ENABLED
	CLK1_DIV	ENABLED
	CLK2_DIV	ENABLED
	CLK3_DIV	ENABLED
	HIGHSPEED_CLK	NONE
	PSE17		0b00000000000000000
	PSE35		0b00000000000000000
     
	C_PSE17		0b00000000000000000

     . MULT18X18D  signal_process/rs422/un3_p_sum[18:53]:

     Multiplier
	Operation A		Unsigned
	Operation A Registers	CLK	CE	RST
	--------------------------------------------

                                   Page 27




Design:  INS350_5J_JZ                                  Date:  09/25/25  10:29:38

DSP Component Details (cont)
----------------------------
		Input		CLK0	CE0	RST0
		Pipeline	    	   	    

     	Operation B		Unsigned
	Operation B Registers	CLK	CE	RST
	--------------------------------------------
		Input		    	   	    
		Pipeline	    	   	    
Data
	Input Registers		CLK	CE	RST	Source
	-------------------------------------------------------
		A		CLK0	CE0	RST0	A Data In
		B		    	   	    	B Data In
		C		    	   	    	N/A
	Cascaded Match

     	Pipeline Registers	CLK	CE	RST
	--------------------------------------------
		Pipe		    	   	    

     	Output Register		CLK	CE	RST
	--------------------------------------------
		Output		    	   	    
Other
	SOURCEB_MODE	B_SHIFT
	MULT_BYPASS	DISABLED
	CAS_MATCH_REG	FALSE
	GSR		DISABLED
	RESETMODE	SYNC
	CLK0_DIV	ENABLED
	CLK1_DIV	ENABLED
	CLK2_DIV	ENABLED
	CLK3_DIV	ENABLED
	HIGHSPEED_CLK	NONE
	PSE17		0b00000000000000000
	PSE35		0b00000000000000000
     
	C_PSE17		0b00000000000000000

     . MULT18X18D  signal_process/rs422/un3_p_sum[36:71]:

     Multiplier
	Operation A		Unsigned
	Operation A Registers	CLK	CE	RST
	--------------------------------------------
		Input		CLK0	CE0	RST0
		Pipeline	    	   	    

     	Operation B		Unsigned
	Operation B Registers	CLK	CE	RST
	--------------------------------------------
		Input		    	   	    
		Pipeline	    	   	    
Data
	Input Registers		CLK	CE	RST	Source
	-------------------------------------------------------

                                   Page 28




Design:  INS350_5J_JZ                                  Date:  09/25/25  10:29:38

DSP Component Details (cont)
----------------------------
		A		CLK0	CE0	RST0	A Data In
		B		    	   	    	B Data In
		C		    	   	    	N/A
	Cascaded Match

     	Pipeline Registers	CLK	CE	RST
	--------------------------------------------
		Pipe		    	   	    

     	Output Register		CLK	CE	RST
	--------------------------------------------
		Output		    	   	    
Other
	SOURCEB_MODE	B_SHIFT
	MULT_BYPASS	DISABLED
	CAS_MATCH_REG	FALSE
	GSR		DISABLED
	RESETMODE	SYNC
	CLK0_DIV	ENABLED
	CLK1_DIV	ENABLED
	CLK2_DIV	ENABLED
	CLK3_DIV	ENABLED
	HIGHSPEED_CLK	NONE
	PSE17		0b00000000000000000
	PSE35		0b00000000000000000
     
	C_PSE17		0b00000000000000000

     . MULT18X18D  signal_process/modu/un1_dout_mult[26:0]:

     Multiplier
	Operation A		Unsigned
	Operation A Registers	CLK	CE	RST
	--------------------------------------------
		Input		CLK0	CE0	RST0
		Pipeline	CLK0	CE1	RST0

     	Operation B		Unsigned
	Operation B Registers	CLK	CE	RST
	--------------------------------------------
		Input		    	   	    
		Pipeline	CLK0	CE1	RST0
Data
	Input Registers		CLK	CE	RST	Source
	-------------------------------------------------------
		A		CLK0	CE0	RST0	A Data In
		B		    	   	    	B Data In
		C		    	   	    	N/A
	Cascaded Match

     	Pipeline Registers	CLK	CE	RST
	--------------------------------------------
		Pipe		CLK0	CE1	RST0

     	Output Register		CLK	CE	RST
	--------------------------------------------

                                   Page 29




Design:  INS350_5J_JZ                                  Date:  09/25/25  10:29:38

DSP Component Details (cont)
----------------------------
		Output		CLK0	CE0	RST0
Other
	SOURCEB_MODE	B_SHIFT
	MULT_BYPASS	DISABLED
	CAS_MATCH_REG	FALSE
	GSR		DISABLED
	RESETMODE	ASYNC
	CLK0_DIV	ENABLED
	CLK1_DIV	ENABLED
	CLK2_DIV	ENABLED
	CLK3_DIV	ENABLED
	HIGHSPEED_CLK	NONE
	PSE17		0b00000000000000000
	PSE35		0b00000000000000000
     
	C_PSE17		0b00000000000000000

     

PLL/DLL Summary
---------------

PLL 1:                                     Pin/Node Value
  PLL Instance Name:                                CLK120/PLLInst_0
  PLL Type:                                         EHXPLLL
  Input Clock:                             PIN      clk_in_c
  Input Clock2:                                     NONE
  Input Clock select:                               NONE
  Output Clock(P):                         NODE     clk120mhz
  Output Clock(S):                         NODE     clk_AD
  Output Clock(S2):                        PIN      clk_AD_t_c
  Output Clock(S3):                                 NONE
  Feedback Signal:                         NODE     clk120mhz
  Reset Signal:                                     NONE
  Standby Signal:                          NODE     CLK120/GND
  PLL LOCK signal:                         NODE     CLK120/locked_out
  PLL Internal LOCK Signal:                         NONE
  Input Clock Frequency (MHz):                      20.0000
  Output Clock(P) Frequency (MHz):                  120.0000
  Output Clock(S) Frequency (MHz):                  60.0000
  Output Clock(S2) Frequency (MHz):                 60.0000
  Output Clock(S3) Frequency (MHz):                 NA
  CLKOP Post Divider A Input:                       DIVA
  CLKOS Post Divider B Input:                       DIVB
  CLKOS2 Post Divider C Input:                      DIVC
  CLKOS3 Post Divider D Input:                      DIVD
  Pre Divider A Input:                              NONE
  Pre Divider B Input:                              NONE
  Pre Divider C Input:                              NONE
  Pre Divider D Input:                              NONE
  FB_MODE:                                          CLKOP
  CLKI Divider:                                     1
  CLKFB Divider:                                    6
  CLKOP Divider:                                    5
  CLKOS Divider:                                    10
  CLKOS2 Divider:                                   10

                                   Page 30




Design:  INS350_5J_JZ                                  Date:  09/25/25  10:29:38

PLL/DLL Summary (cont)
----------------------
  CLKOS3 Divider:                                   1
  Fractional N Divider:                             NONE
  CLKOP Desired Phase Shift(degree):                0
  CLKOP Trim Option Rising/Falling:                 FALLING
  CLKOP Trim Option Delay:                          0
  CLKOS Desired Phase Shift(degree):                0
  CLKOS Trim Option Rising/Falling:                 FALLING
  CLKOS Trim Option Delay:                          0
  CLKOS2 Desired Phase Shift(degree):               0
  CLKOS2 Trim Option Rising/Falling:                NONE
  CLKOS2 Trim Option Delay:                         NONE
  CLKOS3 Desired Phase Shift(degree):               0
  CLKOS3 Trim Option Rising/Falling:                NONE
  CLKOS3 Trim Option Delay:                         NONE

ASIC Components
---------------

Instance Name: signal_process/rs422/un3_p_sum_add[0:53]
         Type: ALU54B
Instance Name: signal_process/rs422/un3_p_sum[0:35]
         Type: MULT18X18D
Instance Name: signal_process/rs422/un3_p_sum[18:53]
         Type: MULT18X18D
Instance Name: signal_process/rs422/un3_p_sum[36:71]
         Type: MULT18X18D
Instance Name: signal_process/modu/un1_dout_mult[26:0]
         Type: MULT18X18D
Instance Name: signal_process/demodu/fifo/pdp_ram_0_1_0
         Type: PDPW16KD
Instance Name: signal_process/demodu/fifo/pdp_ram_0_0_1
         Type: PDPW16KD
Instance Name: CLK120/PLLInst_0
         Type: EHXPLLL

GSR Usage
---------

GSR Component:
   The local reset signal 'CLK120/locked_out' of the design has been inferred as
        Global Set Reset (GSR). The reset signal used for GSR control is
        'CLK120/locked_out'.
        

     GSR Property:
   The design components with GSR property set to ENABLED will respond to global
        set reset while the components with GSR property set to DISABLED will
        not.
        

     Components on inferred reset domain with GSR Property disabled
--------------------------------------------------------------

     These components have the GSR property set to DISABLED and are on the
     inferred reset domain. The components will respond to the reset signal
     'CLK120/locked_out' via the local reset on the component and not the GSR

                                   Page 31




Design:  INS350_5J_JZ                                  Date:  09/25/25  10:29:38

GSR Usage (cont)
----------------
     component.

     Type and number of components of the type: 
   PDPW16KD = 2

     Type and instance name of component: 
   PDPW16KD : signal_process/demodu/fifo/pdp_ram_0_1_0
   PDPW16KD : signal_process/demodu/fifo/pdp_ram_0_0_1

Run Time and Memory Usage
-------------------------

   Total CPU Time: 0 secs  
   Total REAL Time: 0 secs  
   Peak Memory Usage: 135 MB
        









































                                   Page 32


Copyright (c) 1991-1994 by NeoCAD Inc. All rights reserved.
     Copyright (c) 1995 AT&T Corp.   All rights reserved.
     Copyright (c) 1995-2001 Lucent Technologies Inc.  All rights reserved.
     Copyright (c) 2001 Agere Systems   All rights reserved.
     Copyright (c) 2002-2020 Lattice Semiconductor Corporation,  All rights
     reserved.
