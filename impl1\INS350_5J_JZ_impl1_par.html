<HTML>
<HEAD><TITLE>Place & Route Report</TITLE>
<STYLE TYPE="text/css">
<!--
 body,pre{
    font-family:'Courier New', monospace;
    color: #000000;
    font-size:88%;
    background-color: #ffffff;
}
h1 {
    font-weight: bold;
    margin-top: 24px;
    margin-bottom: 10px;
    border-bottom: 3px solid #000;    font-size: 1em;
}
h2 {
    font-weight: bold;
    margin-top: 18px;
    margin-bottom: 5px;
    font-size: 0.90em;
}
h3 {
    font-weight: bold;
    margin-top: 12px;
    margin-bottom: 5px;
    font-size: 0.80em;
}
p {
    font-size:78%;
}
P.Table {
    margin-top: 4px;
    margin-bottom: 4px;
    margin-right: 4px;
    margin-left: 4px;
}
table
{
    border-width: 1px 1px 1px 1px;
    border-style: solid solid solid solid;
    border-color: black black black black;
    border-collapse: collapse;
}
th {
    font-weight:bold;
    padding: 4px;
    border-width: 1px 1px 1px 1px;
    border-style: solid solid solid solid;
    border-color: black black black black;
    vertical-align:top;
    text-align:left;
    font-size:78%;
}
td {
    padding: 4px;
    border-width: 1px 1px 1px 1px;
    border-style: solid solid solid solid;
    border-color: black black black black;
    vertical-align:top;
    font-size:78%;
}
a {
    color:#013C9A;
    text-decoration:none;
}

a:visited {
    color:#013C9A;
}

a:hover, a:active {
    text-decoration:underline;
    color:#5BAFD4;
}
.pass
{
background-color: #00ff00;
}
.fail
{
background-color: #ff0000;
}
.comment
{
    font-size: 90%;
    font-style: italic;
}

-->
</STYLE>
</HEAD>
<PRE><A name="Par"></A>PAR: Place And Route Diamond (64-bit) 3.12.1.454.
Copyright (c) 1991-1994 by NeoCAD Inc. All rights reserved.
Copyright (c) 1995 AT&amp;T Corp.   All rights reserved.
Copyright (c) 1995-2001 Lucent Technologies Inc.  All rights reserved.
Copyright (c) 2001 Agere Systems   All rights reserved.
Copyright (c) 2002-2020 Lattice Semiconductor Corporation,  All rights reserved.
Thu Sep 25 10:29:42 2025

D:/Software/lscc/diamond/3.12/ispfpga\bin\nt64\par -f INS350_5J_JZ_impl1.p2t
INS350_5J_JZ_impl1_map.ncd INS350_5J_JZ_impl1.dir INS350_5J_JZ_impl1.prf -gui
-msgset D:/Project/TLH50_03J_JZ_20250925/promote.xml


Preference file: INS350_5J_JZ_impl1.prf.

<A name="par_cts"></A><B><U><big>Cost Table Summary</big></U></B>
Level/       Number       Worst        Timing       Worst        Timing       Run          NCD
Cost [ncd]   Unrouted     Slack        Score        Slack(hold)  Score(hold)  Time         Status
----------   --------     -----        ------       -----------  -----------  ----         ------
5_1   *      0            1.227        0            0.174        0            22           Completed
* : Design saved.

Total (real) run time for 1-seed: 22 secs 

par done!

Note: user must run &apos;Trace&apos; for timing closure signoff.

Lattice Place and Route Report for Design &quot;INS350_5J_JZ_impl1_map.ncd&quot;
Thu Sep 25 10:29:42 2025


<A name="par_best"></A><B><U><big>Best Par Run</big></U></B>
PAR: Place And Route Diamond (64-bit) 3.12.1.454.
Command Line: par -w -l 5 -i 6 -t 1 -c 0 -e 0 -gui -msgset D:/Project/TLH50_03J_JZ_20250925/promote.xml -exp parUseNBR=1:parCDP=auto:parCDR=1:parPathBased=OFF:parASE=1 INS350_5J_JZ_impl1_map.ncd INS350_5J_JZ_impl1.dir/5_1.ncd INS350_5J_JZ_impl1.prf
Preference file: INS350_5J_JZ_impl1.prf.
Placement level-cost: 5-1.
Routing Iterations: 6

Loading design for application par from file INS350_5J_JZ_impl1_map.ncd.
Design name: INS350_5J_JZ
NCD version: 3.3
Vendor:      LATTICE
Device:      LFE5U-25F
Package:     CABGA256
Performance: 7
Loading device for application par from file &apos;sa5p25.nph&apos; in environment: D:/Software/lscc/diamond/3.12/ispfpga.
Package Status:                     Final          Version 1.42.
Performance Hardware Data Status:   Final          Version 55.1.
License checked out.


Ignore Preference Error(s):  True

<A name="par_dus"></A><B><U><big>Device utilization summary:</big></U></B>

   PIO (prelim)      32/197          16% used
                     32/197          16% bonded
   IOLOGIC           28/199          14% used

   SLICE            697/12144         5% used

   GSR                1/1           100% used
   EBR                2/56            3% used
   PLL                1/2            50% used
   MULT18             4/28           14% used
   ALU54              1/14            7% used


Number of Signals: 2184
Number of Connections: 4201

Pin Constraint Summary:
   32 out of 32 pins locked (100% locked).


The following 12 signals are selected to use the primary clock routing resources:
    clk120mhz (driver: CLK120/PLLInst_0, clk/ce/sr load #: 446/0/0)
    clk_AD (driver: CLK120/PLLInst_0, clk/ce/sr load #: 101/0/0)
    wendu.clk_us (driver: wendu/SLICE_241, clk/ce/sr load #: 36/0/0)
    signal_process/demodu/AD_validcnt7 (driver: signal_process/demodu/SLICE_285, clk/ce/sr load #: 0/0/33)
    signal_process/rs422/trans_state[5] (driver: signal_process/rs422/SLICE_482, clk/ce/sr load #: 0/0/31)
    signal_process/rs422/N_80_i (driver: signal_process/rs422/SLICE_627, clk/ce/sr load #: 0/31/0)
    signal_process/demodu/N_410_i (driver: signal_process/demodu/SLICE_594, clk/ce/sr load #: 0/29/0)
    signal_process/polarity (driver: signal_process/ctrl_signal/SLICE_405, clk/ce/sr load #: 0/28/0)
    signal_process/integ/DA_dout5 (driver: signal_process/integ/SLICE_641, clk/ce/sr load #: 0/28/0)
    signal_process/demodu/median_sum_n_1_sqmuxa (driver: signal_process/demodu/SLICE_624, clk/ce/sr load #: 0/28/0)
    signal_process/demodu/Latch_sum (driver: signal_process/demodu/SLICE_287, clk/ce/sr load #: 0/28/0)
    signal_process/demodu/N_417_i (driver: signal_process/demodu/SLICE_624, clk/ce/sr load #: 0/28/0)


Signal CLK120/locked_out is selected as Global Set/Reset.
Starting Placer Phase 0.
..............
Finished Placer Phase 0.  REAL time: 3 secs 


Starting Placer Phase 1.
......................
Placer score = 383024.
Finished Placer Phase 1.  REAL time: 9 secs 

Starting Placer Phase 2.
.
Placer score =  319973
Finished Placer Phase 2.  REAL time: 9 secs 



<A name="par_clk"></A><B><U><big>Clock Report</big></U></B>

Global Clock Resources:
  CLK_PIN    : 0 out of 12 (0%)
  GR_PCLK    : 0 out of 12 (0%)
  PLL        : 1 out of 2 (50%)
  DCS        : 0 out of 2 (0%)
  DCC        : 0 out of 60 (0%)
  CLKDIV     : 0 out of 4 (0%)

Quadrant TL Clocks:
  PRIMARY &quot;clk120mhz&quot; from CLKOP on comp &quot;CLK120/PLLInst_0&quot; on PLL site &quot;PLL_BR0&quot;, CLK/CE/SR load = 193
  PRIMARY &quot;clk_AD&quot; from CLKOS on comp &quot;CLK120/PLLInst_0&quot; on PLL site &quot;PLL_BR0&quot;, CLK/CE/SR load = 8
  PRIMARY &quot;signal_process/demodu/AD_validcnt7&quot; from F0 on comp &quot;signal_process/demodu/SLICE_285&quot; on site &quot;R22C23C&quot;, CLK/CE/SR load = 4
  PRIMARY &quot;signal_process/rs422/trans_state[5]&quot; from Q0 on comp &quot;signal_process/rs422/SLICE_482&quot; on site &quot;R23C31D&quot;, CLK/CE/SR load = 31
  PRIMARY &quot;signal_process/rs422/N_80_i&quot; from F1 on comp &quot;signal_process/rs422/SLICE_627&quot; on site &quot;R18C33D&quot;, CLK/CE/SR load = 31
  PRIMARY &quot;signal_process/polarity&quot; from Q0 on comp &quot;signal_process/ctrl_signal/SLICE_405&quot; on site &quot;R19C21A&quot;, CLK/CE/SR load = 27

  PRIMARY  : 6 out of 16 (37%)

Quadrant TR Clocks:
  PRIMARY &quot;clk120mhz&quot; from CLKOP on comp &quot;CLK120/PLLInst_0&quot; on PLL site &quot;PLL_BR0&quot;, CLK/CE/SR load = 33

  PRIMARY  : 1 out of 16 (6%)

Quadrant BL Clocks:
  PRIMARY &quot;clk120mhz&quot; from CLKOP on comp &quot;CLK120/PLLInst_0&quot; on PLL site &quot;PLL_BR0&quot;, CLK/CE/SR load = 151
  PRIMARY &quot;clk_AD&quot; from CLKOS on comp &quot;CLK120/PLLInst_0&quot; on PLL site &quot;PLL_BR0&quot;, CLK/CE/SR load = 93
  PRIMARY &quot;wendu.clk_us&quot; from Q0 on comp &quot;wendu/SLICE_241&quot; on site &quot;R38C31A&quot;, CLK/CE/SR load = 19
  PRIMARY &quot;signal_process/demodu/AD_validcnt7&quot; from F0 on comp &quot;signal_process/demodu/SLICE_285&quot; on site &quot;R22C23C&quot;, CLK/CE/SR load = 29
  PRIMARY &quot;signal_process/demodu/N_410_i&quot; from F0 on comp &quot;signal_process/demodu/SLICE_594&quot; on site &quot;R23C24C&quot;, CLK/CE/SR load = 29
  PRIMARY &quot;signal_process/polarity&quot; from Q0 on comp &quot;signal_process/ctrl_signal/SLICE_405&quot; on site &quot;R19C21A&quot;, CLK/CE/SR load = 1
  PRIMARY &quot;signal_process/integ/DA_dout5&quot; from F0 on comp &quot;signal_process/integ/SLICE_641&quot; on site &quot;R17C21A&quot;, CLK/CE/SR load = 28
  PRIMARY &quot;signal_process/demodu/median_sum_n_1_sqmuxa&quot; from F1 on comp &quot;signal_process/demodu/SLICE_624&quot; on site &quot;R19C20B&quot;, CLK/CE/SR load = 28
  PRIMARY &quot;signal_process/demodu/Latch_sum&quot; from Q0 on comp &quot;signal_process/demodu/SLICE_287&quot; on site &quot;R23C24A&quot;, CLK/CE/SR load = 28
  PRIMARY &quot;signal_process/demodu/N_417_i&quot; from F0 on comp &quot;signal_process/demodu/SLICE_624&quot; on site &quot;R19C20B&quot;, CLK/CE/SR load = 28

  PRIMARY  : 10 out of 16 (62%)

Quadrant BR Clocks:
  PRIMARY &quot;clk120mhz&quot; from CLKOP on comp &quot;CLK120/PLLInst_0&quot; on PLL site &quot;PLL_BR0&quot;, CLK/CE/SR load = 69
  PRIMARY &quot;wendu.clk_us&quot; from Q0 on comp &quot;wendu/SLICE_241&quot; on site &quot;R38C31A&quot;, CLK/CE/SR load = 17

  PRIMARY  : 2 out of 16 (12%)

Edge Clocks:

  No edge clock selected.





+
I/O Usage Summary (final):
   32 out of 197 (16.2%) PIO sites used.
   32 out of 197 (16.2%) bonded PIO sites used.
   Number of PIO comps: 32; differential: 0.
   Number of Vref pins used: 0.

I/O Bank Usage Summary:
+----------+----------------+------------+------------+------------+
| I/O Bank | Usage          | Bank Vccio | Bank Vref1 | Bank Vref2 |
+----------+----------------+------------+------------+------------+
| 0        | 0 / 24 (  0%)  | 3.3V       | -          | -          |
| 1        | 1 / 32 (  3%)  | 3.3V       | -          | -          |
| 2        | 0 / 32 (  0%)  | 3.3V       | -          | -          |
| 3        | 3 / 32 (  9%)  | 3.3V       | -          | -          |
| 6        | 13 / 32 ( 40%) | 3.3V       | -          | -          |
| 7        | 15 / 32 ( 46%) | 3.3V       | -          | -          |
| 8        | 0 / 13 (  0%)  | 3.3V       | -          | -          |
+----------+----------------+------------+------------+------------+

---------------------------------- DSP Report ----------------------------------

DSP Slice #:           1  2  3  4  5  6  7  8  9 10 11 12 13 14
# of MULT9                                                     
# of MULT18               1  2  1                              
# of ALU24                                                     
# of ALU54                   1                                 
# of PRADD9                                                    
# of PRADD18                                                   

DSP Slice  2         Component_Type       Physical_Type                    Instance_Name                  
  MULT18_R13C9         MULT18X18D             MULT18          signal_process/modu/un1_dout_mult[26:0]     

DSP Slice  3         Component_Type       Physical_Type                    Instance_Name                  
 MULT18_R13C13         MULT18X18D             MULT18            signal_process/rs422/un3_p_sum[0:35]      
 MULT18_R13C14         MULT18X18D             MULT18           signal_process/rs422/un3_p_sum[18:53]      
  ALU54_R13C16           ALU54B               ALU54           signal_process/rs422/un3_p_sum_add[0:53]    

DSP Slice  4         Component_Type       Physical_Type                    Instance_Name                  
 MULT18_R13C17         MULT18X18D             MULT18           signal_process/rs422/un3_p_sum[36:71]      

------------------------------ End of DSP Report -------------------------------
Total placer CPU time: 8 secs 

Dumping design to file INS350_5J_JZ_impl1.dir/5_1.ncd.

0 connections routed; 4201 unrouted.
Starting router resource preassignment
DSP info: No dsp pins have been swapped.

Completed router resource preassignment. Real time: 13 secs 

Start NBR router at 10:29:56 09/25/25

*****************************************************************
Info: NBR allows conflicts(one node used by more than one signal)
      in the earlier iterations. In each iteration, it tries to  
      solve the conflicts while keeping the critical connections 
      routed as short as possible. The routing process is said to
      be completed when no conflicts exist and all connections   
      are routed.                                                
Note: NBR uses a different method to calculate timing slacks. The
      worst slack and total negative slack may not be the same as
      that in TRCE report. You should always run TRCE to verify  
      your design.                                               
*****************************************************************

Start NBR special constraint process at 10:29:56 09/25/25

Start NBR section for initial routing at 10:29:56 09/25/25
Level 1, iteration 1
0(0.00%) conflict; 2539(60.44%) untouched conns; 0 (nbr) score; 
Estimated worst slack/total negative slack&lt;setup&gt;: 1.127ns/0.000ns; real time: 15 secs 
Level 2, iteration 1
5(0.00%) conflicts; 2516(59.89%) untouched conns; 0 (nbr) score; 
Estimated worst slack/total negative slack&lt;setup&gt;: 1.227ns/0.000ns; real time: 15 secs 
Level 3, iteration 1
18(0.00%) conflicts; 2233(53.15%) untouched conns; 0 (nbr) score; 
Estimated worst slack/total negative slack&lt;setup&gt;: 1.227ns/0.000ns; real time: 15 secs 
Level 4, iteration 1
147(0.01%) conflicts; 0(0.00%) untouched conn; 0 (nbr) score; 
Estimated worst slack/total negative slack&lt;setup&gt;: 1.227ns/0.000ns; real time: 16 secs 

Info: Initial congestion level at 75% usage is 0
Info: Initial congestion area  at 75% usage is 0 (0.00%)

Start NBR section for normal routing at 10:29:58 09/25/25
Level 1, iteration 1
5(0.00%) conflicts; 160(3.81%) untouched conns; 0 (nbr) score; 
Estimated worst slack/total negative slack&lt;setup&gt;: 1.227ns/0.000ns; real time: 16 secs 
Level 1, iteration 2
5(0.00%) conflicts; 160(3.81%) untouched conns; 0 (nbr) score; 
Estimated worst slack/total negative slack&lt;setup&gt;: 1.227ns/0.000ns; real time: 16 secs 
Level 2, iteration 1
5(0.00%) conflicts; 160(3.81%) untouched conns; 0 (nbr) score; 
Estimated worst slack/total negative slack&lt;setup&gt;: 1.227ns/0.000ns; real time: 16 secs 
Level 3, iteration 1
5(0.00%) conflicts; 156(3.71%) untouched conns; 0 (nbr) score; 
Estimated worst slack/total negative slack&lt;setup&gt;: 1.227ns/0.000ns; real time: 16 secs 
Level 4, iteration 1
72(0.01%) conflicts; 0(0.00%) untouched conn; 0 (nbr) score; 
Estimated worst slack/total negative slack&lt;setup&gt;: 1.227ns/0.000ns; real time: 16 secs 
Level 4, iteration 2
31(0.00%) conflicts; 0(0.00%) untouched conn; 0 (nbr) score; 
Estimated worst slack/total negative slack&lt;setup&gt;: 1.227ns/0.000ns; real time: 16 secs 
Level 4, iteration 3
15(0.00%) conflicts; 0(0.00%) untouched conn; 0 (nbr) score; 
Estimated worst slack/total negative slack&lt;setup&gt;: 1.227ns/0.000ns; real time: 16 secs 
Level 4, iteration 4
7(0.00%) conflicts; 0(0.00%) untouched conn; 0 (nbr) score; 
Estimated worst slack/total negative slack&lt;setup&gt;: 1.227ns/0.000ns; real time: 16 secs 
Level 4, iteration 5
4(0.00%) conflicts; 0(0.00%) untouched conn; 0 (nbr) score; 
Estimated worst slack/total negative slack&lt;setup&gt;: 1.227ns/0.000ns; real time: 17 secs 
Level 4, iteration 6
2(0.00%) conflicts; 0(0.00%) untouched conn; 0 (nbr) score; 
Estimated worst slack/total negative slack&lt;setup&gt;: 1.227ns/0.000ns; real time: 17 secs 
Level 4, iteration 7
0(0.00%) conflict; 0(0.00%) untouched conn; 0 (nbr) score; 
Estimated worst slack/total negative slack&lt;setup&gt;: 1.227ns/0.000ns; real time: 17 secs 

Start NBR section for setup/hold timing optimization with effort level 3 at 10:29:59 09/25/25

Start NBR section for re-routing at 10:30:00 09/25/25
Level 4, iteration 1
0(0.00%) conflict; 0(0.00%) untouched conn; 0 (nbr) score; 
Estimated worst slack/total negative slack&lt;setup&gt;: 1.227ns/0.000ns; real time: 18 secs 

Start NBR section for post-routing at 10:30:00 09/25/25

End NBR router with 0 unrouted connection

NBR Summary
-----------
  Number of unrouted connections : 0 (0.00%)
  Number of connections with timing violations : 0 (0.00%)
  Estimated worst slack&lt;setup&gt; : 1.227ns
  Timing score&lt;setup&gt; : 0
-----------
Notes: The timing info is calculated for SETUP only and all PAR_ADJs are ignored.



Total CPU time 21 secs 
Total REAL time: 22 secs 
Completely routed.
End of route.  4201 routed (100.00%); 0 unrouted.

Hold time timing score: 0, hold timing errors: 0

Timing score: 0 

Dumping design to file INS350_5J_JZ_impl1.dir/5_1.ncd.


All signals are completely routed.


PAR_SUMMARY::Run status = Completed
PAR_SUMMARY::Number of unrouted conns = 0
PAR_SUMMARY::Worst  slack&lt;setup/&lt;ns&gt;&gt; = 1.227
PAR_SUMMARY::Timing score&lt;setup/&lt;ns&gt;&gt; = 0.000
PAR_SUMMARY::Worst  slack&lt;hold /&lt;ns&gt;&gt; = 0.174
PAR_SUMMARY::Timing score&lt;hold /&lt;ns&gt;&gt; = 0.000
PAR_SUMMARY::Number of errors = 0

Total CPU  time to completion: 21 secs 
Total REAL time to completion: 22 secs 

par done!

Note: user must run &apos;Trace&apos; for timing closure signoff.

Copyright (c) 1991-1994 by NeoCAD Inc. All rights reserved.
Copyright (c) 1995 AT&amp;T Corp.   All rights reserved.
Copyright (c) 1995-2001 Lucent Technologies Inc.  All rights reserved.
Copyright (c) 2001 Agere Systems   All rights reserved.
Copyright (c) 2002-2020 Lattice Semiconductor Corporation,  All rights reserved.



<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
</PRE></FONT>
</BODY>
</HTML>
