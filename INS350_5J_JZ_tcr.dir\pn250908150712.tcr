#Start recording tcl command: 9/4/2025 16:27:39
#Project Location: D:/Project/TLH50_03J_JZ; Project name: INS350_5J_J<PERSON>
prj_project open "D:/Project/TLH50_03J_JZ/INS350_5J_JZ.ldf"
prj_run Export -impl impl1
pgr_project open "D:/Project/TLH50_03J_JZ/impl1/impl1.xcf"
pgr_program run
pgr_program run
pgr_program run
pgr_program run
pgr_program run
pgr_program run
pgr_program run
pgr_project save "D:/Project/TLH50_03J_JZ/impl1/impl1.xcf"
prj_run Export -impl impl1
pgr_program run
pgr_program run
pgr_program run
pgr_project save "D:/Project/TLH50_03J_JZ/impl1/impl1.xcf"
pgr_program run
pgr_program run
pgr_program set -cable 
pgr_program run
pgr_program run
pgr_program run
pgr_program run
pgr_program set -cable 
pgr_program run
pgr_program run
pgr_program run
pgr_program run
pgr_program run
pgr_program run
pgr_program run
pgr_program run
pgr_program run
pgr_program run
pgr_program run
pgr_program run
pgr_project save "D:/Project/TLH50_03J_JZ/impl1/impl1.xcf"
pgr_program run
pgr_program run
prj_run Export -impl impl1
pgr_program run
pgr_program set -cable 
pgr_program run
pgr_program run
pgr_program set -cable 
pgr_program run
pgr_program run
prj_run Export -impl impl1
pgr_program run
prj_run Export -impl impl1
pgr_program run
pgr_program run
prj_run Export -impl impl1
pgr_program run
prj_run Export -impl impl1
prj_run Export -impl impl1
pgr_program run
pgr_program run
pgr_project save "D:/Project/TLH50_03J_JZ/impl1/impl1.xcf"
prj_project close
#Stop recording: 9/8/2025 15:07:12
