<HTML>
<HEAD><TITLE>La<PERSON>ce TCL Log</TITLE>
<STYLE TYPE="text/css">
<!--
 body,pre{
    font-family:'Courier New', monospace;
    color: #000000;
    font-size:88%;
    background-color: #ffffff;
}
h1 {
    font-weight: bold;
    margin-top: 24px;
    margin-bottom: 10px;
    border-bottom: 3px solid #000;    font-size: 1em;
}
h2 {
    font-weight: bold;
    margin-top: 18px;
    margin-bottom: 5px;
    font-size: 0.90em;
}
h3 {
    font-weight: bold;
    margin-top: 12px;
    margin-bottom: 5px;
    font-size: 0.80em;
}
p {
    font-size:78%;
}
P.Table {
    margin-top: 4px;
    margin-bottom: 4px;
    margin-right: 4px;
    margin-left: 4px;
}
table
{
    border-width: 1px 1px 1px 1px;
    border-style: solid solid solid solid;
    border-color: black black black black;
    border-collapse: collapse;
}
th {
    font-weight:bold;
    padding: 4px;
    border-width: 1px 1px 1px 1px;
    border-style: solid solid solid solid;
    border-color: black black black black;
    vertical-align:top;
    text-align:left;
    font-size:78%;
}
td {
    padding: 4px;
    border-width: 1px 1px 1px 1px;
    border-style: solid solid solid solid;
    border-color: black black black black;
    vertical-align:top;
    font-size:78%;
}
a {
    color:#013C9A;
    text-decoration:none;
}

a:visited {
    color:#013C9A;
}

a:hover, a:active {
    text-decoration:underline;
    color:#5BAFD4;
}
.pass
{
background-color: #00ff00;
}
.fail
{
background-color: #ff0000;
}
.comment
{
    font-size: 90%;
    font-style: italic;
}

-->
</STYLE>
</HEAD>
<PRE><A name="pn250715145303"></A><B><U><big>pn250715145303</big></U></B>
#Start recording tcl command: 7/15/2025 14:22:06
#Project Location: D:/Project/INS350_5J_JZ _V2_copy2; Project name: INS350_5J_JZ
prj_project open "D:/Project/INS350_5J_JZ _V2_copy2/INS350_5J_JZ.ldf"
prj_run Export -impl impl1
prj_run Export -impl impl1
prj_run Export -impl impl1 -task Bitgen
prj_run Export -impl impl1 -task Bitgen
prj_run Export -impl impl1 -forceAll
prj_project close
#Stop recording: 7/15/2025 14:53:03



<A name="pn250715170700"></A><B><U><big>pn250715170700</big></U></B>
#Start recording tcl command: 7/15/2025 16:43:07
#Project Location: D:/Project/INS350_5J_JZ _V2_copy2; Project name: INS350_5J_JZ
prj_project open "D:/Project/INS350_5J_JZ _V2_copy2/INS350_5J_JZ.ldf"
#Stop recording: 7/15/2025 17:07:00



<A name="pn250723135633"></A><B><U><big>pn250723135633</big></U></B>
#Start recording tcl command: 7/23/2025 13:56:24
#Project Location: D:/Project/INS350_5J_JZ _V2; Project name: INS350_5J_JZ
prj_project open "D:/Project/INS350_5J_JZ _V2/INS350_5J_JZ.ldf"
#Stop recording: 7/23/2025 13:56:33



<A name="pn250724195551"></A><B><U><big>pn250724195551</big></U></B>
#Start recording tcl command: 7/24/2025 11:23:28
#Project Location: D:/Project/INS350_5J_JZ _V2; Project name: INS350_5J_JZ
prj_project open "D:/Project/INS350_5J_JZ _V2/INS350_5J_JZ.ldf"
#Stop recording: 7/24/2025 19:55:51



<A name="pn250728122232"></A><B><U><big>pn250728122232</big></U></B>
#Start recording tcl command: 7/28/2025 08:47:56
#Project Location: D:/Project/INS350_5J_JZ _V2; Project name: INS350_5J_JZ
prj_project open "D:/Project/INS350_5J_JZ _V2/INS350_5J_JZ.ldf"
#Stop recording: 7/28/2025 12:22:32



<A name="pn250729170700"></A><B><U><big>pn250729170700</big></U></B>
#Start recording tcl command: 7/29/2025 16:47:21
#Project Location: D:/Project/INS350_5J_JZ _V2; Project name: INS350_5J_JZ
prj_project open "D:/Project/INS350_5J_JZ _V2/INS350_5J_JZ.ldf"
#Stop recording: 7/29/2025 17:07:00



<A name="pn250901100527"></A><B><U><big>pn250901100527</big></U></B>
#Start recording tcl command: 9/1/2025 10:05:13
#Project Location: D:/Project/INS350_5J_JZ _V2; Project name: INS350_5J_JZ
prj_project open "D:/Project/INS350_5J_JZ _V2/INS350_5J_JZ.ldf"
#Stop recording: 9/1/2025 10:05:27



<A name="pn250908150712"></A><B><U><big>pn250908150712</big></U></B>
#Start recording tcl command: 9/4/2025 16:27:39
#Project Location: D:/Project/TLH50_03J_JZ; Project name: INS350_5J_JZ
prj_project open "D:/Project/TLH50_03J_JZ/INS350_5J_JZ.ldf"
prj_run Export -impl impl1
pgr_project open "D:/Project/TLH50_03J_JZ/impl1/impl1.xcf"
pgr_program run
pgr_program run
pgr_program run
pgr_program run
pgr_program run
pgr_program run
pgr_program run
pgr_project save "D:/Project/TLH50_03J_JZ/impl1/impl1.xcf"
prj_run Export -impl impl1
pgr_program run
pgr_program run
pgr_program run
pgr_project save "D:/Project/TLH50_03J_JZ/impl1/impl1.xcf"
pgr_program run
pgr_program run
pgr_program set -cable 
pgr_program run
pgr_program run
pgr_program run
pgr_program run
pgr_program set -cable 
pgr_program run
pgr_program run
pgr_program run
pgr_program run
pgr_program run
pgr_program run
pgr_program run
pgr_program run
pgr_program run
pgr_program run
pgr_program run
pgr_program run
pgr_project save "D:/Project/TLH50_03J_JZ/impl1/impl1.xcf"
pgr_program run
pgr_program run
prj_run Export -impl impl1
pgr_program run
pgr_program set -cable 
pgr_program run
pgr_program run
pgr_program set -cable 
pgr_program run
pgr_program run
prj_run Export -impl impl1
pgr_program run
prj_run Export -impl impl1
pgr_program run
pgr_program run
prj_run Export -impl impl1
pgr_program run
prj_run Export -impl impl1
prj_run Export -impl impl1
pgr_program run
pgr_program run
pgr_project save "D:/Project/TLH50_03J_JZ/impl1/impl1.xcf"
prj_project close
#Stop recording: 9/8/2025 15:07:12



<A name="pn250918171614"></A><B><U><big>pn250918171614</big></U></B>
#Start recording tcl command: 9/18/2025 11:05:33
#Project Location: D:/Project/TLH50_03J_JZ; Project name: INS350_5J_JZ
prj_project open "D:/Project/TLH50_03J_JZ/INS350_5J_JZ.ldf"
prj_run Export -impl impl1
pgr_project open "D:/Project/TLH50_03J_JZ/impl1/impl1.xcf"
pgr_program run
prj_run Export -impl impl1
pgr_program run
pgr_program run
pgr_program run
pgr_program run
pgr_program run
pgr_project save "D:/Project/TLH50_03J_JZ/impl1/impl1.xcf"
prj_project close
#Stop recording: 9/18/2025 17:16:14



<A name="pn250922170310"></A><B><U><big>pn250922170310</big></U></B>
#Start recording tcl command: 9/22/2025 15:34:45
#Project Location: D:/Project/TLH50_03J_JZ; Project name: INS350_5J_JZ
prj_project open "D:/Project/TLH50_03J_JZ/INS350_5J_JZ.ldf"
#Stop recording: 9/22/2025 17:03:10



<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
</PRE></FONT>
</BODY>
</HTML>
