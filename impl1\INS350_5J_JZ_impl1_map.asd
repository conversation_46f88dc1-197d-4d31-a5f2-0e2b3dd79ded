[ActiveSupport MAP]
Device = LFE5U-25F;
Package = CABGA256;
Performance = 7;
LUTS_avail = 24288;
LUTS_used = 775;
FF_avail = 24485;
FF_used = 1044;
INPUT_LVCMOS33 = 13;
OUTPUT_LVCMOS33 = 18;
BIDI_LVCMOS33 = 1;
IO_avail = 197;
IO_used = 32;
EBR_avail = 56;
EBR_used = 2;
;
; start of DSP statistics
MULT18X18D = 4;
MULT9X9D = 0;
ALU54B = 1;
ALU24B = 0;
PRADD18A = 0;
PRADD9A = 0;
DSP_MULT_avail = 56;
DSP_MULT_used = 8;
DSP_ALU_avail = 28;
DSP_ALU_used = 2;
DSP_PRADD_avail = 56;
DSP_PRADD_used = 0;
; end of DSP statistics
;
; Begin EBR Section
Instance_Name = signal_process/demodu/fifo/pdp_ram_0_1_0;
Type = PDPW16KD;
Width = 19;
Depth_R = 128;
Depth_W = 128;
REGMODE = NOREG;
RESETMODE = ASYNC;
ASYNC_RESET_RELEASE = SYNC;
GSR = DISABLED;
MEM_LPC_FILE = Asys_fifo56X16.lpc;
Instance_Name = signal_process/demodu/fifo/pdp_ram_0_0_1;
Type = PDPW16KD;
Width = 36;
Depth_R = 128;
Depth_W = 128;
REGMODE = NOREG;
RESETMODE = ASYNC;
ASYNC_RESET_RELEASE = SYNC;
GSR = DISABLED;
MEM_LPC_FILE = Asys_fifo56X16.lpc;
; End EBR Section
; Begin PLL Section
Instance_Name = CLK120/PLLInst_0;
Type = EHXPLLL;
CLKOP_Post_Divider_A_Input = DIVA;
CLKOS_Post_Divider_B_Input = DIVB;
CLKOS2_Post_Divider_C_Input = DIVC;
CLKOS3_Post_Divider_D_Input = DIVD;
FB_MODE = CLKOP;
CLKI_Divider = 1;
CLKFB_Divider = 6;
CLKOP_Divider = 5;
CLKOS_Divider = 10;
CLKOS2_Divider = 10;
CLKOS3_Divider = 1;
CLKOP_Desired_Phase_Shift(degree) = 0;
CLKOP_Trim_Option_Rising/Falling = FALLING;
CLKOP_Trim_Option_Delay = 0;
CLKOS_Desired_Phase_Shift(degree) = 0;
CLKOS_Trim_Option_Rising/Falling = FALLING;
CLKOS_Trim_Option_Delay = 0;
CLKOS2_Desired_Phase_Shift(degree) = 0;
CLKOS3_Desired_Phase_Shift(degree) = 0;
; End PLL Section
