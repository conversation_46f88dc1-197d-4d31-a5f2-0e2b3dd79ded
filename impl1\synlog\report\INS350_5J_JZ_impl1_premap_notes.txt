@N: MF916 |Option synthesis_strategy=base is enabled. 
@N: MF248 |Running in 64-bit mode.
@N: MF666 |Clock conversion enabled. (Command "set_option -fix_gated_and_generated_clocks 1" in the project file.)
@N: MO111 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\uart_control.v":31:16:31:22|Tristate driver rx_data_1 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) on net rx_data_1 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) has its enable tied to GND.
@N: MO111 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\uart_control.v":31:16:31:22|Tristate driver rx_data_2 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) on net rx_data_2 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) has its enable tied to GND.
@N: MO111 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\uart_control.v":31:16:31:22|Tristate driver rx_data_3 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) on net rx_data_3 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) has its enable tied to GND.
@N: MO111 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\uart_control.v":31:16:31:22|Tristate driver rx_data_4 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) on net rx_data_4 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) has its enable tied to GND.
@N: MO111 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\uart_control.v":31:16:31:22|Tristate driver rx_data_5 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) on net rx_data_5 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) has its enable tied to GND.
@N: MO111 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\uart_control.v":31:16:31:22|Tristate driver rx_data_6 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) on net rx_data_6 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) has its enable tied to GND.
@N: MO111 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\uart_control.v":31:16:31:22|Tristate driver rx_data_7 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) on net rx_data_7 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) has its enable tied to GND.
@N: MO111 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\uart_control.v":31:16:31:22|Tristate driver rx_data_8 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) on net rx_data_8 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) has its enable tied to GND.
@N: BN362 :"d:\project\tlh50_03j_jz_20250925\src_al\src_al\ds18b20.v":157:0:157:5|Removing sequential instance rd_done (in view: work.DS18B20(verilog)) of type view:PrimLib.dffre(prim) because it does not drive other instances.
@N: BN362 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":350:12:350:15|Removing sequential instance FF_1 (in view: work.Asys_fifo56X16(verilog)) of type view:LUCENT.FD1S3BX(PRIM) because it does not drive other instances.
@N: BN362 :"d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v":353:12:353:15|Removing sequential instance FF_0 (in view: work.Asys_fifo56X16(verilog)) of type view:LUCENT.FD1S3DX(PRIM) because it does not drive other instances.
@N: MF578 |Incompatible asynchronous control logic preventing generated clock conversion.
@N: FX1184 |Applying syn_allowed_resources blockrams=56 on top level netlist INS350_5J_JZ 
@N: FX1143 |Skipping assigning INTERNAL_VREF to iobanks, because the table of mapping from pin to iobank is not initialized.
