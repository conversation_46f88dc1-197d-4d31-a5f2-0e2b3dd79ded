[ActiveSupport PAR]
; Global primary clocks
GLOBAL_PRIMARY_USED = 12;
; Global primary clock #0
GLOBAL_PRIMARY_0_SIGNALNAME = clk120mhz;
GLOBAL_PRIMARY_0_DRIVERTYPE = PLL;
GLOBAL_PRIMARY_0_LOADNUM = 446;
; Global primary clock #1
GLOBAL_PRIMARY_1_SIGNALNAME = clk_AD;
GLOBAL_PRIMARY_1_DRIVERTYPE = PLL;
GLOBAL_PRIMARY_1_LOADNUM = 101;
; Global primary clock #2
GLOBAL_PRIMARY_2_SIGNALNAME = wendu.clk_us;
GLOBAL_PRIMARY_2_DRIVERTYPE = SLICE;
GLOBAL_PRIMARY_2_LOADNUM = 36;
; Global primary clock #3
GLOBAL_PRIMARY_3_SIGNALNAME = signal_process/demodu/AD_validcnt7;
GLOBAL_PRIMARY_3_DRIVERTYPE = SLICE;
GLOBAL_PRIMARY_3_LOADNUM = 0;
; Global primary clock #4
GLOBAL_PRIMARY_4_SIGNALNAME = signal_process/rs422/trans_state[5];
GLOBAL_PRIMARY_4_DRIVERTYPE = SLICE;
GLOBAL_PRIMARY_4_LOADNUM = 0;
; Global primary clock #5
GLOBAL_PRIMARY_5_SIGNALNAME = signal_process/rs422/N_80_i;
GLOBAL_PRIMARY_5_DRIVERTYPE = SLICE;
GLOBAL_PRIMARY_5_LOADNUM = 0;
; Global primary clock #6
GLOBAL_PRIMARY_6_SIGNALNAME = signal_process/demodu/N_410_i;
GLOBAL_PRIMARY_6_DRIVERTYPE = SLICE;
GLOBAL_PRIMARY_6_LOADNUM = 0;
; Global primary clock #7
GLOBAL_PRIMARY_7_SIGNALNAME = signal_process/polarity;
GLOBAL_PRIMARY_7_DRIVERTYPE = SLICE;
GLOBAL_PRIMARY_7_LOADNUM = 0;
; Global primary clock #8
GLOBAL_PRIMARY_8_SIGNALNAME = signal_process/integ/DA_dout5;
GLOBAL_PRIMARY_8_DRIVERTYPE = SLICE;
GLOBAL_PRIMARY_8_LOADNUM = 0;
; Global primary clock #9
GLOBAL_PRIMARY_9_SIGNALNAME = signal_process/demodu/median_sum_n_1_sqmuxa;
GLOBAL_PRIMARY_9_DRIVERTYPE = SLICE;
GLOBAL_PRIMARY_9_LOADNUM = 0;
; Global primary clock #10
GLOBAL_PRIMARY_10_SIGNALNAME = signal_process/demodu/Latch_sum;
GLOBAL_PRIMARY_10_DRIVERTYPE = SLICE;
GLOBAL_PRIMARY_10_LOADNUM = 0;
; Global primary clock #11
GLOBAL_PRIMARY_11_SIGNALNAME = signal_process/demodu/N_417_i;
GLOBAL_PRIMARY_11_DRIVERTYPE = SLICE;
GLOBAL_PRIMARY_11_LOADNUM = 0;
; # of global secondary clocks
GLOBAL_SECONDARY_USED = 0;
; I/O Bank 0 Usage
BANK_0_USED = 0;
BANK_0_AVAIL = 24;
BANK_0_VCCIO = 3.3V;
BANK_0_VREF1 = NA;
BANK_0_VREF2 = NA;
; I/O Bank 1 Usage
BANK_1_USED = 1;
BANK_1_AVAIL = 32;
BANK_1_VCCIO = 3.3V;
BANK_1_VREF1 = NA;
BANK_1_VREF2 = NA;
; I/O Bank 2 Usage
BANK_2_USED = 0;
BANK_2_AVAIL = 32;
BANK_2_VCCIO = 3.3V;
BANK_2_VREF1 = NA;
BANK_2_VREF2 = NA;
; I/O Bank 3 Usage
BANK_3_USED = 3;
BANK_3_AVAIL = 32;
BANK_3_VCCIO = 3.3V;
BANK_3_VREF1 = NA;
BANK_3_VREF2 = NA;
; I/O Bank 6 Usage
BANK_6_USED = 13;
BANK_6_AVAIL = 32;
BANK_6_VCCIO = 3.3V;
BANK_6_VREF1 = NA;
BANK_6_VREF2 = NA;
; I/O Bank 7 Usage
BANK_7_USED = 15;
BANK_7_AVAIL = 32;
BANK_7_VCCIO = 3.3V;
BANK_7_VREF1 = NA;
BANK_7_VREF2 = NA;
; I/O Bank 8 Usage
BANK_8_USED = 0;
BANK_8_AVAIL = 13;
BANK_8_VCCIO = 3.3V;
BANK_8_VREF1 = NA;
BANK_8_VREF2 = NA;
