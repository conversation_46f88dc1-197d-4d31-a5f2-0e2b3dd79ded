[ START MERGED ]
locked_out_i CLK120/locked_out
signal_process/demodu/fifo/rden_i_inv signal_process/demodu/fifo/rden_i
signal_process/demodu/fifo/invout_2 signal_process/demodu/fifo/Full
signal_process/demodu/fifo/invout_1 signal_process/demodu/fifo/empty_flag
[ END MERGED ]
[ START CLIPPED ]
GND
wendu/VCC
u_uart/U0/VCC
signal_process/ctrl_signal/VCC
signal_process/demodu/VCC
signal_process/demodu/fifo/VCC
signal_process/integ/VCC
signal_process/modu/GND
signal_process/modu/VCC
signal_process/rs422/GND
signal_process/rs422/VCC
signal_process/trans/VCC
VCC
CLK120/CLKINTFB
CLK120/REFCLK
CLK120/INTLOCK
CLK120/CLKOS3
wendu/un1_cnt_us_18_cry_0_0_S0
wendu/N_8
wendu/un4_cnt_s_7_0_S1
wendu/un4_cnt_s_7_0_COUT
wendu/un4_cnt_cry_0_0_S1
wendu/un4_cnt_cry_0_0_S0
wendu/N_9
wendu/un1_cnt_us_18_s_19_0_S1
wendu/un1_cnt_us_18_s_19_0_COUT
u_uart/U0/cnt_cry_0_COUT[11]
u_uart/U0/cnt_cry_0_S0[0]
u_uart/U0/N_1
signal_process/ctrl_signal/un6_count_s_15_0_S1
signal_process/ctrl_signal/un6_count_s_15_0_COUT
signal_process/ctrl_signal/un6_count_cry_0_0_S1_0
signal_process/ctrl_signal/un6_count_cry_0_0_S0_0
signal_process/ctrl_signal/N_1
signal_process/demodu/un1_AD_validcnt_1_s_7_0_S1
signal_process/demodu/un1_AD_validcnt_1_s_7_0_COUT
signal_process/demodu/un1_AD_validcnt_1_cry_0_0_S0
signal_process/demodu/N_1
signal_process/demodu/un3_sample_sum_s_55_0_S1
signal_process/demodu/un3_sample_sum_s_55_0_COUT
signal_process/demodu/un3_sample_sum_cry_0_0_S1
signal_process/demodu/un3_sample_sum_cry_0_0_S0
signal_process/demodu/N_2
signal_process/demodu/INS_dout_1_cry_53_0_COUT
signal_process/demodu/INS_dout_1_cry_0_0_S1
signal_process/demodu/INS_dout_1_cry_0_0_S0
signal_process/demodu/N_3
signal_process/demodu/fifo/co3_2
signal_process/demodu/fifo/r_ctr_cia_S1
signal_process/demodu/fifo/r_ctr_cia_S0
signal_process/demodu/fifo/CIN
signal_process/demodu/fifo/co3_1
signal_process/demodu/fifo/w_ctr_cia_S1
signal_process/demodu/fifo/w_ctr_cia_S0
signal_process/demodu/fifo/CIN_0
signal_process/demodu/fifo/a1_S1
signal_process/demodu/fifo/a1_COUT
signal_process/demodu/fifo/g_cmp_3_S1
signal_process/demodu/fifo/g_cmp_3_S0
signal_process/demodu/fifo/g_cmp_2_S1
signal_process/demodu/fifo/g_cmp_2_S0
signal_process/demodu/fifo/g_cmp_1_S1
signal_process/demodu/fifo/g_cmp_1_S0
signal_process/demodu/fifo/g_cmp_0_S1
signal_process/demodu/fifo/g_cmp_0_S0
signal_process/demodu/fifo/g_cmp_ci_a_S1
signal_process/demodu/fifo/g_cmp_ci_a_S0
signal_process/demodu/fifo/CIN_1
signal_process/demodu/fifo/a0_S1
signal_process/demodu/fifo/a0_COUT
signal_process/demodu/fifo/e_cmp_3_S1
signal_process/demodu/fifo/e_cmp_3_S0
signal_process/demodu/fifo/e_cmp_2_S1
signal_process/demodu/fifo/e_cmp_2_S0
signal_process/demodu/fifo/e_cmp_1_S1
signal_process/demodu/fifo/e_cmp_1_S0
signal_process/demodu/fifo/e_cmp_0_S1
signal_process/demodu/fifo/e_cmp_0_S0
signal_process/demodu/fifo/e_cmp_ci_a_S1
signal_process/demodu/fifo/e_cmp_ci_a_S0
signal_process/demodu/fifo/CIN_2
signal_process/demodu/fifo/co3
signal_process/demodu/fifo/bdcnt_bctr_cia_S1
signal_process/demodu/fifo/bdcnt_bctr_cia_S0
signal_process/demodu/fifo/CIN_3
signal_process/demodu/fifo/pdp_ram_0_1_0_DO17
signal_process/demodu/fifo/pdp_ram_0_1_0_DO16
signal_process/demodu/fifo/pdp_ram_0_1_0_DO15
signal_process/demodu/fifo/pdp_ram_0_1_0_DO14
signal_process/demodu/fifo/pdp_ram_0_1_0_DO13
signal_process/demodu/fifo/pdp_ram_0_1_0_DO12
signal_process/demodu/fifo/pdp_ram_0_1_0_DO11
signal_process/demodu/fifo/pdp_ram_0_1_0_DO10
signal_process/demodu/fifo/pdp_ram_0_1_0_DO9
signal_process/demodu/fifo/pdp_ram_0_1_0_DO8
signal_process/demodu/fifo/pdp_ram_0_1_0_DO7
signal_process/demodu/fifo/pdp_ram_0_1_0_DO6
signal_process/demodu/fifo/pdp_ram_0_1_0_DO5
signal_process/demodu/fifo/pdp_ram_0_1_0_DO4
signal_process/demodu/fifo/pdp_ram_0_1_0_DO3
signal_process/demodu/fifo/pdp_ram_0_1_0_DO2
signal_process/demodu/fifo/sample_sum_DY[55]
signal_process/integ/DA_dout_1_cry_53_0_COUT
signal_process/integ/DA_dout_1_cry_0_0_S1
signal_process/integ/DA_dout_1_cry_0_0_S0
signal_process/integ/N_1
signal_process/modu/stair_1_s_23_0_S1
signal_process/modu/stair_1_s_23_0_COUT
signal_process/modu/stair_1_cry_0_0_S1
signal_process/modu/stair_1_cry_0_0_S0
signal_process/modu/N_1
signal_process/modu/dout_reg_1_s_13_0_S1
signal_process/modu/dout_reg_1_s_13_0_COUT
signal_process/modu/dout_reg_1_cry_0_0_S1
signal_process/modu/dout_reg_1_cry_0_0_S0
signal_process/modu/N_2
signal_process/modu/un1_dout_mult_ROC0
signal_process/modu/un1_dout_mult_ROC1
signal_process/modu/un1_dout_mult_ROC2
signal_process/modu/un1_dout_mult_ROC3
signal_process/modu/un1_dout_mult_ROC4
signal_process/modu/un1_dout_mult_ROC5
signal_process/modu/un1_dout_mult_ROC6
signal_process/modu/un1_dout_mult_ROC7
signal_process/modu/un1_dout_mult_ROC8
signal_process/modu/un1_dout_mult_ROC9
signal_process/modu/un1_dout_mult_ROC10
signal_process/modu/un1_dout_mult_ROC11
signal_process/modu/un1_dout_mult_ROC12
signal_process/modu/un1_dout_mult_ROC13
signal_process/modu/un1_dout_mult_ROC14
signal_process/modu/un1_dout_mult_ROC15
signal_process/modu/un1_dout_mult_ROC16
signal_process/modu/un1_dout_mult_ROC17
signal_process/modu/un1_dout_mult_ROB0
signal_process/modu/un1_dout_mult_ROB1
signal_process/modu/un1_dout_mult_ROB2
signal_process/modu/un1_dout_mult_ROB3
signal_process/modu/un1_dout_mult_ROB4
signal_process/modu/un1_dout_mult_ROB5
signal_process/modu/un1_dout_mult_ROB6
signal_process/modu/un1_dout_mult_ROB7
signal_process/modu/un1_dout_mult_ROB8
signal_process/modu/un1_dout_mult_ROB9
signal_process/modu/un1_dout_mult_ROB10
signal_process/modu/un1_dout_mult_ROB11
signal_process/modu/un1_dout_mult_ROB12
signal_process/modu/un1_dout_mult_ROB13
signal_process/modu/un1_dout_mult_ROB14
signal_process/modu/un1_dout_mult_ROB15
signal_process/modu/un1_dout_mult_ROB16
signal_process/modu/un1_dout_mult_ROB17
signal_process/modu/un1_dout_mult_ROA0
signal_process/modu/un1_dout_mult_ROA1
signal_process/modu/un1_dout_mult_ROA2
signal_process/modu/un1_dout_mult_ROA3
signal_process/modu/un1_dout_mult_ROA4
signal_process/modu/un1_dout_mult_ROA5
signal_process/modu/un1_dout_mult_ROA6
signal_process/modu/un1_dout_mult_ROA7
signal_process/modu/un1_dout_mult_ROA8
signal_process/modu/un1_dout_mult_ROA9
signal_process/modu/un1_dout_mult_ROA10
signal_process/modu/un1_dout_mult_ROA11
signal_process/modu/un1_dout_mult_ROA12
signal_process/modu/un1_dout_mult_ROA13
signal_process/modu/un1_dout_mult_ROA14
signal_process/modu/un1_dout_mult_ROA15
signal_process/modu/un1_dout_mult_ROA16
signal_process/modu/un1_dout_mult_ROA17
signal_process/modu/un1_dout_mult_SROB0
signal_process/modu/un1_dout_mult_SROB1
signal_process/modu/un1_dout_mult_SROB2
signal_process/modu/un1_dout_mult_SROB3
signal_process/modu/un1_dout_mult_SROB4
signal_process/modu/un1_dout_mult_SROB5
signal_process/modu/un1_dout_mult_SROB6
signal_process/modu/un1_dout_mult_SROB7
signal_process/modu/un1_dout_mult_SROB8
signal_process/modu/un1_dout_mult_SROB9
signal_process/modu/un1_dout_mult_SROB10
signal_process/modu/un1_dout_mult_SROB11
signal_process/modu/un1_dout_mult_SROB12
signal_process/modu/un1_dout_mult_SROB13
signal_process/modu/un1_dout_mult_SROB14
signal_process/modu/un1_dout_mult_SROB15
signal_process/modu/un1_dout_mult_SROB16
signal_process/modu/un1_dout_mult_SROB17
signal_process/modu/un1_dout_mult_SROA0
signal_process/modu/un1_dout_mult_SROA1
signal_process/modu/un1_dout_mult_SROA2
signal_process/modu/un1_dout_mult_SROA3
signal_process/modu/un1_dout_mult_SROA4
signal_process/modu/un1_dout_mult_SROA5
signal_process/modu/un1_dout_mult_SROA6
signal_process/modu/un1_dout_mult_SROA7
signal_process/modu/un1_dout_mult_SROA8
signal_process/modu/un1_dout_mult_SROA9
signal_process/modu/un1_dout_mult_SROA10
signal_process/modu/un1_dout_mult_SROA11
signal_process/modu/un1_dout_mult_SROA12
signal_process/modu/un1_dout_mult_SROA13
signal_process/modu/un1_dout_mult_SROA14
signal_process/modu/un1_dout_mult_SROA15
signal_process/modu/un1_dout_mult_SROA16
signal_process/modu/un1_dout_mult_SROA17
signal_process/modu/un1_dout_mult_P0
signal_process/modu/un1_dout_mult_P1
signal_process/modu/un1_dout_mult_P2
signal_process/modu/un1_dout_mult_P3
signal_process/modu/un1_dout_mult_P4
signal_process/modu/un1_dout_mult_P5
signal_process/modu/un1_dout_mult_P6
signal_process/modu/un1_dout_mult_P7
signal_process/modu/un1_dout_mult_P8
signal_process/modu/un1_dout_mult_P9
signal_process/modu/un1_dout_mult_P10
signal_process/modu/un1_dout_mult_P11
signal_process/modu/un1_dout_mult_P12
signal_process/modu/un1_dout_mult_P27
signal_process/modu/un1_dout_mult_P28
signal_process/modu/un1_dout_mult_P29
signal_process/modu/un1_dout_mult_P30
signal_process/modu/un1_dout_mult_P31
signal_process/modu/un1_dout_mult_P32
signal_process/modu/un1_dout_mult_P33
signal_process/modu/un1_dout_mult_P34
signal_process/modu/un1_dout_mult_P35
signal_process/modu/un1_dout_mult_SIGNEDP
signal_process/rs422/un3_p_sum_ROC0_1
signal_process/rs422/un3_p_sum_ROC1_1
signal_process/rs422/un3_p_sum_ROC2_1
signal_process/rs422/un3_p_sum_ROC3_1
signal_process/rs422/un3_p_sum_ROC4_1
signal_process/rs422/un3_p_sum_ROC5_1
signal_process/rs422/un3_p_sum_ROC6_1
signal_process/rs422/un3_p_sum_ROC7_1
signal_process/rs422/un3_p_sum_ROC8_1
signal_process/rs422/un3_p_sum_ROC9_1
signal_process/rs422/un3_p_sum_ROC10_1
signal_process/rs422/un3_p_sum_ROC11_1
signal_process/rs422/un3_p_sum_ROC12_1
signal_process/rs422/un3_p_sum_ROC13_1
signal_process/rs422/un3_p_sum_ROC14_1
signal_process/rs422/un3_p_sum_ROC15_1
signal_process/rs422/un3_p_sum_ROC16_1
signal_process/rs422/un3_p_sum_ROC17_1
signal_process/rs422/un3_p_sum_ROB0_1
signal_process/rs422/un3_p_sum_ROB1_1
signal_process/rs422/un3_p_sum_ROB2_1
signal_process/rs422/un3_p_sum_ROB3_1
signal_process/rs422/un3_p_sum_ROB4_1
signal_process/rs422/un3_p_sum_ROB5_1
signal_process/rs422/un3_p_sum_ROB6_1
signal_process/rs422/un3_p_sum_ROB7_1
signal_process/rs422/un3_p_sum_ROB8_1
signal_process/rs422/un3_p_sum_ROB9_1
signal_process/rs422/un3_p_sum_ROB10_1
signal_process/rs422/un3_p_sum_ROB11_1
signal_process/rs422/un3_p_sum_ROB12_1
signal_process/rs422/un3_p_sum_ROB13_1
signal_process/rs422/un3_p_sum_ROB14_1
signal_process/rs422/un3_p_sum_ROB15_1
signal_process/rs422/un3_p_sum_ROB16_1
signal_process/rs422/un3_p_sum_ROB17_1
signal_process/rs422/un3_p_sum_ROA0_1
signal_process/rs422/un3_p_sum_ROA1_1
signal_process/rs422/un3_p_sum_ROA2_1
signal_process/rs422/un3_p_sum_ROA3_1
signal_process/rs422/un3_p_sum_ROA4_1
signal_process/rs422/un3_p_sum_ROA5_1
signal_process/rs422/un3_p_sum_ROA6_1
signal_process/rs422/un3_p_sum_ROA7_1
signal_process/rs422/un3_p_sum_ROA8_1
signal_process/rs422/un3_p_sum_ROA9_1
signal_process/rs422/un3_p_sum_ROA10_1
signal_process/rs422/un3_p_sum_ROA11_1
signal_process/rs422/un3_p_sum_ROA12_1
signal_process/rs422/un3_p_sum_ROA13_1
signal_process/rs422/un3_p_sum_ROA14_1
signal_process/rs422/un3_p_sum_ROA15_1
signal_process/rs422/un3_p_sum_ROA16_1
signal_process/rs422/un3_p_sum_ROA17_1
signal_process/rs422/un3_p_sum_SROB0_1
signal_process/rs422/un3_p_sum_SROB1_1
signal_process/rs422/un3_p_sum_SROB2_1
signal_process/rs422/un3_p_sum_SROB3_1
signal_process/rs422/un3_p_sum_SROB4_1
signal_process/rs422/un3_p_sum_SROB5_1
signal_process/rs422/un3_p_sum_SROB6_1
signal_process/rs422/un3_p_sum_SROB7_1
signal_process/rs422/un3_p_sum_SROB8_1
signal_process/rs422/un3_p_sum_SROB9_1
signal_process/rs422/un3_p_sum_SROB10_1
signal_process/rs422/un3_p_sum_SROB11_1
signal_process/rs422/un3_p_sum_SROB12_1
signal_process/rs422/un3_p_sum_SROB13_1
signal_process/rs422/un3_p_sum_SROB14_1
signal_process/rs422/un3_p_sum_SROB15_1
signal_process/rs422/un3_p_sum_SROB16_1
signal_process/rs422/un3_p_sum_SROB17_1
signal_process/rs422/un3_p_sum_SROA0_1
signal_process/rs422/un3_p_sum_SROA1_1
signal_process/rs422/un3_p_sum_SROA2_1
signal_process/rs422/un3_p_sum_SROA3_1
signal_process/rs422/un3_p_sum_SROA4_1
signal_process/rs422/un3_p_sum_SROA5_1
signal_process/rs422/un3_p_sum_SROA6_1
signal_process/rs422/un3_p_sum_SROA7_1
signal_process/rs422/un3_p_sum_SROA8_1
signal_process/rs422/un3_p_sum_SROA9_1
signal_process/rs422/un3_p_sum_SROA10_1
signal_process/rs422/un3_p_sum_SROA11_1
signal_process/rs422/un3_p_sum_SROA12_1
signal_process/rs422/un3_p_sum_SROA13_1
signal_process/rs422/un3_p_sum_SROA14_1
signal_process/rs422/un3_p_sum_SROA15_1
signal_process/rs422/un3_p_sum_SROA16_1
signal_process/rs422/un3_p_sum_SROA17_1
signal_process/rs422/un3_p_sum_2[19]
signal_process/rs422/un3_p_sum_2[20]
signal_process/rs422/un3_p_sum_2[21]
signal_process/rs422/un3_p_sum_2[22]
signal_process/rs422/un3_p_sum_2[23]
signal_process/rs422/un3_p_sum_2[24]
signal_process/rs422/un3_p_sum_2[25]
signal_process/rs422/un3_p_sum_2[26]
signal_process/rs422/un3_p_sum_2[27]
signal_process/rs422/un3_p_sum_2[28]
signal_process/rs422/un3_p_sum_2[29]
signal_process/rs422/un3_p_sum_2[30]
signal_process/rs422/un3_p_sum_2[31]
signal_process/rs422/un3_p_sum_2[32]
signal_process/rs422/un3_p_sum_2[33]
signal_process/rs422/un3_p_sum_2[34]
signal_process/rs422/un3_p_sum_2[35]
signal_process/rs422/un3_p_sum_SIGNEDP_1
signal_process/rs422/un3_p_sum_ROC0_0
signal_process/rs422/un3_p_sum_ROC1_0
signal_process/rs422/un3_p_sum_ROC2_0
signal_process/rs422/un3_p_sum_ROC3_0
signal_process/rs422/un3_p_sum_ROC4_0
signal_process/rs422/un3_p_sum_ROC5_0
signal_process/rs422/un3_p_sum_ROC6_0
signal_process/rs422/un3_p_sum_ROC7_0
signal_process/rs422/un3_p_sum_ROC8_0
signal_process/rs422/un3_p_sum_ROC9_0
signal_process/rs422/un3_p_sum_ROC10_0
signal_process/rs422/un3_p_sum_ROC11_0
signal_process/rs422/un3_p_sum_ROC12_0
signal_process/rs422/un3_p_sum_ROC13_0
signal_process/rs422/un3_p_sum_ROC14_0
signal_process/rs422/un3_p_sum_ROC15_0
signal_process/rs422/un3_p_sum_ROC16_0
signal_process/rs422/un3_p_sum_ROC17_0
signal_process/rs422/un3_p_sum_SROB0_0
signal_process/rs422/un3_p_sum_SROB1_0
signal_process/rs422/un3_p_sum_SROB2_0
signal_process/rs422/un3_p_sum_SROB3_0
signal_process/rs422/un3_p_sum_SROB4_0
signal_process/rs422/un3_p_sum_SROB5_0
signal_process/rs422/un3_p_sum_SROB6_0
signal_process/rs422/un3_p_sum_SROB7_0
signal_process/rs422/un3_p_sum_SROB8_0
signal_process/rs422/un3_p_sum_SROB9_0
signal_process/rs422/un3_p_sum_SROB10_0
signal_process/rs422/un3_p_sum_SROB11_0
signal_process/rs422/un3_p_sum_SROB12_0
signal_process/rs422/un3_p_sum_SROB13_0
signal_process/rs422/un3_p_sum_SROB14_0
signal_process/rs422/un3_p_sum_SROB15_0
signal_process/rs422/un3_p_sum_SROB16_0
signal_process/rs422/un3_p_sum_SROB17_0
signal_process/rs422/un3_p_sum_SROA0_0
signal_process/rs422/un3_p_sum_SROA1_0
signal_process/rs422/un3_p_sum_SROA2_0
signal_process/rs422/un3_p_sum_SROA3_0
signal_process/rs422/un3_p_sum_SROA4_0
signal_process/rs422/un3_p_sum_SROA5_0
signal_process/rs422/un3_p_sum_SROA6_0
signal_process/rs422/un3_p_sum_SROA7_0
signal_process/rs422/un3_p_sum_SROA8_0
signal_process/rs422/un3_p_sum_SROA9_0
signal_process/rs422/un3_p_sum_SROA10_0
signal_process/rs422/un3_p_sum_SROA11_0
signal_process/rs422/un3_p_sum_SROA12_0
signal_process/rs422/un3_p_sum_SROA13_0
signal_process/rs422/un3_p_sum_SROA14_0
signal_process/rs422/un3_p_sum_SROA15_0
signal_process/rs422/un3_p_sum_SROA16_0
signal_process/rs422/un3_p_sum_SROA17_0
signal_process/rs422/un3_p_sum_ROC0
signal_process/rs422/un3_p_sum_ROC1
signal_process/rs422/un3_p_sum_ROC2
signal_process/rs422/un3_p_sum_ROC3
signal_process/rs422/un3_p_sum_ROC4
signal_process/rs422/un3_p_sum_ROC5
signal_process/rs422/un3_p_sum_ROC6
signal_process/rs422/un3_p_sum_ROC7
signal_process/rs422/un3_p_sum_ROC8
signal_process/rs422/un3_p_sum_ROC9
signal_process/rs422/un3_p_sum_ROC10
signal_process/rs422/un3_p_sum_ROC11
signal_process/rs422/un3_p_sum_ROC12
signal_process/rs422/un3_p_sum_ROC13
signal_process/rs422/un3_p_sum_ROC14
signal_process/rs422/un3_p_sum_ROC15
signal_process/rs422/un3_p_sum_ROC16
signal_process/rs422/un3_p_sum_ROC17
signal_process/rs422/un3_p_sum_SROB0
signal_process/rs422/un3_p_sum_SROB1
signal_process/rs422/un3_p_sum_SROB2
signal_process/rs422/un3_p_sum_SROB3
signal_process/rs422/un3_p_sum_SROB4
signal_process/rs422/un3_p_sum_SROB5
signal_process/rs422/un3_p_sum_SROB6
signal_process/rs422/un3_p_sum_SROB7
signal_process/rs422/un3_p_sum_SROB8
signal_process/rs422/un3_p_sum_SROB9
signal_process/rs422/un3_p_sum_SROB10
signal_process/rs422/un3_p_sum_SROB11
signal_process/rs422/un3_p_sum_SROB12
signal_process/rs422/un3_p_sum_SROB13
signal_process/rs422/un3_p_sum_SROB14
signal_process/rs422/un3_p_sum_SROB15
signal_process/rs422/un3_p_sum_SROB16
signal_process/rs422/un3_p_sum_SROB17
signal_process/rs422/un3_p_sum_SROA0
signal_process/rs422/un3_p_sum_SROA1
signal_process/rs422/un3_p_sum_SROA2
signal_process/rs422/un3_p_sum_SROA3
signal_process/rs422/un3_p_sum_SROA4
signal_process/rs422/un3_p_sum_SROA5
signal_process/rs422/un3_p_sum_SROA6
signal_process/rs422/un3_p_sum_SROA7
signal_process/rs422/un3_p_sum_SROA8
signal_process/rs422/un3_p_sum_SROA9
signal_process/rs422/un3_p_sum_SROA10
signal_process/rs422/un3_p_sum_SROA11
signal_process/rs422/un3_p_sum_SROA12
signal_process/rs422/un3_p_sum_SROA13
signal_process/rs422/un3_p_sum_SROA14
signal_process/rs422/un3_p_sum_SROA15
signal_process/rs422/un3_p_sum_SROA16
signal_process/rs422/un3_p_sum_SROA17
signal_process/rs422/un3_p_sum_add_cry_17_0_COUT
signal_process/rs422/un3_p_sum_add_cry_0_0_S1
signal_process/rs422/un3_p_sum_add_cry_0_0_S0
signal_process/rs422/N_7
signal_process/rs422/un2_sum_cry_53_0_COUT
signal_process/rs422/un2_sum_cry_0_0_S1
signal_process/rs422/un2_sum_cry_0_0_S0
signal_process/rs422/N_8
signal_process/rs422/dout_1_s_31_0_S1
signal_process/rs422/dout_1_s_31_0_COUT
signal_process/rs422/dout_1_cry_0_0_S1
signal_process/rs422/dout_1_cry_0_0_S0
signal_process/rs422/N_9
signal_process/rs422/count_pos_s_0_S1[7]
signal_process/rs422/count_pos_s_0_COUT[7]
signal_process/rs422/count_pos_cry_0_S0[0]
signal_process/rs422/N_10
signal_process/rs422/un3_p_sum_add_OVERUNDER
signal_process/rs422/un3_p_sum_add_UNDER
signal_process/rs422/un3_p_sum_add_OVER
signal_process/rs422/un3_p_sum_add_EQPATB
signal_process/rs422/un3_p_sum_add_EQPAT
signal_process/rs422/un3_p_sum_add_EQOM
signal_process/rs422/un3_p_sum_add_EQZM
signal_process/rs422/un3_p_sum_add_EQZ
signal_process/rs422/un3_p_sum_add_CO0
signal_process/rs422/un3_p_sum_add_CO1
signal_process/rs422/un3_p_sum_add_CO2
signal_process/rs422/un3_p_sum_add_CO3
signal_process/rs422/un3_p_sum_add_CO4
signal_process/rs422/un3_p_sum_add_CO5
signal_process/rs422/un3_p_sum_add_CO6
signal_process/rs422/un3_p_sum_add_CO7
signal_process/rs422/un3_p_sum_add_CO8
signal_process/rs422/un3_p_sum_add_CO9
signal_process/rs422/un3_p_sum_add_CO10
signal_process/rs422/un3_p_sum_add_CO11
signal_process/rs422/un3_p_sum_add_CO12
signal_process/rs422/un3_p_sum_add_CO13
signal_process/rs422/un3_p_sum_add_CO14
signal_process/rs422/un3_p_sum_add_CO15
signal_process/rs422/un3_p_sum_add_CO16
signal_process/rs422/un3_p_sum_add_CO17
signal_process/rs422/un3_p_sum_add_CO18
signal_process/rs422/un3_p_sum_add_CO19
signal_process/rs422/un3_p_sum_add_CO20
signal_process/rs422/un3_p_sum_add_CO21
signal_process/rs422/un3_p_sum_add_CO22
signal_process/rs422/un3_p_sum_add_CO23
signal_process/rs422/un3_p_sum_add_CO24
signal_process/rs422/un3_p_sum_add_CO25
signal_process/rs422/un3_p_sum_add_CO26
signal_process/rs422/un3_p_sum_add_CO27
signal_process/rs422/un3_p_sum_add_CO28
signal_process/rs422/un3_p_sum_add_CO29
signal_process/rs422/un3_p_sum_add_CO30
signal_process/rs422/un3_p_sum_add_CO31
signal_process/rs422/un3_p_sum_add_CO32
signal_process/rs422/un3_p_sum_add_CO33
signal_process/rs422/un3_p_sum_add_CO34
signal_process/rs422/un3_p_sum_add_CO35
signal_process/rs422/un3_p_sum_add_CO36
signal_process/rs422/un3_p_sum_add_CO37
signal_process/rs422/un3_p_sum_add_CO38
signal_process/rs422/un3_p_sum_add_CO39
signal_process/rs422/un3_p_sum_add_CO40
signal_process/rs422/un3_p_sum_add_CO41
signal_process/rs422/un3_p_sum_add_CO42
signal_process/rs422/un3_p_sum_add_CO43
signal_process/rs422/un3_p_sum_add_CO44
signal_process/rs422/un3_p_sum_add_CO45
signal_process/rs422/un3_p_sum_add_CO46
signal_process/rs422/un3_p_sum_add_CO47
signal_process/rs422/un3_p_sum_add_CO48
signal_process/rs422/un3_p_sum_add_CO49
signal_process/rs422/un3_p_sum_add_CO50
signal_process/rs422/un3_p_sum_add_CO51
signal_process/rs422/un3_p_sum_add_CO52
signal_process/rs422/un3_p_sum_add_CO53
signal_process/rs422/un3_p_sum_add_R0
signal_process/rs422/un3_p_sum_add_R1
signal_process/rs422/un3_p_sum_add_R2
signal_process/rs422/un3_p_sum_add_R3
signal_process/rs422/un3_p_sum_add_R4
signal_process/rs422/un3_p_sum_add_R5
signal_process/rs422/un3_p_sum_add_R6
signal_process/rs422/un3_p_sum_add_R7
signal_process/rs422/un3_p_sum_add_R8
signal_process/rs422/un3_p_sum_add_R9
signal_process/rs422/un3_p_sum_add_R10
signal_process/rs422/un3_p_sum_add_R11
signal_process/rs422/un3_p_sum_add_R12
signal_process/rs422/un3_p_sum_add_R13
signal_process/rs422/un3_p_sum_add_R14
signal_process/rs422/un3_p_sum_add_R15
signal_process/rs422/un3_p_sum_add_R16
signal_process/rs422/un3_p_sum_add_R17
signal_process/rs422/un3_p_sum_add_R18
signal_process/rs422/un3_p_sum_add_R19
signal_process/rs422/un3_p_sum_add_R20
signal_process/rs422/un3_p_sum_add_R21
signal_process/rs422/un3_p_sum_add_R22
signal_process/rs422/un3_p_sum_add_SIGNEDR
signal_process/trans/un6_count_s_19_0_S1
signal_process/trans/un6_count_s_19_0_COUT
signal_process/trans/un6_count_cry_0_0_S1
signal_process/trans/un6_count_cry_0_0_S0
signal_process/trans/N_1
[ END CLIPPED ]
[ START DESIGN PREFS ]
SCHEMATIC START ;
# map:  version Diamond (64-bit) 3.12.1.454 -- WARNING: Map write only section -- Thu Sep 25 10:29:39 2025

SYSCONFIG SLAVE_SPI_PORT=DISABLE MASTER_SPI_PORT=ENABLE SLAVE_PARALLEL_PORT=DISABLE BACKGROUND_RECONFIG=OFF DONE_EX=OFF DONE_OD=ON DONE_PULL=ON MCCLK_FREQ=2.4 TRANSFR=OFF CONFIG_IOVOLTAGE=3.3 CONFIG_SECURE=OFF WAKE_UP=21 COMPRESS_CONFIG=OFF CONFIG_MODE=JTAG INBUF=OFF ;
LOCATE COMP "wendu/SLICE_241" SITE "CLKOS3" ;
LOCATE COMP "dq" SITE "N16" ;
LOCATE COMP "TXD" SITE "A13" ;
LOCATE COMP "clk_in" SITE "L16" ;
LOCATE COMP "clk_DA" SITE "K1" ;
LOCATE COMP "clk_AD_t" SITE "L1" ;
LOCATE COMP "DA_DATA[13]" SITE "K3" ;
LOCATE COMP "DA_DATA[12]" SITE "K2" ;
LOCATE COMP "DA_DATA[11]" SITE "J2" ;
LOCATE COMP "DA_DATA[10]" SITE "J1" ;
LOCATE COMP "DA_DATA[9]" SITE "G2" ;
LOCATE COMP "DA_DATA[8]" SITE "G1" ;
LOCATE COMP "DA_DATA[7]" SITE "F2" ;
LOCATE COMP "DA_DATA[6]" SITE "F1" ;
LOCATE COMP "DA_DATA[5]" SITE "E1" ;
LOCATE COMP "DA_DATA[4]" SITE "D1" ;
LOCATE COMP "DA_DATA[3]" SITE "C1" ;
LOCATE COMP "DA_DATA[2]" SITE "C2" ;
LOCATE COMP "DA_DATA[1]" SITE "B1" ;
LOCATE COMP "DA_DATA[0]" SITE "B2" ;
LOCATE COMP "AD_DATA[11]" SITE "P1" ;
LOCATE COMP "AD_DATA[10]" SITE "P2" ;
LOCATE COMP "AD_DATA[9]" SITE "R1" ;
LOCATE COMP "AD_DATA[8]" SITE "R2" ;
LOCATE COMP "AD_DATA[7]" SITE "T2" ;
LOCATE COMP "AD_DATA[6]" SITE "R3" ;
LOCATE COMP "AD_DATA[5]" SITE "T3" ;
LOCATE COMP "AD_DATA[4]" SITE "R4" ;
LOCATE COMP "AD_DATA[3]" SITE "T4" ;
LOCATE COMP "AD_DATA[2]" SITE "R5" ;
LOCATE COMP "AD_DATA[1]" SITE "P6" ;
LOCATE COMP "AD_DATA[0]" SITE "N6" ;
LOCATE COMP "TxTransmit" SITE "P16" ;
FREQUENCY NET "clk120mhz" 120.000000 MHz ;
FREQUENCY NET "wendu.clk_us" 1.000000 MHz ;
USE PRIMARY NET "clk120mhz" ;
FREQUENCY NET "clk_in_c" 20.000000 MHz ;
FREQUENCY NET "clk_AD" 60.000000 MHz ;
FREQUENCY NET "clk_AD_t_c" 60.000000 MHz ;
SCHEMATIC END ;
[ END DESIGN PREFS ]
