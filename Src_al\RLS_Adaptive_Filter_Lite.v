`timescale 1ns / 1ps
//////////////////////////////////////////////////////////////////////////////////
// Company: tianhailu
// Engineer: Andrew
// 
// Create Date:    15:33:33 09/11/2025
// Design Name:    IFOG501_2B
// Module Name:    RLS_Adaptive_Filter_Lite
// Project Name:   IFOG501_2B
// Target Devices: 
// Tool versions:  TD5.6.2-64bit
// Description:    简化版RLS自适应滤波器
//                 优化资源使用，减少布线复杂度
//
// 简化策略：
// 1. 减少滤波器阶数到4阶
// 2. 使用简化的LMS算法替代完整RLS
// 3. 优化数据位宽和精度
// 4. 减少状态机复杂度
//
// Revision 1.01 - File Created
// Additional Comments: 
// - 针对FPGA资源优化的轻量级实现
// - 保持良好的滤波性能
// - 降低布线复杂度
//////////////////////////////////////////////////////////////////////////////////

module RLS_Adaptive_Filter_Lite
#(
    parameter FILTER_ORDER = 4,         // 简化为4阶滤波器
    parameter DATA_WIDTH = 32,           // 减少数据位宽到32位
    parameter FRAC_WIDTH = 16,           // 减少小数位宽到16位
    parameter COEFF_WIDTH = 24,          // 减少系数位宽到24位
    parameter MU_SHIFT = 8               // LMS步长因子μ = 2^(-8)
)
(
    // 时钟和复位
    input                           clk,            // 系统时钟
    input                           rst_n,          // 异步复位，低有效
    
    // 控制信号
    input                           enable,         // 滤波器使能
    input                           data_valid,     // 输入数据有效
    
    // 数据接口
    input  signed [DATA_WIDTH-1:0]  data_in,        // 输入信号x(n)
    input  signed [DATA_WIDTH-1:0]  desired_in,     // 期望信号d(n)
    output signed [DATA_WIDTH-1:0]  data_out,       // 滤波输出y(n)
    output signed [DATA_WIDTH-1:0]  error_out,      // 误差输出e(n)
    
    // 状态输出
    output                          output_valid,   // 输出数据有效
    output                          converged,      // 收敛指示
    output [15:0]                   mse_out         // 均方误差输出
);

//////////////////////////////////////////////////
// 内部信号定义
//////////////////////////////////////////////////

// 延迟线寄存器（使用分布式RAM）
(* ram_style = "distributed" *) reg signed [DATA_WIDTH-1:0] delay_line [0:FILTER_ORDER-1];

// 滤波器系数（使用寄存器）
reg signed [COEFF_WIDTH-1:0] coefficients [0:FILTER_ORDER-1];

// 计算信号
reg signed [DATA_WIDTH-1:0]     desired_reg;       // 期望信号寄存器
reg signed [DATA_WIDTH+8-1:0]   filter_output;     // 滤波器输出
reg signed [DATA_WIDTH-1:0]     error_signal;      // 误差信号
reg signed [DATA_WIDTH-1:0]     data_out_reg;      // 输出寄存器

// 控制信号
reg [1:0]                       state;             // 简化状态机
reg [2:0]                       calc_counter;      // 计算计数器
reg                             output_valid_reg;  // 输出有效寄存器

// 收敛检测
reg [31:0]                      mse_accumulator;   // 均方误差累加器
reg [7:0]                       mse_counter;       // 均方误差计数器
reg                             converged_reg;     // 收敛标志

// 流水线寄存器
reg signed [DATA_WIDTH-1:0]     data_in_reg;
reg signed [DATA_WIDTH-1:0]     desired_in_reg;
reg                             data_valid_reg;

//////////////////////////////////////////////////
// 状态机定义（简化）
//////////////////////////////////////////////////
localparam IDLE         = 2'b00;
localparam CALC_OUTPUT  = 2'b01;
localparam UPDATE_COEFF = 2'b10;
localparam DONE         = 2'b11;

//////////////////////////////////////////////////
// 初始化
//////////////////////////////////////////////////
integer i;
initial begin
    // 初始化滤波器系数为小值
    for (i = 0; i < FILTER_ORDER; i = i + 1) begin
        coefficients[i] = 24'h001000;  // 小的初始值
        delay_line[i] = 0;
    end
    
    state = IDLE;
    calc_counter = 0;
    output_valid_reg = 1'b0;
    mse_accumulator = 0;
    mse_counter = 0;
    converged_reg = 1'b0;
    filter_output = 0;
    error_signal = 0;
    data_out_reg = 0;
end

//////////////////////////////////////////////////
// 输入数据流水线
//////////////////////////////////////////////////
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        data_in_reg <= 0;
        desired_in_reg <= 0;
        data_valid_reg <= 1'b0;
    end
    else begin
        data_in_reg <= data_in;
        desired_in_reg <= desired_in;
        data_valid_reg <= data_valid;
    end
end

//////////////////////////////////////////////////
// 简化的LMS算法实现
//////////////////////////////////////////////////
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        state <= IDLE;
        calc_counter <= 0;
        output_valid_reg <= 1'b0;
        
        // 重新初始化
        for (i = 0; i < FILTER_ORDER; i = i + 1) begin
            coefficients[i] <= 24'h001000;
            delay_line[i] <= 0;
        end
        
        filter_output <= 0;
        error_signal <= 0;
        data_out_reg <= 0;
    end
    else if (enable) begin
        case (state)
            IDLE: begin
                output_valid_reg <= 1'b0;
                if (data_valid_reg) begin
                    // 更新延迟线（串行移位，减少资源）
                    delay_line[0] <= data_in_reg;
                    for (i = 1; i < FILTER_ORDER; i = i + 1) begin
                        delay_line[i] <= delay_line[i-1];
                    end
                    
                    desired_reg <= desired_in_reg;
                    filter_output <= 0;
                    calc_counter <= 0;
                    state <= CALC_OUTPUT;
                end
            end
            
            CALC_OUTPUT: begin
                // 串行计算滤波器输出，减少并行乘法器使用
                if (calc_counter < FILTER_ORDER) begin
                    filter_output <= filter_output + 
                        ((delay_line[calc_counter] >>> 4) * (coefficients[calc_counter] >>> 4));
                    calc_counter <= calc_counter + 1;
                end
                else begin
                    // 计算误差
                    error_signal <= desired_reg - filter_output[DATA_WIDTH+4-1:4];
                    data_out_reg <= filter_output[DATA_WIDTH+4-1:4];
                    calc_counter <= 0;
                    state <= UPDATE_COEFF;
                end
            end
            
            UPDATE_COEFF: begin
                // 简化的LMS系数更新：w(n) = w(n-1) + μ*e(n)*x(n)
                if (calc_counter < FILTER_ORDER) begin
                    coefficients[calc_counter] <= coefficients[calc_counter] + 
                        ((error_signal * delay_line[calc_counter]) >>> MU_SHIFT);
                    calc_counter <= calc_counter + 1;
                end
                else begin
                    state <= DONE;
                end
            end
            
            DONE: begin
                output_valid_reg <= 1'b1;
                
                // 更新均方误差
                mse_accumulator <= mse_accumulator + (error_signal[15:0] * error_signal[15:0]);
                mse_counter <= mse_counter + 1;
                
                // 收敛检测：连续256个样本的MSE小于阈值
                if (mse_counter == 8'hFF) begin
                    if (mse_accumulator < 32'h00001000) begin  // 阈值可调
                        converged_reg <= 1'b1;
                    end
                    else begin
                        converged_reg <= 1'b0;
                    end
                    mse_accumulator <= 0;
                    mse_counter <= 0;
                end
                
                state <= IDLE;
            end
            
            default: state <= IDLE;
        endcase
    end
    else begin
        // 滤波器禁用时保持空闲状态
        state <= IDLE;
        output_valid_reg <= 1'b0;
    end
end

//////////////////////////////////////////////////
// 输出信号分配
//////////////////////////////////////////////////
assign data_out = data_out_reg;
assign error_out = error_signal;
assign output_valid = output_valid_reg;
assign converged = converged_reg;
assign mse_out = mse_accumulator[31:16];  // 输出MSE的高16位

endmodule
