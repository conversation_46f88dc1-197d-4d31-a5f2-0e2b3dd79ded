
Copyright (C) 1994-2021 Synopsys, Inc.
This Synopsys software and all associated documentation are proprietary to Synopsys, Inc.
and may only be used pursuant to the terms and conditions of a written license agreement
with Synopsys, Inc. All other use, reproduction, modification, or distribution of the
Synopsys software or the associated documentation is strictly prohibited.
Tool: Synplify Pro (R)
Build: R-2021.03L-SP1-1
Install: D:\Software\lscc\diamond\3.12\synpbase
OS: Windows 6.2

Hostname: TLH-022

Implementation : impl1

# Written on Thu Sep 25 10:29:26 2025

##### DESIGN INFO #######################################################

Top View:                "INS350_5J_JZ"
Constraint File(s):      (none)




##### SUMMARY ############################################################

Found 0 issues in 0 out of 0 constraints


##### DETAILS ############################################################



Clock Relationships
*******************

Starting                              Ending                                |     rise to rise     |     fall to fall     |     rise to fall     |     fall to rise                     
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
System                                System                                |     5.000            |     No paths         |     No paths         |     No paths                         
System                                global_clock|CLKOP_inferred_clock     |     5.000            |     No paths         |     No paths         |     No paths                         
System                                global_clock|CLKOS_inferred_clock     |     5.000            |     No paths         |     No paths         |     No paths                         
System                                DS18B20|clk_us_derived_clock          |     5.000            |     No paths         |     No paths         |     No paths                         
global_clock|CLKOP_inferred_clock     System                                |     5.000            |     No paths         |     No paths         |     No paths                         
global_clock|CLKOP_inferred_clock     global_clock|CLKOP_inferred_clock     |     5.000            |     No paths         |     No paths         |     No paths                         
global_clock|CLKOP_inferred_clock     global_clock|CLKOS_inferred_clock     |     Diff grp         |     No paths         |     No paths         |     No paths                         
global_clock|CLKOS_inferred_clock     System                                |     5.000            |     No paths         |     No paths         |     No paths                         
global_clock|CLKOS_inferred_clock     global_clock|CLKOP_inferred_clock     |     Diff grp         |     No paths         |     No paths         |     No paths                         
global_clock|CLKOS_inferred_clock     global_clock|CLKOS_inferred_clock     |     5.000            |     No paths         |     No paths         |     No paths                         
DS18B20|clk_us_derived_clock          global_clock|CLKOP_inferred_clock     |     5.000            |     No paths         |     No paths         |     No paths                         
DS18B20|clk_us_derived_clock          DS18B20|clk_us_derived_clock          |     5.000            |     No paths         |     No paths         |     No paths                         
=======================================================================================================================================================================================
 Note: 'No paths' indicates there are no paths in the design for that pair of clock edges.
       'Diff grp' indicates that paths exist but the starting clock and ending clock are in different clock groups.


Unconstrained Start/End Points
******************************

p:AD_DATA[0]
p:AD_DATA[1]
p:AD_DATA[2]
p:AD_DATA[3]
p:AD_DATA[4]
p:AD_DATA[5]
p:AD_DATA[6]
p:AD_DATA[7]
p:AD_DATA[8]
p:AD_DATA[9]
p:AD_DATA[10]
p:AD_DATA[11]
p:DA_DATA[0]
p:DA_DATA[1]
p:DA_DATA[2]
p:DA_DATA[3]
p:DA_DATA[4]
p:DA_DATA[5]
p:DA_DATA[6]
p:DA_DATA[7]
p:DA_DATA[8]
p:DA_DATA[9]
p:DA_DATA[10]
p:DA_DATA[11]
p:DA_DATA[12]
p:DA_DATA[13]
p:RXD
p:RxTransmit
p:TXD
p:TxTransmit
p:clk_AD_t
p:clk_DA
p:clk_in
p:dq (bidir end point)
p:dq (bidir start point)


Inapplicable constraints
************************

(none)


Applicable constraints with issues
**********************************

(none)


Constraints with matching wildcard expressions
**********************************************

(none)


Library Report
**************


# End of Constraint Checker Report
