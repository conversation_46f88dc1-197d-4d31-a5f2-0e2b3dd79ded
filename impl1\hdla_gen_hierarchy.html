<HTML>         	<HEAD><TITLE></TITLE>                                   	<STYLE TYPE="text/css">                               	<!--                                                    	body,pre{                                              	font-family:'Courier New', monospace;             	color: #000000;                                     	font-size:88%;                                      	background-color: #ffffff;                          	}                                                       	h1 {                                                    	font-weight: bold;                                  	margin-top: 24px;                                   	margin-bottom: 10px;                                	border-bottom: 3px solid #000;    font-size: 1em;   	}                                                       	h2 {                                                    	font-weight: bold;                                  	margin-top: 18px;                                   	margin-bottom: 5px;                                 	font-size: 0.90em;                                  	}                                                       	h3 {                                                    	font-weight: bold;                                  	margin-top: 12px;                                   	margin-bottom: 5px;                                 	font-size: 0.80em;                                       	}                                                       	p {                                                     	font-size:78%;                                      	}                                                       	P.Table {                                               	margin-top: 4px;                                    	margin-bottom: 4px;                                 	margin-right: 4px;                                  	margin-left: 4px;                                   	}                                                       	table                                                   	{                                                       	border-width: 1px 1px 1px 1px;                      	border-style: solid solid solid solid;              	border-color: black black black black;              	border-collapse: collapse;                          	}                                                       	th {                                                    	font-weight:bold;                                   	padding: 4px;                                       	border-width: 1px 1px 1px 1px;                      	border-style: solid solid solid solid;              	border-color: black black black black;              	vertical-align:top;                                 	text-align:left;                                    	font-size:78%;                                           	}                                                       	td {                                                    	padding: 4px;                                       	border-width: 1px 1px 1px 1px;                      	border-style: solid solid solid solid;              	border-color: black black black black;              	vertical-align:top;                                 	font-size:78%;                                      	}                                                       	a {                                                     	color:#013C9A;                                      	text-decoration:none;                               	}                                                       		a:visited {                                             	color:#013C9A;                                      	}                                                       		a:hover, a:active {                                     	text-decoration:underline;                          	color:#5BAFD4;                                      	}                                                       	.pass                                                   	{                                                       	background-color: #00ff00;                              	}                                                                	.fail                                                   	{                                                       	background-color: #ff0000;                              	}                                                       	.comment                                                	{                                                       	font-size: 90%;                                     	font-style: italic;                                 	}                                                       		-->                                                     	</STYLE>                                                	</HEAD>                                                 	<BODY>                                                  	<PRE>Setting log file to 'D:/Project/TLH50_03J_JZ_20250925/impl1/hdla_gen_hierarchy.html'.
Starting: parse design source files
(VERI-1482) Analyzing Verilog file 'D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v'
(VERI-1482) Analyzing Verilog file 'D:/Project/TLH50_03J_JZ_20250925/Src_al/INS350_5J_JZ.v'
(VERI-1482) Analyzing Verilog file 'D:/Project/TLH50_03J_JZ_20250925/Src_al/Src_al/SignalProcessing.v'
(VERI-1482) Analyzing Verilog file 'D:/Project/TLH50_03J_JZ_20250925/Src_al/Src_al/speed_select_Tx.v'
(VERI-1482) Analyzing Verilog file 'D:/Project/TLH50_03J_JZ_20250925/Src_al/Src_al/SquareWaveGenerator.v'
(VERI-1482) Analyzing Verilog file 'D:/Project/TLH50_03J_JZ_20250925/Src_al/Src_al/UART_Control.v'
(VERI-1482) Analyzing Verilog file 'D:/Project/TLH50_03J_JZ_20250925/Src_al/Src_al/uart_tx.v'
(VERI-1482) Analyzing Verilog file 'D:/Project/TLH50_03J_JZ_20250925/Src_al/Src_al/SignalGenerator.v'
(VERI-1482) Analyzing Verilog file 'D:/Project/TLH50_03J_JZ_20250925/Src_al/Src_al/Ctrl_Data.v'
(VERI-1482) Analyzing Verilog file 'D:/Project/TLH50_03J_JZ_20250925/Src_al/Src_al/DS18B20.v'
(VERI-1482) Analyzing Verilog file 'D:/Project/TLH50_03J_JZ_20250925/Src_al/Src_al/Integration.v'
(VERI-1482) Analyzing Verilog file 'D:/Project/TLH50_03J_JZ_20250925/Src_al/Src_al/Modulation.v'
(VERI-1482) Analyzing Verilog file 'D:/Project/TLH50_03J_JZ_20250925/Src_al/Src_al/Rs422Output.v'
(VERI-1482) Analyzing Verilog file 'D:/Project/TLH50_03J_JZ_20250925/Src_al/Demodulation.v'
(VERI-1482) Analyzing Verilog file 'D:/Project/TLH50_03J_JZ_20250925/IP_al/global_clock1/Asys_fifo56X16/Asys_fifo56X16.v'
(VERI-1482) Analyzing Verilog file 'D:/Project/TLH50_03J_JZ_20250925/IP_al/global_clock1/global_clock/global_clock.v'
INFO - D:/Project/TLH50_03J_JZ_20250925/Src_al/INS350_5J_JZ.v(17,8-17,20) (VERI-1018) compiling module 'INS350_5J_JZ'
INFO - D:/Project/TLH50_03J_JZ_20250925/Src_al/INS350_5J_JZ.v(17,1-159,10) (VERI-9000) elaborating module 'INS350_5J_JZ'
INFO - D:/Project/TLH50_03J_JZ_20250925/IP_al/global_clock1/global_clock/global_clock.v(8,1-86,10) (VERI-9000) elaborating module 'global_clock_uniq_1'
INFO - D:/Project/TLH50_03J_JZ_20250925/Src_al/Src_al/DS18B20.v(17,1-318,10) (VERI-9000) elaborating module 'DS18B20_uniq_1'
INFO - D:/Project/TLH50_03J_JZ_20250925/Src_al/Src_al/UART_Control.v(16,1-101,10) (VERI-9000) elaborating module 'UART_Control_uniq_1'
INFO - D:/Project/TLH50_03J_JZ_20250925/Src_al/Src_al/SignalProcessing.v(41,1-190,10) (VERI-9000) elaborating module 'SignalProcessing_uniq_1'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(757,1-759,10) (VERI-9000) elaborating module 'VHI_uniq_1'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(761,1-763,10) (VERI-9000) elaborating module 'VLO_uniq_1'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(1696,1-1738,10) (VERI-9000) elaborating module 'EHXPLLL_uniq_1'
INFO - D:/Project/TLH50_03J_JZ_20250925/Src_al/Src_al/speed_select_Tx.v(17,1-60,10) (VERI-9000) elaborating module 'speed_select_Tx_uniq_1'
INFO - D:/Project/TLH50_03J_JZ_20250925/Src_al/Src_al/uart_tx.v(16,1-109,10) (VERI-9000) elaborating module 'uart_tx_uniq_1'
INFO - D:/Project/TLH50_03J_JZ_20250925/Src_al/Src_al/Ctrl_Data.v(15,1-225,10) (VERI-9000) elaborating module 'Ctrl_Data_uniq_1'
INFO - D:/Project/TLH50_03J_JZ_20250925/Src_al/Src_al/SignalGenerator.v(42,1-129,10) (VERI-9000) elaborating module 'SignalGenerator_uniq_1'
INFO - D:/Project/TLH50_03J_JZ_20250925/Src_al/Demodulation.v(42,1-185,10) (VERI-9000) elaborating module 'Demodulation_uniq_1'
INFO - D:/Project/TLH50_03J_JZ_20250925/Src_al/Src_al/Integration.v(42,1-80,10) (VERI-9000) elaborating module 'Integration_uniq_1'
INFO - D:/Project/TLH50_03J_JZ_20250925/Src_al/Src_al/Modulation.v(42,1-158,10) (VERI-9000) elaborating module 'Modulation_uniq_1'
INFO - D:/Project/TLH50_03J_JZ_20250925/Src_al/Src_al/Rs422Output.v(43,1-233,10) (VERI-9000) elaborating module 'Rs422Output_uniq_1'
INFO - D:/Project/TLH50_03J_JZ_20250925/Src_al/Src_al/SquareWaveGenerator.v(16,1-49,10) (VERI-9000) elaborating module 'SquareWaveGenerator_uniq_1'
INFO - D:/Project/TLH50_03J_JZ_20250925/IP_al/global_clock1/Asys_fifo56X16/Asys_fifo56X16.v(8,1-809,10) (VERI-9000) elaborating module 'Asys_fifo56X16_uniq_1'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(757,1-759,10) (VERI-9000) elaborating module 'VHI_uniq_2'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(761,1-763,10) (VERI-9000) elaborating module 'VLO_uniq_2'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(25,1-29,10) (VERI-9000) elaborating module 'AND2_uniq_1'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(25,1-29,10) (VERI-9000) elaborating module 'AND2_uniq_2'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(25,1-29,10) (VERI-9000) elaborating module 'AND2_uniq_3'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(25,1-29,10) (VERI-9000) elaborating module 'AND2_uniq_4'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(367,1-370,10) (VERI-9000) elaborating module 'INV_uniq_1'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(367,1-370,10) (VERI-9000) elaborating module 'INV_uniq_2'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(367,1-370,10) (VERI-9000) elaborating module 'INV_uniq_3'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(367,1-370,10) (VERI-9000) elaborating module 'INV_uniq_4'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(367,1-370,10) (VERI-9000) elaborating module 'INV_uniq_5'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(367,1-370,10) (VERI-9000) elaborating module 'INV_uniq_6'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(367,1-370,10) (VERI-9000) elaborating module 'INV_uniq_7'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(367,1-370,10) (VERI-9000) elaborating module 'INV_uniq_8'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(367,1-370,10) (VERI-9000) elaborating module 'INV_uniq_9'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(810,1-814,10) (VERI-9000) elaborating module 'XOR2_uniq_1'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(710,1-717,10) (VERI-9000) elaborating module 'ROM16X1A_uniq_1'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(710,1-717,10) (VERI-9000) elaborating module 'ROM16X1A_uniq_2'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(710,1-717,10) (VERI-9000) elaborating module 'ROM16X1A_uniq_3'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(710,1-717,10) (VERI-9000) elaborating module 'ROM16X1A_uniq_4'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(959,1-1047,10) (VERI-9000) elaborating module 'PDPW16KD_uniq_1'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(959,1-1047,10) (VERI-9000) elaborating module 'PDPW16KD_uniq_2'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(119,1-126,10) (VERI-9000) elaborating module 'FD1P3DX_uniq_1'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(119,1-126,10) (VERI-9000) elaborating module 'FD1P3DX_uniq_2'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(119,1-126,10) (VERI-9000) elaborating module 'FD1P3DX_uniq_3'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(119,1-126,10) (VERI-9000) elaborating module 'FD1P3DX_uniq_4'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(119,1-126,10) (VERI-9000) elaborating module 'FD1P3DX_uniq_5'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(119,1-126,10) (VERI-9000) elaborating module 'FD1P3DX_uniq_6'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(119,1-126,10) (VERI-9000) elaborating module 'FD1P3DX_uniq_7'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(119,1-126,10) (VERI-9000) elaborating module 'FD1P3DX_uniq_8'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(119,1-126,10) (VERI-9000) elaborating module 'FD1P3DX_uniq_9'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(119,1-126,10) (VERI-9000) elaborating module 'FD1P3DX_uniq_10'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(119,1-126,10) (VERI-9000) elaborating module 'FD1P3DX_uniq_11'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(119,1-126,10) (VERI-9000) elaborating module 'FD1P3DX_uniq_12'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(119,1-126,10) (VERI-9000) elaborating module 'FD1P3DX_uniq_13'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(119,1-126,10) (VERI-9000) elaborating module 'FD1P3DX_uniq_14'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(119,1-126,10) (VERI-9000) elaborating module 'FD1P3DX_uniq_15'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(119,1-126,10) (VERI-9000) elaborating module 'FD1P3DX_uniq_16'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(119,1-126,10) (VERI-9000) elaborating module 'FD1P3DX_uniq_17'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(119,1-126,10) (VERI-9000) elaborating module 'FD1P3DX_uniq_18'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(119,1-126,10) (VERI-9000) elaborating module 'FD1P3DX_uniq_19'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(119,1-126,10) (VERI-9000) elaborating module 'FD1P3DX_uniq_20'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(119,1-126,10) (VERI-9000) elaborating module 'FD1P3DX_uniq_21'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(119,1-126,10) (VERI-9000) elaborating module 'FD1P3DX_uniq_22'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(119,1-126,10) (VERI-9000) elaborating module 'FD1P3DX_uniq_23'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(119,1-126,10) (VERI-9000) elaborating module 'FD1P3DX_uniq_24'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(160,1-166,10) (VERI-9000) elaborating module 'FD1S3BX_uniq_1'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(160,1-166,10) (VERI-9000) elaborating module 'FD1S3BX_uniq_2'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(168,1-174,10) (VERI-9000) elaborating module 'FD1S3DX_uniq_1'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(168,1-174,10) (VERI-9000) elaborating module 'FD1S3DX_uniq_2'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_1'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_2'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_3'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_4'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_5'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_6'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_7'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_8'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_9'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_10'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_11'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_12'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_13'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_14'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_15'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_16'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_17'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_18'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_19'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_20'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_21'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_22'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_23'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_24'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_25'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_26'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_27'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_28'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_29'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_30'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_31'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_32'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_33'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_34'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_35'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_36'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_37'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_38'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_39'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_40'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_41'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_42'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_43'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_44'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_45'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_46'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_47'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_48'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_49'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_50'
INFO - D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76,1-91,10) (VERI-9000) elaborating module 'CCU2C_uniq_51'
Done: design load finished with (0) errors, and (0) warnings

</PRE></BODY></HTML>