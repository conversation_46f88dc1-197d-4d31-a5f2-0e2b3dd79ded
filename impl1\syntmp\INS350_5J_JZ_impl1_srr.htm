<html><body><samp><pre>
<!@TC:**********>
#Build: Synplify Pro (R) R-2021.03L-SP1-1, Build 223R, Dec 22 2021
#install: D:\Software\lscc\diamond\3.12\synpbase
#OS: Windows 8 6.2
#Hostname: TLH-022

# Thu Sep 25 10:29:18 2025

#Implementation: impl1


Copyright (C) 1994-2021 Synopsys, Inc.
This Synopsys software and all associated documentation are proprietary to Synopsys, Inc.
and may only be used pursuant to the terms and conditions of a written license agreement
with Synopsys, Inc. All other use, reproduction, modification, or distribution of the
Synopsys software or the associated documentation is strictly prohibited.
Tool: Synplify Pro (R)
Build: R-2021.03L-SP1-1
Install: D:\Software\lscc\diamond\3.12\synpbase
OS: Windows 6.2

Hostname: TLH-022

Implementation : impl1
<a name=compilerReport1></a>Synopsys HDL Compiler, Version comp202103synp2, Build 222R, Built Dec 22 2021 00:18:12, @</a>

@N: : <!@TM:**********> | Running in 64-bit mode 
###########################################################[

Copyright (C) 1994-2021 Synopsys, Inc.
This Synopsys software and all associated documentation are proprietary to Synopsys, Inc.
and may only be used pursuant to the terms and conditions of a written license agreement
with Synopsys, Inc. All other use, reproduction, modification, or distribution of the
Synopsys software or the associated documentation is strictly prohibited.
Tool: Synplify Pro (R)
Build: R-2021.03L-SP1-1
Install: D:\Software\lscc\diamond\3.12\synpbase
OS: Windows 6.2

Hostname: TLH-022

Implementation : impl1
<a name=compilerReport2></a>Synopsys Verilog Compiler, Version comp202103synp2, Build 222R, Built Dec 22 2021 00:18:12, @</a>

@N: : <!@TM:**********> | Running in 64-bit mode 
@I::"D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v" (library work)
@I::"D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\pmi_def.v" (library work)
@I::"D:\Software\lscc\diamond\3.12\synpbase\lib\vlog\hypermods.v" (library __hyper__lib__)
@I::"D:\Software\lscc\diamond\3.12\synpbase\lib\vlog\umr_capim.v" (library snps_haps)
@I::"D:\Software\lscc\diamond\3.12\synpbase\lib\vlog\scemi_objects.v" (library snps_haps)
@I::"D:\Software\lscc\diamond\3.12\synpbase\lib\vlog\scemi_pipes.svh" (library snps_haps)
@I::"D:\Project\TLH50_03J_JZ_20250925\Src_al\INS350_5J_JZ.v" (library work)
@I::"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\SignalProcessing.v" (library work)
@I::"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\speed_select_Tx.v" (library work)
@I::"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\SquareWaveGenerator.v" (library work)
@I::"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\UART_Control.v" (library work)
@I::"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\uart_tx.v" (library work)
@I::"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\SignalGenerator.v" (library work)
@I::"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Ctrl_Data.v" (library work)
@I::"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\DS18B20.v" (library work)
@I::"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Integration.v" (library work)
@I::"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v" (library work)
@I::"D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Rs422Output.v" (library work)
<font color=#A52A2A>@W:<a href="@W:CS141:@XP_HELP">CS141</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Rs422Output.v:55:34:55:38:@W:CS141:@XP_MSG">Rs422Output.v(55)</a><!@TM:**********> | Unrecognized synthesis directive keep. Verify the correct directive name.</font>
<font color=#A52A2A>@W:<a href="@W:CS141:@XP_HELP">CS141</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Rs422Output.v:62:33:62:37:@W:CS141:@XP_MSG">Rs422Output.v(62)</a><!@TM:**********> | Unrecognized synthesis directive keep. Verify the correct directive name.</font>
@I::"D:\Project\TLH50_03J_JZ_20250925\Src_al\Demodulation.v" (library work)
@I::"D:\Project\TLH50_03J_JZ_20250925\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v" (library work)
@I::"D:\Project\TLH50_03J_JZ_20250925\IP_al\global_clock1\global_clock\global_clock.v" (library work)
Verilog syntax check successful!
Options changed - recompiling
Selecting top level module INS350_5J_JZ
@N:<a href="@N:CG364:@XP_HELP">CG364</a> : <a href="D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v:757:7:757:10:@N:CG364:@XP_MSG">ecp5u.v(757)</a><!@TM:**********> | Synthesizing module VHI in library work.
Running optimization stage 1 on VHI .......
Finished optimization stage 1 on VHI (CPU Time 0h:00m:00s, Memory Used current: 110MB peak: 110MB)
@N:<a href="@N:CG364:@XP_HELP">CG364</a> : <a href="D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v:761:7:761:10:@N:CG364:@XP_MSG">ecp5u.v(761)</a><!@TM:**********> | Synthesizing module VLO in library work.
Running optimization stage 1 on VLO .......
Finished optimization stage 1 on VLO (CPU Time 0h:00m:00s, Memory Used current: 110MB peak: 110MB)
@N:<a href="@N:CG364:@XP_HELP">CG364</a> : <a href="D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v:1696:7:1696:14:@N:CG364:@XP_MSG">ecp5u.v(1696)</a><!@TM:**********> | Synthesizing module EHXPLLL in library work.
Running optimization stage 1 on EHXPLLL .......
Finished optimization stage 1 on EHXPLLL (CPU Time 0h:00m:00s, Memory Used current: 110MB peak: 111MB)
@N:<a href="@N:CG364:@XP_HELP">CG364</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\IP_al\global_clock1\global_clock\global_clock.v:8:7:8:19:@N:CG364:@XP_MSG">global_clock.v(8)</a><!@TM:**********> | Synthesizing module global_clock in library work.
Running optimization stage 1 on global_clock .......
<font color=#A52A2A>@W:<a href="@W:CL168:@XP_HELP">CL168</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\IP_al\global_clock1\global_clock\global_clock.v:23:8:23:22:@W:CL168:@XP_MSG">global_clock.v(23)</a><!@TM:**********> | Removing instance scuba_vhi_inst because it does not drive other instances. To preserve this instance, use the syn_noprune synthesis directive.</font>
Finished optimization stage 1 on global_clock (CPU Time 0h:00m:00s, Memory Used current: 110MB peak: 111MB)
@N:<a href="@N:CG364:@XP_HELP">CG364</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\DS18B20.v:17:7:17:14:@N:CG364:@XP_MSG">DS18B20.v(17)</a><!@TM:**********> | Synthesizing module DS18B20 in library work.
@N:<a href="@N:CG179:@XP_HELP">CG179</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\DS18B20.v:85:12:85:18:@N:CG179:@XP_MSG">DS18B20.v(85)</a><!@TM:**********> | Removing redundant assignment.
@N:<a href="@N:CG179:@XP_HELP">CG179</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\DS18B20.v:189:20:189:28:@N:CG179:@XP_MSG">DS18B20.v(189)</a><!@TM:**********> | Removing redundant assignment.
@N:<a href="@N:CG179:@XP_HELP">CG179</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\DS18B20.v:248:20:248:28:@N:CG179:@XP_MSG">DS18B20.v(248)</a><!@TM:**********> | Removing redundant assignment.
@N:<a href="@N:CG179:@XP_HELP">CG179</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\DS18B20.v:291:15:291:19:@N:CG179:@XP_MSG">DS18B20.v(291)</a><!@TM:**********> | Removing redundant assignment.
@N:<a href="@N:CG179:@XP_HELP">CG179</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\DS18B20.v:307:21:307:30:@N:CG179:@XP_MSG">DS18B20.v(307)</a><!@TM:**********> | Removing redundant assignment.
Running optimization stage 1 on DS18B20 .......
@A:<a href="@A:CL282:@XP_HELP">CL282</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\DS18B20.v:157:0:157:6:@A:CL282:@XP_MSG">DS18B20.v(157)</a><!@TM:**********> | Feedback mux created for signal rd_flag. It is possible a set/reset assignment for this is signal missing. To improve timing and area, specify a set/reset value.
<font color=#A52A2A>@W:<a href="@W:CL190:@XP_HELP">CL190</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\DS18B20.v:157:0:157:6:@W:CL190:@XP_MSG">DS18B20.v(157)</a><!@TM:**********> | Optimizing register bit dq_out to a constant 0. To keep the instance, apply constraint syn_preserve=1 on the instance.</font>
@N:<a href="@N:CL189:@XP_HELP">CL189</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\DS18B20.v:157:0:157:6:@N:CL189:@XP_MSG">DS18B20.v(157)</a><!@TM:**********> | Register bit rd_flag is always 1.
<font color=#A52A2A>@W:<a href="@W:CL169:@XP_HELP">CL169</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\DS18B20.v:157:0:157:6:@W:CL169:@XP_MSG">DS18B20.v(157)</a><!@TM:**********> | Pruning unused register dq_out. Make sure that there are no unused intermediate registers.</font>
Finished optimization stage 1 on DS18B20 (CPU Time 0h:00m:00s, Memory Used current: 112MB peak: 113MB)
@N:<a href="@N:CG364:@XP_HELP">CG364</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\speed_select_Tx.v:17:7:17:22:@N:CG364:@XP_MSG">speed_select_Tx.v(17)</a><!@TM:**********> | Synthesizing module speed_select_Tx in library work.

	SYS_FREQ=32'b00000111001001110000111000000000
	UART_BAUD=32'b00000000000000011100001000000000
	BPS_PARA=32'b00000000000000000000010000010001
	BPS_PARA_2=32'b00000000000000000000001000001000
   Generated name = speed_select_Tx_120000000s_115200s_1041s_520s
<font color=#A52A2A>@W:<a href="@W:CG133:@XP_HELP">CG133</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\speed_select_Tx.v:34:16:34:25:@W:CG133:@XP_MSG">speed_select_Tx.v(34)</a><!@TM:**********> | Object uart_ctrl is declared but not assigned. Either assign a value or remove the declaration.</font>
Running optimization stage 1 on speed_select_Tx_120000000s_115200s_1041s_520s .......
Finished optimization stage 1 on speed_select_Tx_120000000s_115200s_1041s_520s (CPU Time 0h:00m:00s, Memory Used current: 112MB peak: 113MB)
@N:<a href="@N:CG364:@XP_HELP">CG364</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\uart_tx.v:16:7:16:14:@N:CG364:@XP_MSG">uart_tx.v(16)</a><!@TM:**********> | Synthesizing module uart_tx in library work.
@N:<a href="@N:CG179:@XP_HELP">CG179</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\uart_tx.v:53:12:53:17:@N:CG179:@XP_MSG">uart_tx.v(53)</a><!@TM:**********> | Removing redundant assignment.
@N:<a href="@N:CG179:@XP_HELP">CG179</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\uart_tx.v:54:13:54:20:@N:CG179:@XP_MSG">uart_tx.v(54)</a><!@TM:**********> | Removing redundant assignment.
@N:<a href="@N:CG179:@XP_HELP">CG179</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\uart_tx.v:55:17:55:28:@N:CG179:@XP_MSG">uart_tx.v(55)</a><!@TM:**********> | Removing redundant assignment.
Running optimization stage 1 on uart_tx .......
<font color=#A52A2A>@W:<a href="@W:CL169:@XP_HELP">CL169</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\uart_tx.v:69:0:69:6:@W:CL169:@XP_MSG">uart_tx.v(69)</a><!@TM:**********> | Pruning unused register odd_bit. Make sure that there are no unused intermediate registers.</font>
Finished optimization stage 1 on uart_tx (CPU Time 0h:00m:00s, Memory Used current: 112MB peak: 113MB)
@N:<a href="@N:CG364:@XP_HELP">CG364</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Ctrl_Data.v:15:7:15:16:@N:CG364:@XP_MSG">Ctrl_Data.v(15)</a><!@TM:**********> | Synthesizing module Ctrl_Data in library work.
@N:<a href="@N:CG179:@XP_HELP">CG179</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Ctrl_Data.v:64:53:64:64:@N:CG179:@XP_MSG">Ctrl_Data.v(64)</a><!@TM:**********> | Removing redundant assignment.
@N:<a href="@N:CG179:@XP_HELP">CG179</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Ctrl_Data.v:69:57:69:70:@N:CG179:@XP_MSG">Ctrl_Data.v(69)</a><!@TM:**********> | Removing redundant assignment.
@N:<a href="@N:CG179:@XP_HELP">CG179</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Ctrl_Data.v:85:14:85:22:@N:CG179:@XP_MSG">Ctrl_Data.v(85)</a><!@TM:**********> | Removing redundant assignment.
@N:<a href="@N:CG179:@XP_HELP">CG179</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Ctrl_Data.v:117:24:117:34:@N:CG179:@XP_MSG">Ctrl_Data.v(117)</a><!@TM:**********> | Removing redundant assignment.
@N:<a href="@N:CG179:@XP_HELP">CG179</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Ctrl_Data.v:129:22:129:32:@N:CG179:@XP_MSG">Ctrl_Data.v(129)</a><!@TM:**********> | Removing redundant assignment.
@N:<a href="@N:CG179:@XP_HELP">CG179</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Ctrl_Data.v:141:22:141:32:@N:CG179:@XP_MSG">Ctrl_Data.v(141)</a><!@TM:**********> | Removing redundant assignment.
@N:<a href="@N:CG179:@XP_HELP">CG179</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Ctrl_Data.v:153:22:153:32:@N:CG179:@XP_MSG">Ctrl_Data.v(153)</a><!@TM:**********> | Removing redundant assignment.
@N:<a href="@N:CG179:@XP_HELP">CG179</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Ctrl_Data.v:165:22:165:32:@N:CG179:@XP_MSG">Ctrl_Data.v(165)</a><!@TM:**********> | Removing redundant assignment.
@N:<a href="@N:CG179:@XP_HELP">CG179</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Ctrl_Data.v:177:22:177:32:@N:CG179:@XP_MSG">Ctrl_Data.v(177)</a><!@TM:**********> | Removing redundant assignment.
@N:<a href="@N:CG179:@XP_HELP">CG179</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Ctrl_Data.v:189:22:189:32:@N:CG179:@XP_MSG">Ctrl_Data.v(189)</a><!@TM:**********> | Removing redundant assignment.
@N:<a href="@N:CG179:@XP_HELP">CG179</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Ctrl_Data.v:201:22:201:32:@N:CG179:@XP_MSG">Ctrl_Data.v(201)</a><!@TM:**********> | Removing redundant assignment.
Running optimization stage 1 on Ctrl_Data .......
Finished optimization stage 1 on Ctrl_Data (CPU Time 0h:00m:00s, Memory Used current: 112MB peak: 113MB)
@N:<a href="@N:CG364:@XP_HELP">CG364</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\UART_Control.v:16:7:16:19:@N:CG364:@XP_MSG">UART_Control.v(16)</a><!@TM:**********> | Synthesizing module UART_Control in library work.

	iWID_RS422=32'b00000000000000000000000000100000
	RX_SYS_FREQ=32'b00000111001001110000111000000000
	RX_UART_BAUD=32'b00000000000000011100001000000000
	TX_SYS_FREQ=32'b00000111001001110000111000000000
	TX_UART_BAUD=32'b00000000000000011100001000000000
   Generated name = UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1
<font color=#A52A2A>@W:<a href="@W:CG360:@XP_HELP">CG360</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\UART_Control.v:31:16:31:23:@W:CG360:@XP_MSG">UART_Control.v(31)</a><!@TM:**********> | Removing wire rx_data, as there is no assignment to it.</font>
Running optimization stage 1 on UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1 .......
<font color=#A52A2A>@W:<a href="@W:CL318:@XP_HELP">CL318</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\UART_Control.v:31:16:31:23:@W:CL318:@XP_MSG">UART_Control.v(31)</a><!@TM:**********> | *Output rx_data has undriven bits; assigning undriven bits to 'Z'.  Simulation mismatch possible. Assign all bits of the output.</font>
Finished optimization stage 1 on UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1 (CPU Time 0h:00m:00s, Memory Used current: 112MB peak: 113MB)
@N:<a href="@N:CG364:@XP_HELP">CG364</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\SignalGenerator.v:42:7:42:22:@N:CG364:@XP_MSG">SignalGenerator.v(42)</a><!@TM:**********> | Synthesizing module SignalGenerator in library work.

	iWID_TRANS=32'b00000000000000000000000000001101
	iTRANSIT_TIME=32'b00000000000000000000000010110110
	iAD_VALID_START=32'b00000000000000000000000001000110
	iDA=32'b00000000000000000000000000000011
	iAD_VALID_END=32'b00000000000000000000000010100110
	iDEMODU=32'b00000000000000000000000010101001
	iINTEGRATE=32'b00000000000000000000000010101101
	iMODU=32'b00000000000000000000000010110000
   Generated name = SignalGenerator_13s_182s_70s_3s_166s_169s_173s_176s
@N:<a href="@N:CG179:@XP_HELP">CG179</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\SignalGenerator.v:80:15:80:26:@N:CG179:@XP_MSG">SignalGenerator.v(80)</a><!@TM:**********> | Removing redundant assignment.
<font color=#A52A2A>@W:<a href="@W:CG133:@XP_HELP">CG133</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\SignalGenerator.v:56:16:56:28:@W:CG133:@XP_MSG">SignalGenerator.v(56)</a><!@TM:**********> | Object output_drive is declared but not assigned. Either assign a value or remove the declaration.</font>
Running optimization stage 1 on SignalGenerator_13s_182s_70s_3s_166s_169s_173s_176s .......
Finished optimization stage 1 on SignalGenerator_13s_182s_70s_3s_166s_169s_173s_176s (CPU Time 0h:00m:00s, Memory Used current: 112MB peak: 113MB)
@N:<a href="@N:CG364:@XP_HELP">CG364</a> : <a href="D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v:25:7:25:11:@N:CG364:@XP_MSG">ecp5u.v(25)</a><!@TM:**********> | Synthesizing module AND2 in library work.
Running optimization stage 1 on AND2 .......
Finished optimization stage 1 on AND2 (CPU Time 0h:00m:00s, Memory Used current: 113MB peak: 113MB)
@N:<a href="@N:CG364:@XP_HELP">CG364</a> : <a href="D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v:367:7:367:10:@N:CG364:@XP_MSG">ecp5u.v(367)</a><!@TM:**********> | Synthesizing module INV in library work.
Running optimization stage 1 on INV .......
Finished optimization stage 1 on INV (CPU Time 0h:00m:00s, Memory Used current: 113MB peak: 113MB)
@N:<a href="@N:CG364:@XP_HELP">CG364</a> : <a href="D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v:810:7:810:11:@N:CG364:@XP_MSG">ecp5u.v(810)</a><!@TM:**********> | Synthesizing module XOR2 in library work.
Running optimization stage 1 on XOR2 .......
Finished optimization stage 1 on XOR2 (CPU Time 0h:00m:00s, Memory Used current: 113MB peak: 113MB)
@N:<a href="@N:CG364:@XP_HELP">CG364</a> : <a href="D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v:710:7:710:15:@N:CG364:@XP_MSG">ecp5u.v(710)</a><!@TM:**********> | Synthesizing module ROM16X1A in library work.
Running optimization stage 1 on ROM16X1A .......
Finished optimization stage 1 on ROM16X1A (CPU Time 0h:00m:00s, Memory Used current: 113MB peak: 113MB)
@N:<a href="@N:CG364:@XP_HELP">CG364</a> : <a href="D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v:959:7:959:15:@N:CG364:@XP_MSG">ecp5u.v(959)</a><!@TM:**********> | Synthesizing module PDPW16KD in library work.
Running optimization stage 1 on PDPW16KD .......
Finished optimization stage 1 on PDPW16KD (CPU Time 0h:00m:00s, Memory Used current: 113MB peak: 113MB)
@N:<a href="@N:CG364:@XP_HELP">CG364</a> : <a href="D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v:119:7:119:14:@N:CG364:@XP_MSG">ecp5u.v(119)</a><!@TM:**********> | Synthesizing module FD1P3DX in library work.
Running optimization stage 1 on FD1P3DX .......
Finished optimization stage 1 on FD1P3DX (CPU Time 0h:00m:00s, Memory Used current: 113MB peak: 113MB)
@N:<a href="@N:CG364:@XP_HELP">CG364</a> : <a href="D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v:160:7:160:14:@N:CG364:@XP_MSG">ecp5u.v(160)</a><!@TM:**********> | Synthesizing module FD1S3BX in library work.
Running optimization stage 1 on FD1S3BX .......
Finished optimization stage 1 on FD1S3BX (CPU Time 0h:00m:00s, Memory Used current: 113MB peak: 113MB)
@N:<a href="@N:CG364:@XP_HELP">CG364</a> : <a href="D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v:168:7:168:14:@N:CG364:@XP_MSG">ecp5u.v(168)</a><!@TM:**********> | Synthesizing module FD1S3DX in library work.
Running optimization stage 1 on FD1S3DX .......
Finished optimization stage 1 on FD1S3DX (CPU Time 0h:00m:00s, Memory Used current: 113MB peak: 113MB)
@N:<a href="@N:CG364:@XP_HELP">CG364</a> : <a href="D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v:76:7:76:12:@N:CG364:@XP_MSG">ecp5u.v(76)</a><!@TM:**********> | Synthesizing module CCU2C in library work.
Running optimization stage 1 on CCU2C .......
Finished optimization stage 1 on CCU2C (CPU Time 0h:00m:00s, Memory Used current: 113MB peak: 113MB)
@N:<a href="@N:CG364:@XP_HELP">CG364</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v:8:7:8:21:@N:CG364:@XP_MSG">Asys_fifo56X16.v(8)</a><!@TM:**********> | Synthesizing module Asys_fifo56X16 in library work.
Running optimization stage 1 on Asys_fifo56X16 .......
<font color=#A52A2A>@W:<a href="@W:CL168:@XP_HELP">CL168</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v:177:8:177:13:@W:CL168:@XP_MSG">Asys_fifo56X16.v(177)</a><!@TM:**********> | Removing instance INV_1 because it does not drive other instances. To preserve this instance, use the syn_noprune synthesis directive.</font>
<font color=#A52A2A>@W:<a href="@W:CL168:@XP_HELP">CL168</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v:171:8:171:13:@W:CL168:@XP_MSG">Asys_fifo56X16.v(171)</a><!@TM:**********> | Removing instance INV_4 because it does not drive other instances. To preserve this instance, use the syn_noprune synthesis directive.</font>
<font color=#A52A2A>@W:<a href="@W:CL168:@XP_HELP">CL168</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v:169:9:169:16:@W:CL168:@XP_MSG">Asys_fifo56X16.v(169)</a><!@TM:**********> | Removing instance AND2_t0 because it does not drive other instances. To preserve this instance, use the syn_noprune synthesis directive.</font>
Finished optimization stage 1 on Asys_fifo56X16 (CPU Time 0h:00m:00s, Memory Used current: 113MB peak: 114MB)
@N:<a href="@N:CG364:@XP_HELP">CG364</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Demodulation.v:42:7:42:19:@N:CG364:@XP_MSG">Demodulation.v(42)</a><!@TM:**********> | Synthesizing module Demodulation in library work.

	acum_cnt=32'b00000000000000000000000000101000
	iWID_IN=32'b00000000000000000000000000001100
	iWID_OUT=32'b00000000000000000000000000111000
   Generated name = Demodulation_40s_12s_56s
@N:<a href="@N:CG179:@XP_HELP">CG179</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Demodulation.v:101:17:101:28:@N:CG179:@XP_MSG">Demodulation.v(101)</a><!@TM:**********> | Removing redundant assignment.
@N:<a href="@N:CG179:@XP_HELP">CG179</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Demodulation.v:118:16:118:26:@N:CG179:@XP_MSG">Demodulation.v(118)</a><!@TM:**********> | Removing redundant assignment.
@N:<a href="@N:CG179:@XP_HELP">CG179</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Demodulation.v:130:46:130:62:@N:CG179:@XP_MSG">Demodulation.v(130)</a><!@TM:**********> | Removing redundant assignment.
@N:<a href="@N:CG179:@XP_HELP">CG179</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Demodulation.v:159:59:159:73:@N:CG179:@XP_MSG">Demodulation.v(159)</a><!@TM:**********> | Removing redundant assignment.
@N:<a href="@N:CG179:@XP_HELP">CG179</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Demodulation.v:171:13:171:21:@N:CG179:@XP_MSG">Demodulation.v(171)</a><!@TM:**********> | Removing redundant assignment.
@N:<a href="@N:CG179:@XP_HELP">CG179</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Demodulation.v:175:12:175:20:@N:CG179:@XP_MSG">Demodulation.v(175)</a><!@TM:**********> | Removing redundant assignment.
@N:<a href="@N:CG179:@XP_HELP">CG179</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Demodulation.v:176:16:176:28:@N:CG179:@XP_MSG">Demodulation.v(176)</a><!@TM:**********> | Removing redundant assignment.
Running optimization stage 1 on Demodulation_40s_12s_56s .......
<font color=#A52A2A>@W:<a href="@W:CL169:@XP_HELP">CL169</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Demodulation.v:158:0:158:6:@W:CL169:@XP_MSG">Demodulation.v(158)</a><!@TM:**********> | Pruning unused register sample_sum_DY2[55:0]. Make sure that there are no unused intermediate registers.</font>
<font color=#A52A2A>@W:<a href="@W:CL169:@XP_HELP">CL169</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Demodulation.v:139:0:139:6:@W:CL169:@XP_MSG">Demodulation.v(139)</a><!@TM:**********> | Pruning unused register Read_enDY[1:0]. Make sure that there are no unused intermediate registers.</font>
Finished optimization stage 1 on Demodulation_40s_12s_56s (CPU Time 0h:00m:00s, Memory Used current: 114MB peak: 119MB)
@N:<a href="@N:CG364:@XP_HELP">CG364</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Integration.v:42:7:42:18:@N:CG364:@XP_MSG">Integration.v(42)</a><!@TM:**********> | Synthesizing module Integration in library work.

	iWID_IN=32'b00000000000000000000000000111000
	iWID_OUT=32'b00000000000000000000000000111000
   Generated name = Integration_56s_56s
@N:<a href="@N:CG179:@XP_HELP">CG179</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Integration.v:71:11:71:18:@N:CG179:@XP_MSG">Integration.v(71)</a><!@TM:**********> | Removing redundant assignment.
Running optimization stage 1 on Integration_56s_56s .......
Finished optimization stage 1 on Integration_56s_56s (CPU Time 0h:00m:00s, Memory Used current: 114MB peak: 119MB)
@N:<a href="@N:CG364:@XP_HELP">CG364</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v:42:7:42:17:@N:CG364:@XP_MSG">Modulation.v(42)</a><!@TM:**********> | Synthesizing module Modulation in library work.

	iWID_TRANS=32'b00000000000000000000000000001101
	iWID_IN=32'b00000000000000000000000000111000
	iWID_OUT=32'b00000000000000000000000000001110
	DA_CONSTANT=32'b00000000000000000010111111011010
	iFEEDBACK_SCALE=32'b00000000000000000000000000001010
	wCLOSED=1'b1
	pi_1_2=14'b00111111111111
	c_step=14'b00110110101100
   Generated name = Modulation_13s_56s_14s_12250s_10s_1_4095_3500
@N:<a href="@N:CG179:@XP_HELP">CG179</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v:103:10:103:16:@N:CG179:@XP_MSG">Modulation.v(103)</a><!@TM:**********> | Removing redundant assignment.
@N:<a href="@N:CG179:@XP_HELP">CG179</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v:117:9:117:14:@N:CG179:@XP_MSG">Modulation.v(117)</a><!@TM:**********> | Removing redundant assignment.
@N:<a href="@N:CG179:@XP_HELP">CG179</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v:131:11:131:18:@N:CG179:@XP_MSG">Modulation.v(131)</a><!@TM:**********> | Removing redundant assignment.
@N:<a href="@N:CG179:@XP_HELP">CG179</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v:138:13:138:22:@N:CG179:@XP_MSG">Modulation.v(138)</a><!@TM:**********> | Removing redundant assignment.
@N:<a href="@N:CG179:@XP_HELP">CG179</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v:150:11:150:18:@N:CG179:@XP_MSG">Modulation.v(150)</a><!@TM:**********> | Removing redundant assignment.
<font color=#A52A2A>@W:<a href="@W:CG133:@XP_HELP">CG133</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v:64:19:64:23:@W:CG133:@XP_MSG">Modulation.v(64)</a><!@TM:**********> | Object step is declared but not assigned. Either assign a value or remove the declaration.</font>
<font color=#A52A2A>@W:<a href="@W:CG133:@XP_HELP">CG133</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v:67:20:67:29:@W:CG133:@XP_MSG">Modulation.v(67)</a><!@TM:**********> | Object stair_dy1 is declared but not assigned. Either assign a value or remove the declaration.</font>
Running optimization stage 1 on Modulation_13s_56s_14s_12250s_10s_1_4095_3500 .......
<font color=#A52A2A>@W:<a href="@W:CL169:@XP_HELP">CL169</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v:127:0:127:6:@W:CL169:@XP_MSG">Modulation.v(127)</a><!@TM:**********> | Pruning unused register c_stair[13:0]. Make sure that there are no unused intermediate registers.</font>
<font color=#A52A2A>@W:<a href="@W:CL271:@XP_HELP">CL271</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v:142:0:142:6:@W:CL271:@XP_MSG">Modulation.v(142)</a><!@TM:**********> | Pruning unused bits 13 to 0 of DADY_dout[27:0]. If this is not the intended behavior, drive the inputs with valid values, or inputs from the top level.</font>
<font color=#A52A2A>@W:<a href="@W:CL271:@XP_HELP">CL271</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v:134:0:134:6:@W:CL271:@XP_MSG">Modulation.v(134)</a><!@TM:**********> | Pruning unused bits 13 to 0 of dout_mult[27:0]. If this is not the intended behavior, drive the inputs with valid values, or inputs from the top level.</font>
Finished optimization stage 1 on Modulation_13s_56s_14s_12250s_10s_1_4095_3500 (CPU Time 0h:00m:00s, Memory Used current: 114MB peak: 119MB)
@N:<a href="@N:CG364:@XP_HELP">CG364</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Rs422Output.v:43:7:43:18:@N:CG364:@XP_MSG">Rs422Output.v(43)</a><!@TM:**********> | Synthesizing module Rs422Output in library work.

	bPOLAR=32'b00000000000000000000000000000001
	iWID_IN=32'b00000000000000000000000000111000
	iWID_OUT=32'b00000000000000000000000000100000
	iDELAYED=32'b00000000000000000000000001111000
	iOUTPUT_SCALE=32'b00000000000000000000010101000110
	idle_s=4'b0000
	wait_1us_s=4'b0001
	dalay_state=4'b0010
	check_data_stable_s=4'b0011
	transmit_data_s=4'b0100
	clear_data_s=4'b0101
   Generated name = Rs422Output_Z2
@N:<a href="@N:CG179:@XP_HELP">CG179</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Rs422Output.v:180:14:180:22:@N:CG179:@XP_MSG">Rs422Output.v(180)</a><!@TM:**********> | Removing redundant assignment.
@N:<a href="@N:CG179:@XP_HELP">CG179</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Rs422Output.v:190:9:190:12:@N:CG179:@XP_MSG">Rs422Output.v(190)</a><!@TM:**********> | Removing redundant assignment.
@N:<a href="@N:CG179:@XP_HELP">CG179</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Rs422Output.v:195:38:195:44:@N:CG179:@XP_MSG">Rs422Output.v(195)</a><!@TM:**********> | Removing redundant assignment.
@N:<a href="@N:CG179:@XP_HELP">CG179</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Rs422Output.v:204:11:204:16:@N:CG179:@XP_MSG">Rs422Output.v(204)</a><!@TM:**********> | Removing redundant assignment.
@N:<a href="@N:CG179:@XP_HELP">CG179</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Rs422Output.v:221:13:221:20:@N:CG179:@XP_MSG">Rs422Output.v(221)</a><!@TM:**********> | Removing redundant assignment.
Running optimization stage 1 on Rs422Output_Z2 .......
<font color=#A52A2A>@W:<a href="@W:CL169:@XP_HELP">CL169</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Rs422Output.v:194:0:194:6:@W:CL169:@XP_MSG">Rs422Output.v(194)</a><!@TM:**********> | Pruning unused register sum_dy[55:0]. Make sure that there are no unused intermediate registers.</font>
<font color=#A52A2A>@W:<a href="@W:CL271:@XP_HELP">CL271</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Rs422Output.v:208:0:208:6:@W:CL271:@XP_MSG">Rs422Output.v(208)</a><!@TM:**********> | Pruning unused bits 79 to 56 of p_sum_dy[79:0]. If this is not the intended behavior, drive the inputs with valid values, or inputs from the top level.</font>
<font color=#A52A2A>@W:<a href="@W:CL271:@XP_HELP">CL271</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Rs422Output.v:208:0:208:6:@W:CL271:@XP_MSG">Rs422Output.v(208)</a><!@TM:**********> | Pruning unused bits 23 to 0 of p_sum_dy[79:0]. If this is not the intended behavior, drive the inputs with valid values, or inputs from the top level.</font>
<font color=#A52A2A>@W:<a href="@W:CL271:@XP_HELP">CL271</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Rs422Output.v:198:0:198:6:@W:CL271:@XP_MSG">Rs422Output.v(198)</a><!@TM:**********> | Pruning unused bits 79 to 56 of p_sum[79:0]. If this is not the intended behavior, drive the inputs with valid values, or inputs from the top level.</font>
<font color=#A52A2A>@W:<a href="@W:CL271:@XP_HELP">CL271</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Rs422Output.v:198:0:198:6:@W:CL271:@XP_MSG">Rs422Output.v(198)</a><!@TM:**********> | Pruning unused bits 23 to 0 of p_sum[79:0]. If this is not the intended behavior, drive the inputs with valid values, or inputs from the top level.</font>
Finished optimization stage 1 on Rs422Output_Z2 (CPU Time 0h:00m:00s, Memory Used current: 114MB peak: 119MB)
@N:<a href="@N:CG364:@XP_HELP">CG364</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\SquareWaveGenerator.v:16:7:16:26:@N:CG364:@XP_MSG">SquareWaveGenerator.v(16)</a><!@TM:**********> | Synthesizing module SquareWaveGenerator in library work.

	iTRANSMIT_COFF=32'b00000000000010010010011111000000
   Generated name = SquareWaveGenerator_600000s
@N:<a href="@N:CG179:@XP_HELP">CG179</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\SquareWaveGenerator.v:46:11:46:18:@N:CG179:@XP_MSG">SquareWaveGenerator.v(46)</a><!@TM:**********> | Removing redundant assignment.
Running optimization stage 1 on SquareWaveGenerator_600000s .......
Finished optimization stage 1 on SquareWaveGenerator_600000s (CPU Time 0h:00m:00s, Memory Used current: 114MB peak: 119MB)
@N:<a href="@N:CG364:@XP_HELP">CG364</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\SignalProcessing.v:41:7:41:23:@N:CG364:@XP_MSG">SignalProcessing.v(41)</a><!@TM:**********> | Synthesizing module SignalProcessing in library work.

	acum_cnt=32'b00000000000000000000000000101000
	iWID_TRANS=32'b00000000000000000000000000001101
	iWID_AD=32'b00000000000000000000000000001100
	iWID_DA=32'b00000000000000000000000000001110
	iWID_RS422=32'b00000000000000000000000000100000
	iWID_SIGN=32'b00000000000000000000000000000101
	wCLOSED=1'b1
	iTRANSIT_TIME=32'b00000000000000000000000010110110
	iAD_VALID_START=32'b00000000000000000000000001000110
	iFEEDBACK_SCALE=32'b00000000000000000000000000001010
	bPOLAR=32'b00000000000000000000000000000001
	iOUTPUT_SCALE=32'b00000000000000000000010101000110
	iDELAYED=32'b00000000000000000000000001111000
	DA_CONSTANT=32'b00000000000000000010111111011010
	iTRANSMIT_COFF=32'b00000000000010010010011111000000
	iWID_PROC=32'b00000000000000000000000000111000
   Generated name = SignalProcessing_Z3
Running optimization stage 1 on SignalProcessing_Z3 .......
Finished optimization stage 1 on SignalProcessing_Z3 (CPU Time 0h:00m:00s, Memory Used current: 114MB peak: 119MB)
@N:<a href="@N:CG364:@XP_HELP">CG364</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\INS350_5J_JZ.v:17:7:17:19:@N:CG364:@XP_MSG">INS350_5J_JZ.v(17)</a><!@TM:**********> | Synthesizing module INS350_5J_JZ in library work.
<font color=#A52A2A>@W:<a href="@W:CG360:@XP_HELP">CG360</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\INS350_5J_JZ.v:54:9:54:20:@W:CG360:@XP_MSG">INS350_5J_JZ.v(54)</a><!@TM:**********> | Removing wire TxTransmitt, as there is no assignment to it.</font>
<font color=#A52A2A>@W:<a href="@W:CG360:@XP_HELP">CG360</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\INS350_5J_JZ.v:61:10:61:19:@W:CG360:@XP_MSG">INS350_5J_JZ.v(61)</a><!@TM:**********> | Removing wire CLKFX_OUT, as there is no assignment to it.</font>
Running optimization stage 1 on INS350_5J_JZ .......
Finished optimization stage 1 on INS350_5J_JZ (CPU Time 0h:00m:00s, Memory Used current: 114MB peak: 119MB)
Running optimization stage 2 on INS350_5J_JZ .......
@N:<a href="@N:CL159:@XP_HELP">CL159</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\INS350_5J_JZ.v:22:9:22:19:@N:CL159:@XP_MSG">INS350_5J_JZ.v(22)</a><!@TM:**********> | Input RxTransmit is unused.
Finished optimization stage 2 on INS350_5J_JZ (CPU Time 0h:00m:00s, Memory Used current: 113MB peak: 119MB)
Running optimization stage 2 on SignalProcessing_Z3 .......
Finished optimization stage 2 on SignalProcessing_Z3 (CPU Time 0h:00m:00s, Memory Used current: 113MB peak: 119MB)
Running optimization stage 2 on SquareWaveGenerator_600000s .......
Finished optimization stage 2 on SquareWaveGenerator_600000s (CPU Time 0h:00m:00s, Memory Used current: 114MB peak: 119MB)
Running optimization stage 2 on Rs422Output_Z2 .......
@N:<a href="@N:CL201:@XP_HELP">CL201</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Rs422Output.v:93:0:93:6:@N:CL201:@XP_MSG">Rs422Output.v(93)</a><!@TM:**********> | Trying to extract state machine for register trans_state.
Extracted state machine for register trans_state
State machine has 6 reachable states with original encodings of:
   0000
   0001
   0010
   0011
   0100
   0101
<font color=#A52A2A>@W:<a href="@W:CL247:@XP_HELP">CL247</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Rs422Output.v:57:22:57:25:@W:CL247:@XP_MSG">Rs422Output.v(57)</a><!@TM:**********> | Input port bit 55 of din[55:0] is unused</font>

Finished optimization stage 2 on Rs422Output_Z2 (CPU Time 0h:00m:00s, Memory Used current: 116MB peak: 119MB)
Running optimization stage 2 on Modulation_13s_56s_14s_12250s_10s_1_4095_3500 .......
@N:<a href="@N:CL189:@XP_HELP">CL189</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v:96:0:96:6:@N:CL189:@XP_MSG">Modulation.v(96)</a><!@TM:**********> | Register bit square[12] is always 0.
@N:<a href="@N:CL189:@XP_HELP">CL189</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v:96:0:96:6:@N:CL189:@XP_MSG">Modulation.v(96)</a><!@TM:**********> | Register bit square[13] is always 0.
<font color=#A52A2A>@W:<a href="@W:CL279:@XP_HELP">CL279</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v:96:0:96:6:@W:CL279:@XP_MSG">Modulation.v(96)</a><!@TM:**********> | Pruning register bits 13 to 12 of square[13:0]. If this is not the intended behavior, drive the inputs with valid values, or inputs from the top level.</font>
@N:<a href="@N:CL189:@XP_HELP">CL189</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v:107:0:107:6:@N:CL189:@XP_MSG">Modulation.v(107)</a><!@TM:**********> | Register bit square_dy[13] is always 0.
@N:<a href="@N:CL189:@XP_HELP">CL189</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v:107:0:107:6:@N:CL189:@XP_MSG">Modulation.v(107)</a><!@TM:**********> | Register bit square_dy[12] is always 0.
<font color=#A52A2A>@W:<a href="@W:CL279:@XP_HELP">CL279</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v:107:0:107:6:@W:CL279:@XP_MSG">Modulation.v(107)</a><!@TM:**********> | Pruning register bits 13 to 12 of square_dy[13:0]. If this is not the intended behavior, drive the inputs with valid values, or inputs from the top level.</font>
<font color=#A52A2A>@W:<a href="@W:CL279:@XP_HELP">CL279</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v:96:0:96:6:@W:CL279:@XP_MSG">Modulation.v(96)</a><!@TM:**********> | Pruning register bits 11 to 1 of square[11:0]. If this is not the intended behavior, drive the inputs with valid values, or inputs from the top level.</font>
<font color=#A52A2A>@W:<a href="@W:CL279:@XP_HELP">CL279</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v:107:0:107:6:@W:CL279:@XP_MSG">Modulation.v(107)</a><!@TM:**********> | Pruning register bits 11 to 1 of square_dy[11:0]. If this is not the intended behavior, drive the inputs with valid values, or inputs from the top level.</font>
<font color=#A52A2A>@W:<a href="@W:CL246:@XP_HELP">CL246</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v:56:28:56:31:@W:CL246:@XP_MSG">Modulation.v(56)</a><!@TM:**********> | Input port bits 55 to 24 of din[55:0] are unused. Assign logic for all port bits or change the input port size.</font>
@N:<a href="@N:CL159:@XP_HELP">CL159</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Modulation.v:52:12:52:17:@N:CL159:@XP_MSG">Modulation.v(52)</a><!@TM:**********> | Input rst_n is unused.
Finished optimization stage 2 on Modulation_13s_56s_14s_12250s_10s_1_4095_3500 (CPU Time 0h:00m:00s, Memory Used current: 116MB peak: 119MB)
Running optimization stage 2 on Integration_56s_56s .......
@N:<a href="@N:CL159:@XP_HELP">CL159</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Integration.v:48:14:48:19:@N:CL159:@XP_MSG">Integration.v(48)</a><!@TM:**********> | Input rst_n is unused.
Finished optimization stage 2 on Integration_56s_56s (CPU Time 0h:00m:00s, Memory Used current: 116MB peak: 119MB)
Running optimization stage 2 on Demodulation_40s_12s_56s .......
Finished optimization stage 2 on Demodulation_40s_12s_56s (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)
Running optimization stage 2 on Asys_fifo56X16 .......
Finished optimization stage 2 on Asys_fifo56X16 (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)
Running optimization stage 2 on CCU2C .......
Finished optimization stage 2 on CCU2C (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)
Running optimization stage 2 on FD1S3DX .......
Finished optimization stage 2 on FD1S3DX (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)
Running optimization stage 2 on FD1S3BX .......
Finished optimization stage 2 on FD1S3BX (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)
Running optimization stage 2 on FD1P3DX .......
Finished optimization stage 2 on FD1P3DX (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)
Running optimization stage 2 on PDPW16KD .......
Finished optimization stage 2 on PDPW16KD (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)
Running optimization stage 2 on ROM16X1A .......
Finished optimization stage 2 on ROM16X1A (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)
Running optimization stage 2 on XOR2 .......
Finished optimization stage 2 on XOR2 (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)
Running optimization stage 2 on INV .......
Finished optimization stage 2 on INV (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)
Running optimization stage 2 on AND2 .......
Finished optimization stage 2 on AND2 (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)
Running optimization stage 2 on SignalGenerator_13s_182s_70s_3s_166s_169s_173s_176s .......
@A:<a href="@A:CL153:@XP_HELP">CL153</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\SignalGenerator.v:56:16:56:28:@A:CL153:@XP_MSG">SignalGenerator.v(56)</a><!@TM:**********> | *Unassigned bits of output_drive are referenced and tied to 0 -- simulation mismatch possible.
Finished optimization stage 2 on SignalGenerator_13s_182s_70s_3s_166s_169s_173s_176s (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)
Running optimization stage 2 on UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1 .......
@N:<a href="@N:CL159:@XP_HELP">CL159</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\UART_Control.v:33:11:33:14:@N:CL159:@XP_MSG">UART_Control.v(33)</a><!@TM:**********> | Input RXD is unused.
Finished optimization stage 2 on UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1 (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)
Running optimization stage 2 on Ctrl_Data .......
@N:<a href="@N:CL201:@XP_HELP">CL201</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Ctrl_Data.v:89:0:89:6:@N:CL201:@XP_MSG">Ctrl_Data.v(89)</a><!@TM:**********> | Trying to extract state machine for register tx_state.
Extracted state machine for register tx_state
State machine has 11 reachable states with original encodings of:
   0000
   0001
   0010
   0011
   0100
   0101
   0110
   0111
   1000
   1001
   1010
@N:<a href="@N:CL159:@XP_HELP">CL159</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\Ctrl_Data.v:22:15:22:22:@N:CL159:@XP_MSG">Ctrl_Data.v(22)</a><!@TM:**********> | Input rd_done is unused.
Finished optimization stage 2 on Ctrl_Data (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)
Running optimization stage 2 on uart_tx .......
Finished optimization stage 2 on uart_tx (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)
Running optimization stage 2 on speed_select_Tx_120000000s_115200s_1041s_520s .......
Finished optimization stage 2 on speed_select_Tx_120000000s_115200s_1041s_520s (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)
Running optimization stage 2 on DS18B20 .......
@N:<a href="@N:CL201:@XP_HELP">CL201</a> : <a href="D:\Project\TLH50_03J_JZ_20250925\Src_al\Src_al\DS18B20.v:93:0:93:6:@N:CL201:@XP_MSG">DS18B20.v(93)</a><!@TM:**********> | Trying to extract state machine for register cur_state.
Extracted state machine for register cur_state
State machine has 6 reachable states with original encodings of:
   000001
   000010
   000100
   001000
   010000
   100000
Finished optimization stage 2 on DS18B20 (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)
Running optimization stage 2 on global_clock .......
Finished optimization stage 2 on global_clock (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)
Running optimization stage 2 on EHXPLLL .......
Finished optimization stage 2 on EHXPLLL (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)
Running optimization stage 2 on VLO .......
Finished optimization stage 2 on VLO (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)
Running optimization stage 2 on VHI .......
Finished optimization stage 2 on VHI (CPU Time 0h:00m:00s, Memory Used current: 118MB peak: 120MB)

For a summary of runtime and memory usage per design unit, please see file:
==========================================================
Linked File:  <a href="D:\Project\TLH50_03J_JZ_20250925\impl1\synwork\layer0.rt.csv:@XP_FILE">layer0.rt.csv</a>


At c_ver Exit (Real Time elapsed 0h:00m:04s; CPU Time elapsed 0h:00m:04s; Memory used current: 117MB peak: 120MB)

Process took 0h:00m:04s realtime, 0h:00m:04s cputime

Process completed successfully.
# Thu Sep 25 10:29:22 2025

###########################################################]
###########################################################[

Copyright (C) 1994-2021 Synopsys, Inc.
This Synopsys software and all associated documentation are proprietary to Synopsys, Inc.
and may only be used pursuant to the terms and conditions of a written license agreement
with Synopsys, Inc. All other use, reproduction, modification, or distribution of the
Synopsys software or the associated documentation is strictly prohibited.
Tool: Synplify Pro (R)
Build: R-2021.03L-SP1-1
Install: D:\Software\lscc\diamond\3.12\synpbase
OS: Windows 6.2

Hostname: TLH-022

Implementation : impl1
<a name=compilerReport3></a>Synopsys Synopsys Netlist Linker, Version comp202103synp2, Build 222R, Built Dec 22 2021 00:18:12, @</a>

@N: : <!@TM:**********> | Running in 64-bit mode 

At syn_nfilter Exit (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 106MB peak: 107MB)

Process took 0h:00m:01s realtime, 0h:00m:01s cputime

Process completed successfully.
# Thu Sep 25 10:29:23 2025

###########################################################]

For a summary of runtime and memory usage for all design units, please see file:
==========================================================
Linked File:  <a href="D:\Project\TLH50_03J_JZ_20250925\impl1\synwork\INS350_5J_JZ_impl1_comp.rt.csv:@XP_FILE">INS350_5J_JZ_impl1_comp.rt.csv</a>

@END

At c_hdl Exit (Real Time elapsed 0h:00m:04s; CPU Time elapsed 0h:00m:04s; Memory used current: 38MB peak: 47MB)

Process took 0h:00m:04s realtime, 0h:00m:04s cputime

Process completed successfully.
# Thu Sep 25 10:29:23 2025

###########################################################]

</pre></samp></body></html>
<html><body><samp><pre>
<!@TC:**********>
###########################################################[

Copyright (C) 1994-2021 Synopsys, Inc.
This Synopsys software and all associated documentation are proprietary to Synopsys, Inc.
and may only be used pursuant to the terms and conditions of a written license agreement
with Synopsys, Inc. All other use, reproduction, modification, or distribution of the
Synopsys software or the associated documentation is strictly prohibited.
Tool: Synplify Pro (R)
Build: R-2021.03L-SP1-1
Install: D:\Software\lscc\diamond\3.12\synpbase
OS: Windows 6.2

Hostname: TLH-022

Implementation : impl1
<a name=compilerReport4></a>Synopsys Synopsys Netlist Linker, Version comp202103synp2, Build 222R, Built Dec 22 2021 00:18:12, @</a>

@N: : <!@TM:**********> | Running in 64-bit mode 
File D:\Project\TLH50_03J_JZ\impl1\synwork\INS350_5J_JZ_impl1_comp.srs changed - recompiling

At syn_nfilter Exit (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 107MB peak: 107MB)

Process took 0h:00m:01s realtime, 0h:00m:01s cputime

Process completed successfully.
# Thu Sep 25 10:29:24 2025

###########################################################]

</pre></samp></body></html>
<html><body><samp><pre>
<!@TC:**********>
# Thu Sep 25 10:29:25 2025


Copyright (C) 1994-2021 Synopsys, Inc.
This Synopsys software and all associated documentation are proprietary to Synopsys, Inc.
and may only be used pursuant to the terms and conditions of a written license agreement
with Synopsys, Inc. All other use, reproduction, modification, or distribution of the
Synopsys software or the associated documentation is strictly prohibited.
Tool: Synplify Pro (R)
Build: R-2021.03L-SP1-1
Install: D:\Software\lscc\diamond\3.12\synpbase
OS: Windows 6.2

Hostname: TLH-022

Implementation : impl1
<a name=mapperReport5></a>Synopsys Lattice Technology Pre-mapping, Version map202103lat, Build 107R, Built Dec 22 2021 00:40:26, @</a>


Mapper Startup Complete (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 132MB peak: 132MB)


Done reading skeleton netlist (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 137MB peak: 144MB)

@A:<a href="@A:MF827:@XP_HELP">MF827</a> : <!@TM:**********> | No constraint file specified. 
Linked File:  <a href="D:\Project\TLH50_03J_JZ_20250925\impl1\INS350_5J_JZ_impl1_scck.rpt:@XP_FILE">INS350_5J_JZ_impl1_scck.rpt</a>
See clock summary report "D:\Project\TLH50_03J_JZ_20250925\impl1\INS350_5J_JZ_impl1_scck.rpt"
@N:<a href="@N:MF916:@XP_HELP">MF916</a> : <!@TM:**********> | Option synthesis_strategy=base is enabled.  
@N:<a href="@N:MF248:@XP_HELP">MF248</a> : <!@TM:**********> | Running in 64-bit mode. 
@N:<a href="@N:MF666:@XP_HELP">MF666</a> : <!@TM:**********> | Clock conversion enabled. (Command "set_option -fix_gated_and_generated_clocks 1" in the project file.) 

Design Input Complete (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 143MB peak: 144MB)


Mapper Initialization Complete (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 143MB peak: 144MB)


Start loading timing files (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 155MB peak: 155MB)


Finished loading timing files (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 155MB peak: 157MB)

<font color=#A52A2A>@W:<a href="@W:BN132:@XP_HELP">BN132</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\src_al\uart_tx.v:33:0:33:6:@W:BN132:@XP_MSG">uart_tx.v(33)</a><!@TM:**********> | Removing sequential instance u_uart.U1.bps_start_r because it is equivalent to instance u_uart.U1.tx_en. To keep the instance, apply constraint syn_preserve=1 on the instance.</font>
@N:<a href="@N:MO111:@XP_HELP">MO111</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\src_al\uart_control.v:31:16:31:23:@N:MO111:@XP_MSG">uart_control.v(31)</a><!@TM:**********> | Tristate driver rx_data_1 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) on net rx_data_1 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) has its enable tied to GND.
@N:<a href="@N:MO111:@XP_HELP">MO111</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\src_al\uart_control.v:31:16:31:23:@N:MO111:@XP_MSG">uart_control.v(31)</a><!@TM:**********> | Tristate driver rx_data_2 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) on net rx_data_2 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) has its enable tied to GND.
@N:<a href="@N:MO111:@XP_HELP">MO111</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\src_al\uart_control.v:31:16:31:23:@N:MO111:@XP_MSG">uart_control.v(31)</a><!@TM:**********> | Tristate driver rx_data_3 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) on net rx_data_3 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) has its enable tied to GND.
@N:<a href="@N:MO111:@XP_HELP">MO111</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\src_al\uart_control.v:31:16:31:23:@N:MO111:@XP_MSG">uart_control.v(31)</a><!@TM:**********> | Tristate driver rx_data_4 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) on net rx_data_4 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) has its enable tied to GND.
@N:<a href="@N:MO111:@XP_HELP">MO111</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\src_al\uart_control.v:31:16:31:23:@N:MO111:@XP_MSG">uart_control.v(31)</a><!@TM:**********> | Tristate driver rx_data_5 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) on net rx_data_5 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) has its enable tied to GND.
@N:<a href="@N:MO111:@XP_HELP">MO111</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\src_al\uart_control.v:31:16:31:23:@N:MO111:@XP_MSG">uart_control.v(31)</a><!@TM:**********> | Tristate driver rx_data_6 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) on net rx_data_6 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) has its enable tied to GND.
@N:<a href="@N:MO111:@XP_HELP">MO111</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\src_al\uart_control.v:31:16:31:23:@N:MO111:@XP_MSG">uart_control.v(31)</a><!@TM:**********> | Tristate driver rx_data_7 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) on net rx_data_7 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) has its enable tied to GND.
@N:<a href="@N:MO111:@XP_HELP">MO111</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\src_al\uart_control.v:31:16:31:23:@N:MO111:@XP_MSG">uart_control.v(31)</a><!@TM:**********> | Tristate driver rx_data_8 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) on net rx_data_8 (in view: work.UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1(verilog)) has its enable tied to GND.
@N:<a href="@N:BN362:@XP_HELP">BN362</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\src_al\ds18b20.v:157:0:157:6:@N:BN362:@XP_MSG">ds18b20.v(157)</a><!@TM:**********> | Removing sequential instance rd_done (in view: work.DS18B20(verilog)) of type view:PrimLib.dffre(prim) because it does not drive other instances.
<font color=#A52A2A>@W:<a href="@W:BN114:@XP_HELP">BN114</a> : <a href="d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v:182:13:182:19:@W:BN114:@XP_MSG">asys_fifo56x16.v(182)</a><!@TM:**********> | Removing instance LUT4_1 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.ROM16X1A(PRIM) because it does not drive other instances.</font>
<font color=#A52A2A>@W:<a href="@W:BN114:@XP_HELP">BN114</a> : <a href="d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v:186:13:186:19:@W:BN114:@XP_MSG">asys_fifo56x16.v(186)</a><!@TM:**********> | Removing instance LUT4_0 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.ROM16X1A(PRIM) because it does not drive other instances.</font>
@N:<a href="@N:BN362:@XP_HELP">BN362</a> : <a href="d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v:350:12:350:16:@N:BN362:@XP_MSG">asys_fifo56x16.v(350)</a><!@TM:**********> | Removing sequential instance FF_1 (in view: work.Asys_fifo56X16(verilog)) of type view:LUCENT.FD1S3BX(PRIM) because it does not drive other instances.
@N:<a href="@N:BN362:@XP_HELP">BN362</a> : <a href="d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v:353:12:353:16:@N:BN362:@XP_MSG">asys_fifo56x16.v(353)</a><!@TM:**********> | Removing sequential instance FF_0 (in view: work.Asys_fifo56X16(verilog)) of type view:LUCENT.FD1S3DX(PRIM) because it does not drive other instances.
<font color=#A52A2A>@W:<a href="@W:BN114:@XP_HELP">BN114</a> : <a href="d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v:576:10:576:25:@W:BN114:@XP_MSG">asys_fifo56x16.v(576)</a><!@TM:**********> | Removing instance ae_set_cmp_ci_a (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.</font>
<font color=#A52A2A>@W:<a href="@W:BN114:@XP_HELP">BN114</a> : <a href="d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v:584:10:584:22:@W:BN114:@XP_MSG">asys_fifo56x16.v(584)</a><!@TM:**********> | Removing instance ae_set_cmp_0 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.</font>
<font color=#A52A2A>@W:<a href="@W:BN114:@XP_HELP">BN114</a> : <a href="d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v:592:10:592:22:@W:BN114:@XP_MSG">asys_fifo56x16.v(592)</a><!@TM:**********> | Removing instance ae_set_cmp_1 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.</font>
<font color=#A52A2A>@W:<a href="@W:BN114:@XP_HELP">BN114</a> : <a href="d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v:600:10:600:22:@W:BN114:@XP_MSG">asys_fifo56x16.v(600)</a><!@TM:**********> | Removing instance ae_set_cmp_2 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.</font>
<font color=#A52A2A>@W:<a href="@W:BN114:@XP_HELP">BN114</a> : <a href="d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v:608:10:608:22:@W:BN114:@XP_MSG">asys_fifo56x16.v(608)</a><!@TM:**********> | Removing instance ae_set_cmp_3 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.</font>
<font color=#A52A2A>@W:<a href="@W:BN114:@XP_HELP">BN114</a> : <a href="d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v:616:10:616:12:@W:BN114:@XP_MSG">asys_fifo56x16.v(616)</a><!@TM:**********> | Removing instance a2 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.</font>
<font color=#A52A2A>@W:<a href="@W:BN114:@XP_HELP">BN114</a> : <a href="d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v:624:10:624:25:@W:BN114:@XP_MSG">asys_fifo56x16.v(624)</a><!@TM:**********> | Removing instance ae_clr_cmp_ci_a (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.</font>
<font color=#A52A2A>@W:<a href="@W:BN114:@XP_HELP">BN114</a> : <a href="d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v:632:10:632:22:@W:BN114:@XP_MSG">asys_fifo56x16.v(632)</a><!@TM:**********> | Removing instance ae_clr_cmp_0 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.</font>
<font color=#A52A2A>@W:<a href="@W:BN114:@XP_HELP">BN114</a> : <a href="d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v:640:10:640:22:@W:BN114:@XP_MSG">asys_fifo56x16.v(640)</a><!@TM:**********> | Removing instance ae_clr_cmp_1 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.</font>
<font color=#A52A2A>@W:<a href="@W:BN114:@XP_HELP">BN114</a> : <a href="d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v:648:10:648:22:@W:BN114:@XP_MSG">asys_fifo56x16.v(648)</a><!@TM:**********> | Removing instance ae_clr_cmp_2 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.</font>
<font color=#A52A2A>@W:<a href="@W:BN114:@XP_HELP">BN114</a> : <a href="d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v:656:10:656:22:@W:BN114:@XP_MSG">asys_fifo56x16.v(656)</a><!@TM:**********> | Removing instance ae_clr_cmp_3 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.</font>
<font color=#A52A2A>@W:<a href="@W:BN114:@XP_HELP">BN114</a> : <a href="d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v:664:10:664:12:@W:BN114:@XP_MSG">asys_fifo56x16.v(664)</a><!@TM:**********> | Removing instance a3 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.</font>
<font color=#A52A2A>@W:<a href="@W:BN114:@XP_HELP">BN114</a> : <a href="d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v:672:10:672:25:@W:BN114:@XP_MSG">asys_fifo56x16.v(672)</a><!@TM:**********> | Removing instance af_set_cmp_ci_a (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.</font>
<font color=#A52A2A>@W:<a href="@W:BN114:@XP_HELP">BN114</a> : <a href="d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v:680:10:680:22:@W:BN114:@XP_MSG">asys_fifo56x16.v(680)</a><!@TM:**********> | Removing instance af_set_cmp_0 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.</font>
<font color=#A52A2A>@W:<a href="@W:BN114:@XP_HELP">BN114</a> : <a href="d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v:688:10:688:22:@W:BN114:@XP_MSG">asys_fifo56x16.v(688)</a><!@TM:**********> | Removing instance af_set_cmp_1 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.</font>
<font color=#A52A2A>@W:<a href="@W:BN114:@XP_HELP">BN114</a> : <a href="d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v:696:10:696:22:@W:BN114:@XP_MSG">asys_fifo56x16.v(696)</a><!@TM:**********> | Removing instance af_set_cmp_2 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.</font>
<font color=#A52A2A>@W:<a href="@W:BN114:@XP_HELP">BN114</a> : <a href="d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v:704:10:704:22:@W:BN114:@XP_MSG">asys_fifo56x16.v(704)</a><!@TM:**********> | Removing instance af_set_cmp_3 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.</font>
<font color=#A52A2A>@W:<a href="@W:BN114:@XP_HELP">BN114</a> : <a href="d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v:712:10:712:12:@W:BN114:@XP_MSG">asys_fifo56x16.v(712)</a><!@TM:**********> | Removing instance a4 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.</font>
<font color=#A52A2A>@W:<a href="@W:BN114:@XP_HELP">BN114</a> : <a href="d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v:720:10:720:25:@W:BN114:@XP_MSG">asys_fifo56x16.v(720)</a><!@TM:**********> | Removing instance af_clr_cmp_ci_a (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.</font>
<font color=#A52A2A>@W:<a href="@W:BN114:@XP_HELP">BN114</a> : <a href="d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v:728:10:728:22:@W:BN114:@XP_MSG">asys_fifo56x16.v(728)</a><!@TM:**********> | Removing instance af_clr_cmp_0 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.</font>
<font color=#A52A2A>@W:<a href="@W:BN114:@XP_HELP">BN114</a> : <a href="d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v:736:10:736:22:@W:BN114:@XP_MSG">asys_fifo56x16.v(736)</a><!@TM:**********> | Removing instance af_clr_cmp_1 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.</font>
<font color=#A52A2A>@W:<a href="@W:BN114:@XP_HELP">BN114</a> : <a href="d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v:744:10:744:22:@W:BN114:@XP_MSG">asys_fifo56x16.v(744)</a><!@TM:**********> | Removing instance af_clr_cmp_2 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.</font>
<font color=#A52A2A>@W:<a href="@W:BN114:@XP_HELP">BN114</a> : <a href="d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v:752:10:752:22:@W:BN114:@XP_MSG">asys_fifo56x16.v(752)</a><!@TM:**********> | Removing instance af_clr_cmp_3 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.</font>
<font color=#A52A2A>@W:<a href="@W:BN114:@XP_HELP">BN114</a> : <a href="d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v:764:10:764:12:@W:BN114:@XP_MSG">asys_fifo56x16.v(764)</a><!@TM:**********> | Removing instance a5 (in view: work.Asys_fifo56X16(verilog)) of black box view:LUCENT.CCU2C(PRIM) because it does not drive other instances.</font>
Encoding state machine cur_state[5:0] (in view: work.DS18B20(verilog))
original code -> new code
   000001 -> 000001
   000010 -> 000010
   000100 -> 000100
   001000 -> 001000
   010000 -> 010000
   100000 -> 100000
Encoding state machine tx_state[10:0] (in view: work.Ctrl_Data(verilog))
original code -> new code
   0000 -> 00000000001
   0001 -> 00000000010
   0010 -> 00000000100
   0011 -> 00000001000
   0100 -> 00000010000
   0101 -> 00000100000
   0110 -> 00001000000
   0111 -> 00010000000
   1000 -> 00100000000
   1001 -> 01000000000
   1010 -> 10000000000
Encoding state machine trans_state[5:0] (in view: work.Rs422Output_Z2(verilog))
original code -> new code
   0000 -> 000001
   0001 -> 000010
   0010 -> 000100
   0011 -> 001000
   0100 -> 010000
   0101 -> 100000

Starting clock optimization phase (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 191MB peak: 191MB)

@N:<a href="@N:MF578:@XP_HELP">MF578</a> : <!@TM:**********> | Incompatible asynchronous control logic preventing generated clock conversion. 

Finished clock optimization phase (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 191MB peak: 192MB)


Starting clock optimization report phase (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 191MB peak: 192MB)


Finished clock optimization report phase (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 191MB peak: 192MB)

@N:<a href="@N:FX1184:@XP_HELP">FX1184</a> : <!@TM:**********> | Applying syn_allowed_resources blockrams=56 on top level netlist INS350_5J_JZ  

Finished netlist restructuring (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 191MB peak: 192MB)



<a name=mapperReport6></a>Clock Summary</a>
******************

          Start                                 Requested     Requested     Clock                                                Clock                   Clock
Level     Clock                                 Frequency     Period        Type                                                 Group                   Load 
--------------------------------------------------------------------------------------------------------------------------------------------------------------
0 <USER>       <GROUP>                                200.0 MHz     5.000         system                                               system_clkgroup         0    
                                                                                                                                                              
0 -       global_clock|CLKOP_inferred_clock     200.0 MHz     5.000         inferred                                             Inferred_clkgroup_0     855  
1 .         DS18B20|clk_us_derived_clock        200.0 MHz     5.000         derived (from global_clock|CLKOP_inferred_clock)     Inferred_clkgroup_0     64   
                                                                                                                                                              
0 -       global_clock|CLKOS_inferred_clock     200.0 MHz     5.000         inferred                                             Inferred_clkgroup_1     178  
==============================================================================================================================================================



Clock Load Summary
***********************

                                      Clock     Source                              Clock Pin                                  Non-clock Pin     Non-clock Pin
Clock                                 Load      Pin                                 Seq Example                                Seq Example       Comb Example 
--------------------------------------------------------------------------------------------------------------------------------------------------------------
System                                0         -                                   -                                          -                 -            
                                                                                                                                                              
global_clock|CLKOP_inferred_clock     855       CLK120.PLLInst_0.CLKOP(EHXPLLL)     signal_process.trans.clk_out.C             -                 -            
DS18B20|clk_us_derived_clock          64        wendu.clk_us.Q[0](dffre)            wendu.data_temp[15:0].C                    -                 -            
                                                                                                                                                              
global_clock|CLKOS_inferred_clock     178       CLK120.PLLInst_0.CLKOS(EHXPLLL)     signal_process.demodu.din_reg0[11:0].C     -                 -            
==============================================================================================================================================================

<font color=#A52A2A>@W:<a href="@W:MT529:@XP_HELP">MT529</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\src_al\ds18b20.v:69:0:69:6:@W:MT529:@XP_MSG">ds18b20.v(69)</a><!@TM:**********> | Found inferred clock global_clock|CLKOP_inferred_clock which controls 855 sequential elements including wendu.cnt[7:0]. This clock has no specified timing constraint which may prevent conversion of gated or generated clocks and may adversely impact design performance. </font>
<font color=#A52A2A>@W:<a href="@W:MT529:@XP_HELP">MT529</a> : <a href="d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\asys_fifo56x16\asys_fifo56x16.v:198:13:198:26:@W:MT529:@XP_MSG">asys_fifo56x16.v(198)</a><!@TM:**********> | Found inferred clock global_clock|CLKOS_inferred_clock which controls 178 sequential elements including signal_process.demodu.fifo.pdp_ram_0_0_1. This clock has no specified timing constraint which may prevent conversion of gated or generated clocks and may adversely impact design performance. </font>

ICG Latch Removal Summary:
Number of ICG latches removed: 0
Number of ICG latches not removed:	0
For details review file gcc_ICG_report.rpt


@S |Clock Optimization Summary



<a name=clockReport7></a>#### START OF PREMAP CLOCK OPTIMIZATION REPORT #####[</a>

0 non-gated/non-generated clock tree(s) driving 0 clock pin(s) of sequential element(s)
3 gated/generated clock tree(s) driving 1097 clock pin(s) of sequential element(s)
0 instances converted, 1097 sequential instances remain driven by gated/generated clocks

============================================================================ Gated/Generated Clocks ============================================================================
Clock Tree ID     Driving Element            Drive Element Type     Unconverted Fanout     Sample Instance                            Explanation                               
--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
<a href="@|L:D:\Project\TLH50_03J_JZ_20250925\impl1\synwork\INS350_5J_JZ_impl1_prem.srm@|S:CLK120.PLLInst_0.CLKOP@|E:wendu.cnt[7:0]@|F:@syn_dgcc_clockid0_1==1@|M:ClockId_0_1 @XP_NAMES_BY_PROP">ClockId_0_1</a>       CLK120.PLLInst_0.CLKOP     EHXPLLL                855                    wendu.cnt[7:0]                             Clock source is invalid for GCC           
<a href="@|L:D:\Project\TLH50_03J_JZ_20250925\impl1\synwork\INS350_5J_JZ_impl1_prem.srm@|S:CLK120.PLLInst_0.CLKOS@|E:signal_process.demodu.AD_validcnt[7:0]@|F:@syn_dgcc_clockid0_4==1@|M:ClockId_0_4 @XP_NAMES_BY_PROP">ClockId_0_4</a>       CLK120.PLLInst_0.CLKOS     EHXPLLL                178                    signal_process.demodu.AD_validcnt[7:0]     Clock source is invalid for GCC           
<a href="@|L:D:\Project\TLH50_03J_JZ_20250925\impl1\synwork\INS350_5J_JZ_impl1_prem.srm@|S:wendu.clk_us.Q[0]@|E:wendu.cur_state[5]@|F:@syn_dgcc_clockid0_6==1@|M:ClockId_0_6 @XP_NAMES_BY_PROP">ClockId_0_6</a>       wendu.clk_us.Q[0]          dffre                  64                     wendu.cur_state[5]                         Derived clock on input (not legal for GCC)
================================================================================================================================================================================


##### END OF CLOCK OPTIMIZATION REPORT ######

@N:<a href="@N:FX1143:@XP_HELP">FX1143</a> : <!@TM:**********> | Skipping assigning INTERNAL_VREF to iobanks, because the table of mapping from pin to iobank is not initialized. 
Finished Pre Mapping Phase.

Starting constraint checker (Real Time elapsed 0h:00m:01s; CPU Time elapsed 0h:00m:01s; Memory used current: 193MB peak: 193MB)


Finished constraint checker preprocessing (Real Time elapsed 0h:00m:01s; CPU Time elapsed 0h:00m:01s; Memory used current: 193MB peak: 193MB)


Finished constraint checker (Real Time elapsed 0h:00m:01s; CPU Time elapsed 0h:00m:01s; Memory used current: 194MB peak: 194MB)

Pre-mapping successful!

At Mapper Exit (Real Time elapsed 0h:00m:01s; CPU Time elapsed 0h:00m:01s; Memory used current: 112MB peak: 195MB)

Process took 0h:00m:01s realtime, 0h:00m:01s cputime
# Thu Sep 25 10:29:26 2025

###########################################################]

</pre></samp></body></html>
<html><body><samp><pre>
<!@TC:**********>
# Thu Sep 25 10:29:26 2025


Copyright (C) 1994-2021 Synopsys, Inc.
This Synopsys software and all associated documentation are proprietary to Synopsys, Inc.
and may only be used pursuant to the terms and conditions of a written license agreement
with Synopsys, Inc. All other use, reproduction, modification, or distribution of the
Synopsys software or the associated documentation is strictly prohibited.
Tool: Synplify Pro (R)
Build: R-2021.03L-SP1-1
Install: D:\Software\lscc\diamond\3.12\synpbase
OS: Windows 6.2

Hostname: TLH-022

Implementation : impl1
<a name=mapperReport8></a>Synopsys Lattice Technology Mapper, Version map202103lat, Build 107R, Built Dec 22 2021 00:40:26, @</a>


Mapper Startup Complete (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 132MB peak: 132MB)

@N:<a href="@N:MF916:@XP_HELP">MF916</a> : <!@TM:**********> | Option synthesis_strategy=base is enabled.  
@N:<a href="@N:MF248:@XP_HELP">MF248</a> : <!@TM:**********> | Running in 64-bit mode. 
@N:<a href="@N:MF666:@XP_HELP">MF666</a> : <!@TM:**********> | Clock conversion enabled. (Command "set_option -fix_gated_and_generated_clocks 1" in the project file.) 

Design Input Complete (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 134MB peak: 144MB)


Mapper Initialization Complete (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 134MB peak: 144MB)


Start loading timing files (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 148MB peak: 148MB)


Finished loading timing files (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 150MB peak: 151MB)



Starting Optimization and Mapping (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 185MB peak: 185MB)

<font color=#A52A2A>@W:<a href="@W:FA239:@XP_HELP">FA239</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\src_al\ds18b20.v:269:11:269:34:@W:FA239:@XP_MSG">ds18b20.v(269)</a><!@TM:**********> | ROM RD_CMD_DATA_pmux (in view: work.DS18B20(verilog)) mapped in logic. To map to a technology ROM, apply attribute syn_romstyle on this instance.</font>
@N:<a href="@N:MO106:@XP_HELP">MO106</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\src_al\ds18b20.v:269:11:269:34:@N:MO106:@XP_MSG">ds18b20.v(269)</a><!@TM:**********> | Found ROM RD_CMD_DATA_pmux (in view: work.DS18B20(verilog)) with 16 words by 1 bit.
<font color=#A52A2A>@W:<a href="@W:FA239:@XP_HELP">FA239</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\src_al\ds18b20.v:211:11:211:34:@W:FA239:@XP_MSG">ds18b20.v(211)</a><!@TM:**********> | ROM WR_CMD_DATA_pmux (in view: work.DS18B20(verilog)) mapped in logic. To map to a technology ROM, apply attribute syn_romstyle on this instance.</font>
@N:<a href="@N:MO106:@XP_HELP">MO106</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\src_al\ds18b20.v:211:11:211:34:@N:MO106:@XP_MSG">ds18b20.v(211)</a><!@TM:**********> | Found ROM WR_CMD_DATA_pmux (in view: work.DS18B20(verilog)) with 16 words by 1 bit.

Finished RTL optimizations (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 190MB peak: 190MB)

@N:<a href="@N:MO231:@XP_HELP">MO231</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\src_al\ds18b20.v:157:0:157:6:@N:MO231:@XP_MSG">ds18b20.v(157)</a><!@TM:**********> | Found counter in view:work.DS18B20(verilog) instance bit_cnt[3:0] 
@N:<a href="@N:MO231:@XP_HELP">MO231</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\src_al\speed_select_tx.v:37:0:37:6:@N:MO231:@XP_MSG">speed_select_tx.v(37)</a><!@TM:**********> | Found counter in view:work.speed_select_Tx_120000000s_115200s_1041s_520s(verilog) instance cnt[12:0] 
@N:<a href="@N:MO231:@XP_HELP">MO231</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\src_al\rs422output.v:150:0:150:6:@N:MO231:@XP_MSG">rs422output.v(150)</a><!@TM:**********> | Found counter in view:work.SignalProcessing_Z3(verilog) instance rs422.count_pos[7:0] 
@N:<a href="@N:BN362:@XP_HELP">BN362</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\src_al\integration.v:76:0:76:6:@N:BN362:@XP_MSG">integration.v(76)</a><!@TM:**********> | Removing sequential instance integ.dout[55] (in view: work.SignalProcessing_Z3(verilog)) of type view:PrimLib.dff(prim) because it does not drive other instances.
<font color=#A52A2A>@W:<a href="@W:BN132:@XP_HELP">BN132</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\src_al\rs422output.v:88:0:88:6:@W:BN132:@XP_MSG">rs422output.v(88)</a><!@TM:**********> | Removing instance signal_process.rs422.output_dy[0] because it is equivalent to instance signal_process.modu.mudu_dy[0]. To keep the instance, apply constraint syn_preserve=1 on the instance.</font>
<font color=#A52A2A>@W:<a href="@W:BN132:@XP_HELP">BN132</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\src_al\rs422output.v:88:0:88:6:@W:BN132:@XP_MSG">rs422output.v(88)</a><!@TM:**********> | Removing instance signal_process.rs422.output_dy[1] because it is equivalent to instance signal_process.modu.mudu_dy[1]. To keep the instance, apply constraint syn_preserve=1 on the instance.</font>

Starting factoring (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 194MB peak: 194MB)


Finished factoring (Real Time elapsed 0h:00m:00s; CPU Time elapsed 0h:00m:00s; Memory used current: 195MB peak: 195MB)


Available hyper_sources - for debug and ip models
	None Found


Finished generic timing optimizations - Pass 1 (Real Time elapsed 0h:00m:01s; CPU Time elapsed 0h:00m:01s; Memory used current: 200MB peak: 200MB)

@N:<a href="@N:BN362:@XP_HELP">BN362</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\src_al\integration.v:66:0:66:6:@N:BN362:@XP_MSG">integration.v(66)</a><!@TM:**********> | Removing sequential instance signal_process.integ.DA_dout[55] (in view: work.INS350_5J_JZ(verilog)) because it does not drive other instances.
@N:<a href="@N:BN362:@XP_HELP">BN362</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:181:0:181:6:@N:BN362:@XP_MSG">demodulation.v(181)</a><!@TM:**********> | Removing sequential instance signal_process.demodu.dout[55] (in view: work.INS350_5J_JZ(verilog)) because it does not drive other instances.
@N:<a href="@N:BN362:@XP_HELP">BN362</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:163:0:163:6:@N:BN362:@XP_MSG">demodulation.v(163)</a><!@TM:**********> | Removing sequential instance signal_process.demodu.median_sum_n[55] (in view: work.INS350_5J_JZ(verilog)) because it does not drive other instances.
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:163:0:163:6:@A:BN291:@XP_MSG">demodulation.v(163)</a><!@TM:**********> | Boundary register signal_process.demodu.median_sum_n[55] (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@N:<a href="@N:BN362:@XP_HELP">BN362</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:163:0:163:6:@N:BN362:@XP_MSG">demodulation.v(163)</a><!@TM:**********> | Removing sequential instance signal_process.demodu.INS_dout[55] (in view: work.INS350_5J_JZ(verilog)) because it does not drive other instances.
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:163:0:163:6:@A:BN291:@XP_MSG">demodulation.v(163)</a><!@TM:**********> | Boundary register signal_process.demodu.INS_dout[55] (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 

Starting Early Timing Optimization (Real Time elapsed 0h:00m:03s; CPU Time elapsed 0h:00m:03s; Memory used current: 205MB peak: 205MB)


Finished Early Timing Optimization (Real Time elapsed 0h:00m:03s; CPU Time elapsed 0h:00m:03s; Memory used current: 205MB peak: 206MB)


Finished generic timing optimizations - Pass 2 (Real Time elapsed 0h:00m:03s; CPU Time elapsed 0h:00m:03s; Memory used current: 205MB peak: 206MB)


Finished preparing to map (Real Time elapsed 0h:00m:03s; CPU Time elapsed 0h:00m:03s; Memory used current: 206MB peak: 206MB)


Finished technology mapping (Real Time elapsed 0h:00m:03s; CPU Time elapsed 0h:00m:03s; Memory used current: 210MB peak: 210MB)

Pass		 CPU time		Worst Slack		Luts / Registers
------------------------------------------------------------
   1		0h:00m:03s		     0.80ns		 285 /      1018

Finished technology timing optimizations and critical path resynthesis (Real Time elapsed 0h:00m:04s; CPU Time elapsed 0h:00m:04s; Memory used current: 211MB peak: 211MB)

@N:<a href="@N:FX164:@XP_HELP">FX164</a> : <!@TM:**********> | The option to pack registers in the IOB has not been specified. Please set syn_useioff attribute.   
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_55_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_54_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_53_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_52_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_51_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_50_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_49_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_48_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_47_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_46_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_45_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_44_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_43_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_42_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_41_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_40_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_39_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_38_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_37_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_36_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_35_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_34_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_33_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_32_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_31_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_30_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_29_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_28_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_27_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_26_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_25_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_24_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_23_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_22_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_21_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_20_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_19_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_18_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_17_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_16_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_15_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_14_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_13_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_12_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_11_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_10_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_9_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_8_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_7_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_6_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_5_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_4_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_3_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_2_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_1_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 
@A:<a href="@A:BN291:@XP_HELP">BN291</a> : <a href="d:\project\tlh50_03j_jz_20250925\src_al\demodulation.v:110:0:110:6:@A:BN291:@XP_MSG">demodulation.v(110)</a><!@TM:**********> | Boundary register signal_process.demodu.sample_sum_0_.fb (in view: work.INS350_5J_JZ(verilog)) is packed into a complex cell. To disable register packing, set syn_keep=1 on the net between the register and the complex cell. 

Finished restoring hierarchy (Real Time elapsed 0h:00m:04s; CPU Time elapsed 0h:00m:04s; Memory used current: 211MB peak: 211MB)


Start Writing Netlists (Real Time elapsed 0h:00m:04s; CPU Time elapsed 0h:00m:04s; Memory used current: 172MB peak: 212MB)

Writing Analyst data base D:\Project\TLH50_03J_JZ_20250925\impl1\synwork\INS350_5J_JZ_impl1_m.srm

Finished Writing Netlist Databases (Real Time elapsed 0h:00m:07s; CPU Time elapsed 0h:00m:06s; Memory used current: 214MB peak: 214MB)

Writing EDIF Netlist and constraint files
@N:<a href="@N:FX1056:@XP_HELP">FX1056</a> : <!@TM:**********> | Writing EDF file: D:\Project\TLH50_03J_JZ_20250925\impl1\INS350_5J_JZ_impl1.edi 
@N:<a href="@N:BW106:@XP_HELP">BW106</a> : <!@TM:**********> | Synplicity Constraint File capacitance units using default value of 1pF  

Finished Writing EDIF Netlist and constraint files (Real Time elapsed 0h:00m:07s; CPU Time elapsed 0h:00m:07s; Memory used current: 219MB peak: 219MB)


Finished Writing Netlists (Real Time elapsed 0h:00m:07s; CPU Time elapsed 0h:00m:07s; Memory used current: 219MB peak: 220MB)


Start final timing analysis (Real Time elapsed 0h:00m:07s; CPU Time elapsed 0h:00m:07s; Memory used current: 212MB peak: 220MB)

<font color=#A52A2A>@W:<a href="@W:MT246:@XP_HELP">MT246</a> : <a href="d:\project\tlh50_03j_jz_20250925\ip_al\global_clock1\global_clock\global_clock.v:59:12:59:21:@W:MT246:@XP_MSG">global_clock.v(59)</a><!@TM:**********> | Blackbox EHXPLLL is missing a user supplied timing model. This may have a negative effect on timing analysis and optimizations (Quality of Results)</font>
<font color=#A52A2A>@W:<a href="@W:MT420:@XP_HELP">MT420</a> : <!@TM:**********> | Found inferred clock global_clock|CLKOP_inferred_clock with period 5.00ns. Please declare a user-defined clock on net CLK120.clk120mhz.</font> 
<font color=#A52A2A>@W:<a href="@W:MT420:@XP_HELP">MT420</a> : <!@TM:**********> | Found inferred clock global_clock|CLKOS_inferred_clock with period 5.00ns. Please declare a user-defined clock on net CLK120.clk_AD.</font> 
@N:<a href="@N:MT615:@XP_HELP">MT615</a> : <!@TM:**********> | Found clock DS18B20|clk_us_derived_clock with period 5.00ns  


<a name=timingReport9></a>##### START OF TIMING REPORT #####[</a>
# Timing report written on Thu Sep 25 10:29:34 2025
#


Top view:               INS350_5J_JZ
Requested Frequency:    200.0 MHz
Wire load mode:         top
Paths requested:        5
Constraint File(s):    
@N:<a href="@N:MT320:@XP_HELP">MT320</a> : <!@TM:**********> | This timing report is an estimate of place and route data. For final timing results, use the FPGA vendor place and route report. 

@N:<a href="@N:MT322:@XP_HELP">MT322</a> : <!@TM:**********> | Clock constraints include only register-to-register paths associated with each individual clock. 



<a name=performanceSummary10></a>Performance Summary</a>
*******************


Worst slack in design: 0.058

                                      Requested     Estimated      Requested     Estimated               Clock                                                Clock              
Starting Clock                        Frequency     Frequency      Period        Period        Slack     Type                                                 Group              
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
DS18B20|clk_us_derived_clock          200.0 MHz     238.7 MHz      5.000         4.189         1.621     derived (from global_clock|CLKOP_inferred_clock)     Inferred_clkgroup_0
global_clock|CLKOP_inferred_clock     200.0 MHz     202.3 MHz      5.000         4.942         0.058     inferred                                             Inferred_clkgroup_0
global_clock|CLKOS_inferred_clock     200.0 MHz     233.5 MHz      5.000         4.284         0.717     inferred                                             Inferred_clkgroup_1
System                                200.0 MHz     2347.4 MHz     5.000         0.426         4.574     system                                               system_clkgroup    
=================================================================================================================================================================================





<a name=clockRelationships11></a>Clock Relationships</a>
*******************

Clocks                                                                |    rise  to  rise   |    fall  to  fall   |    rise  to  fall   |    fall  to  rise 
------------------------------------------------------------------------------------------------------------------------------------------------------------
Starting                           Ending                             |  constraint  slack  |  constraint  slack  |  constraint  slack  |  constraint  slack
------------------------------------------------------------------------------------------------------------------------------------------------------------
System                             System                             |  5.000       4.574  |  No paths    -      |  No paths    -      |  No paths    -    
System                             global_clock|CLKOP_inferred_clock  |  5.000       2.861  |  No paths    -      |  No paths    -      |  No paths    -    
System                             global_clock|CLKOS_inferred_clock  |  5.000       2.317  |  No paths    -      |  No paths    -      |  No paths    -    
global_clock|CLKOP_inferred_clock  System                             |  5.000       0.856  |  No paths    -      |  No paths    -      |  No paths    -    
global_clock|CLKOP_inferred_clock  global_clock|CLKOP_inferred_clock  |  5.000       0.058  |  No paths    -      |  No paths    -      |  No paths    -    
global_clock|CLKOP_inferred_clock  global_clock|CLKOS_inferred_clock  |  Diff grp    -      |  No paths    -      |  No paths    -      |  No paths    -    
global_clock|CLKOS_inferred_clock  System                             |  5.000       3.619  |  No paths    -      |  No paths    -      |  No paths    -    
global_clock|CLKOS_inferred_clock  global_clock|CLKOP_inferred_clock  |  Diff grp    -      |  No paths    -      |  No paths    -      |  No paths    -    
global_clock|CLKOS_inferred_clock  global_clock|CLKOS_inferred_clock  |  5.000       0.717  |  No paths    -      |  No paths    -      |  No paths    -    
DS18B20|clk_us_derived_clock       global_clock|CLKOP_inferred_clock  |  5.000       4.093  |  No paths    -      |  No paths    -      |  No paths    -    
DS18B20|clk_us_derived_clock       DS18B20|clk_us_derived_clock       |  5.000       1.622  |  No paths    -      |  No paths    -      |  No paths    -    
============================================================================================================================================================
 Note: 'No paths' indicates there are no paths in the design for that pair of clock edges.
       'Diff grp' indicates that paths exist but the starting clock and ending clock are in different clock groups.



<a name=interfaceInfo12></a>Interface Information </a>
*********************

No IO constraint found



====================================
<a name=clockReport13></a>Detailed Report for Clock: DS18B20|clk_us_derived_clock</a>
====================================



<a name=startingSlack14></a>Starting Points with Worst Slack</a>
********************************

                     Starting                                                            Arrival          
Instance             Reference                        Type        Pin     Net            Time        Slack
                     Clock                                                                                
----------------------------------------------------------------------------------------------------------
wendu.cnt_us[10]     DS18B20|clk_us_derived_clock     FD1S3DX     Q       cnt_us[10]     0.955       1.621
wendu.cnt_us[13]     DS18B20|clk_us_derived_clock     FD1S3DX     Q       cnt_us[13]     0.955       1.621
wendu.cnt_us[16]     DS18B20|clk_us_derived_clock     FD1S3DX     Q       cnt_us[16]     0.955       1.621
wendu.cnt_us[19]     DS18B20|clk_us_derived_clock     FD1S3DX     Q       cnt_us[19]     0.955       1.621
wendu.cnt_us[14]     DS18B20|clk_us_derived_clock     FD1S3DX     Q       cnt_us[14]     0.955       2.228
wendu.cnt_us[15]     DS18B20|clk_us_derived_clock     FD1S3DX     Q       cnt_us[15]     0.955       2.228
wendu.cnt_us[17]     DS18B20|clk_us_derived_clock     FD1S3DX     Q       cnt_us[17]     0.955       2.228
wendu.cnt_us[0]      DS18B20|clk_us_derived_clock     FD1S3DX     Q       cnt_us[0]      0.955       2.797
wendu.cnt_us[11]     DS18B20|clk_us_derived_clock     FD1S3DX     Q       cnt_us[11]     0.955       2.797
wendu.cnt_us[12]     DS18B20|clk_us_derived_clock     FD1S3DX     Q       cnt_us[12]     0.955       2.797
==========================================================================================================


<a name=endingSlack15></a>Ending Points with Worst Slack</a>
******************************

                     Starting                                                               Required          
Instance             Reference                        Type        Pin     Net               Time         Slack
                     Clock                                                                                    
--------------------------------------------------------------------------------------------------------------
wendu.cnt_us[19]     DS18B20|clk_us_derived_clock     FD1S3DX     D       cnt_us_12[19]     9.946        1.621
wendu.cnt_us[17]     DS18B20|clk_us_derived_clock     FD1S3DX     D       cnt_us_12[17]     9.946        1.683
wendu.cnt_us[15]     DS18B20|clk_us_derived_clock     FD1S3DX     D       cnt_us_12[15]     9.946        1.744
wendu.cnt_us[16]     DS18B20|clk_us_derived_clock     FD1S3DX     D       cnt_us_12[16]     9.946        1.744
wendu.cnt_us[13]     DS18B20|clk_us_derived_clock     FD1S3DX     D       cnt_us_12[13]     9.946        1.804
wendu.cnt_us[14]     DS18B20|clk_us_derived_clock     FD1S3DX     D       cnt_us_12[14]     9.946        1.804
wendu.cnt_us[9]      DS18B20|clk_us_derived_clock     FD1S3DX     D       cnt_us_12[9]      9.946        1.927
wendu.cnt_us[10]     DS18B20|clk_us_derived_clock     FD1S3DX     D       cnt_us_12[10]     9.946        1.927
wendu.cnt_us[7]      DS18B20|clk_us_derived_clock     FD1S3DX     D       cnt_us_12[7]      9.946        1.988
wendu.cnt_us[8]      DS18B20|clk_us_derived_clock     FD1S3DX     D       cnt_us_12[8]      9.946        1.988
==============================================================================================================



<a name=worstPaths16></a>Worst Path Information</a>
<a href="D:\Project\TLH50_03J_JZ_20250925\impl1\INS350_5J_JZ_impl1.srr:srsfD:\Project\TLH50_03J_JZ_20250925\impl1\INS350_5J_JZ_impl1.srs:fp:93182:99662:@XP_NAMES_GATE">View Worst Path in Analyst</a>
***********************


Path information for path number 1: 
      Requested Period:                      10.000
    - Setup time:                            0.054
    + Clock delay at ending point:           0.000 (ideal)
    = Required time:                         9.946

    - Propagation time:                      8.325
    - Clock delay at starting point:         0.000 (ideal)
    = Slack (non-critical) :                 1.621

    Number of logic level(s):                19
    Starting point:                          wendu.cnt_us[10] / Q
    Ending point:                            wendu.cnt_us[19] / D
    The start point is clocked by            DS18B20|clk_us_derived_clock [rising] (rise=0.000 fall=2.500 period=5.000) on pin CK
    The end   point is clocked by            DS18B20|clk_us_derived_clock [rising] (rise=0.000 fall=2.500 period=5.000) on pin CK
    -Timing constraint applied as multi cycle path with factor 2 (from c:DS18B20|clk_us_derived_clock to c:DS18B20|clk_us_derived_clock)

Instance / Net                                           Pin      Pin               Arrival     No. of    
Name                                        Type         Name     Dir     Delay     Time        Fan Out(s)
----------------------------------------------------------------------------------------------------------
wendu.cnt_us[10]                            FD1S3DX      Q        Out     0.955     0.955 r     -         
cnt_us[10]                                  Net          -        -       -         -           3         
wendu.un1_cur_state_3_i_a2_3_o3_5_4         ORCALUT4     A        In      0.000     0.955 r     -         
wendu.un1_cur_state_3_i_a2_3_o3_5_4         ORCALUT4     Z        Out     0.606     1.561 r     -         
un1_cur_state_3_i_a2_3_o3_5_4               Net          -        -       -         -           1         
wendu.un1_cur_state_3_i_a2_3_o3_5           ORCALUT4     D        In      0.000     1.561 r     -         
wendu.un1_cur_state_3_i_a2_3_o3_5           ORCALUT4     Z        Out     0.708     2.269 r     -         
un1_cur_state_3_i_a2_3_o3_5                 Net          -        -       -         -           3         
wendu.un1_cur_state_3_i_a2_3_o3_0           ORCALUT4     A        In      0.000     2.269 r     -         
wendu.un1_cur_state_3_i_a2_3_o3_0           ORCALUT4     Z        Out     0.660     2.929 r     -         
un1_cur_state_3_i_a2_3_o3_0                 Net          -        -       -         -           2         
wendu.data_temp_1_sqmuxa_0_o3_0             ORCALUT4     B        In      0.000     2.929 r     -         
wendu.data_temp_1_sqmuxa_0_o3_0             ORCALUT4     Z        Out     0.708     3.637 r     -         
data_temp_1_sqmuxa_0_o3_0                   Net          -        -       -         -           3         
wendu.data_temp_1_sqmuxa_0_o3_0_RNIT8UQ     ORCALUT4     A        In      0.000     3.637 r     -         
wendu.data_temp_1_sqmuxa_0_o3_0_RNIT8UQ     ORCALUT4     Z        Out     0.708     4.345 r     -         
data_temp_1_sqmuxa_0_o3_0_RNIT8UQ           Net          -        -       -         -           3         
wendu.cur_state_RNIG5RS[5]                  ORCALUT4     B        In      0.000     4.345 r     -         
wendu.cur_state_RNIG5RS[5]                  ORCALUT4     Z        Out     0.837     5.182 f     -         
N_93_i                                      Net          -        -       -         -           20        
wendu.un1_cnt_us_18_cry_0_0_RNO             ORCALUT4     A        In      0.000     5.182 f     -         
wendu.un1_cnt_us_18_cry_0_0_RNO             ORCALUT4     Z        Out     0.606     5.788 r     -         
un1_cnt_us_0_sqmuxa_2_i                     Net          -        -       -         -           1         
wendu.un1_cnt_us_18_cry_0_0                 CCU2C        B0       In      0.000     5.788 r     -         
wendu.un1_cnt_us_18_cry_0_0                 CCU2C        COUT     Out     0.900     6.688 r     -         
un1_cnt_us_18_cry_0                         Net          -        -       -         -           1         
wendu.un1_cnt_us_18_cry_1_0                 CCU2C        CIN      In      0.000     6.688 r     -         
wendu.un1_cnt_us_18_cry_1_0                 CCU2C        COUT     Out     0.061     6.749 r     -         
un1_cnt_us_18_cry_2                         Net          -        -       -         -           1         
wendu.un1_cnt_us_18_cry_3_0                 CCU2C        CIN      In      0.000     6.749 r     -         
wendu.un1_cnt_us_18_cry_3_0                 CCU2C        COUT     Out     0.061     6.810 r     -         
un1_cnt_us_18_cry_4                         Net          -        -       -         -           1         
wendu.un1_cnt_us_18_cry_5_0                 CCU2C        CIN      In      0.000     6.810 r     -         
wendu.un1_cnt_us_18_cry_5_0                 CCU2C        COUT     Out     0.061     6.871 r     -         
un1_cnt_us_18_cry_6                         Net          -        -       -         -           1         
wendu.un1_cnt_us_18_cry_7_0                 CCU2C        CIN      In      0.000     6.871 r     -         
wendu.un1_cnt_us_18_cry_7_0                 CCU2C        COUT     Out     0.061     6.932 r     -         
un1_cnt_us_18_cry_8                         Net          -        -       -         -           1         
wendu.un1_cnt_us_18_cry_9_0                 CCU2C        CIN      In      0.000     6.932 r     -         
wendu.un1_cnt_us_18_cry_9_0                 CCU2C        COUT     Out     0.061     6.993 r     -         
un1_cnt_us_18_cry_10                        Net          -        -       -         -           1         
wendu.un1_cnt_us_18_cry_11_0                CCU2C        CIN      In      0.000     6.993 r     -         
wendu.un1_cnt_us_18_cry_11_0                CCU2C        COUT     Out     0.061     7.054 r     -         
un1_cnt_us_18_cry_12                        Net          -        -       -         -           1         
wendu.un1_cnt_us_18_cry_13_0                CCU2C        CIN      In      0.000     7.054 r     -         
wendu.un1_cnt_us_18_cry_13_0                CCU2C        COUT     Out     0.061     7.115 r     -         
un1_cnt_us_18_cry_14                        Net          -        -       -         -           1         
wendu.un1_cnt_us_18_cry_15_0                CCU2C        CIN      In      0.000     7.115 r     -         
wendu.un1_cnt_us_18_cry_15_0                CCU2C        COUT     Out     0.061     7.176 r     -         
un1_cnt_us_18_cry_16                        Net          -        -       -         -           1         
wendu.un1_cnt_us_18_cry_17_0                CCU2C        CIN      In      0.000     7.176 r     -         
wendu.un1_cnt_us_18_cry_17_0                CCU2C        COUT     Out     0.061     7.237 r     -         
un1_cnt_us_18_cry_18                        Net          -        -       -         -           1         
wendu.un1_cnt_us_18_s_19_0                  CCU2C        CIN      In      0.000     7.237 r     -         
wendu.un1_cnt_us_18_s_19_0                  CCU2C        S0       Out     0.698     7.934 r     -         
un1_cnt_us_18_s_19_0_S0                     Net          -        -       -         -           1         
wendu.cnt_us_12[19]                         ORCALUT4     C        In      0.000     7.934 r     -         
wendu.cnt_us_12[19]                         ORCALUT4     Z        Out     0.390     8.325 r     -         
cnt_us_12[19]                               Net          -        -       -         -           1         
wendu.cnt_us[19]                            FD1S3DX      D        In      0.000     8.325 r     -         
==========================================================================================================




====================================
<a name=clockReport17></a>Detailed Report for Clock: global_clock|CLKOP_inferred_clock</a>
====================================



<a name=startingSlack18></a>Starting Points with Worst Slack</a>
********************************

                      Starting                                                              Arrival          
Instance              Reference                             Type        Pin     Net         Time        Slack
                      Clock                                                                                  
-------------------------------------------------------------------------------------------------------------
u_uart.U0.cnt[9]      global_clock|CLKOP_inferred_clock     FD1S3DX     Q       cnt[9]      0.955       0.058
u_uart.U0.cnt[10]     global_clock|CLKOP_inferred_clock     FD1S3DX     Q       cnt[10]     0.955       0.058
u_uart.U0.cnt[1]      global_clock|CLKOP_inferred_clock     FD1S3DX     Q       cnt[1]      0.907       0.657
u_uart.U0.cnt[2]      global_clock|CLKOP_inferred_clock     FD1S3DX     Q       cnt[2]      0.907       0.657
u_uart.U0.cnt[5]      global_clock|CLKOP_inferred_clock     FD1S3DX     Q       cnt[5]      0.907       0.657
u_uart.U0.cnt[6]      global_clock|CLKOP_inferred_clock     FD1S3DX     Q       cnt[6]      0.907       0.657
u_uart.U0.cnt[7]      global_clock|CLKOP_inferred_clock     FD1S3DX     Q       cnt[7]      0.907       0.657
u_uart.U0.cnt[8]      global_clock|CLKOP_inferred_clock     FD1S3DX     Q       cnt[8]      0.907       0.657
u_uart.U0.cnt[11]     global_clock|CLKOP_inferred_clock     FD1S3DX     Q       cnt[11]     0.907       0.657
u_uart.U0.cnt[12]     global_clock|CLKOP_inferred_clock     FD1S3DX     Q       cnt[12]     0.907       0.657
=============================================================================================================


<a name=endingSlack19></a>Ending Points with Worst Slack</a>
******************************

                      Starting                                                                Required          
Instance              Reference                             Type        Pin     Net           Time         Slack
                      Clock                                                                                     
----------------------------------------------------------------------------------------------------------------
u_uart.U0.cnt[11]     global_clock|CLKOP_inferred_clock     FD1S3DX     D       cnt_s[11]     4.946        0.058
u_uart.U0.cnt[12]     global_clock|CLKOP_inferred_clock     FD1S3DX     D       cnt_s[12]     4.946        0.058
u_uart.U0.cnt[9]      global_clock|CLKOP_inferred_clock     FD1S3DX     D       cnt_s[9]      4.946        0.118
u_uart.U0.cnt[10]     global_clock|CLKOP_inferred_clock     FD1S3DX     D       cnt_s[10]     4.946        0.118
u_uart.U0.cnt[7]      global_clock|CLKOP_inferred_clock     FD1S3DX     D       cnt_s[7]      4.946        0.179
u_uart.U0.cnt[8]      global_clock|CLKOP_inferred_clock     FD1S3DX     D       cnt_s[8]      4.946        0.179
u_uart.U0.cnt[5]      global_clock|CLKOP_inferred_clock     FD1S3DX     D       cnt_s[5]      4.946        0.240
u_uart.U0.cnt[6]      global_clock|CLKOP_inferred_clock     FD1S3DX     D       cnt_s[6]      4.946        0.240
u_uart.U0.cnt[3]      global_clock|CLKOP_inferred_clock     FD1S3DX     D       cnt_s[3]      4.946        0.301
u_uart.U0.cnt[4]      global_clock|CLKOP_inferred_clock     FD1S3DX     D       cnt_s[4]      4.946        0.301
================================================================================================================



<a name=worstPaths20></a>Worst Path Information</a>
<a href="D:\Project\TLH50_03J_JZ_20250925\impl1\INS350_5J_JZ_impl1.srr:srsfD:\Project\TLH50_03J_JZ_20250925\impl1\INS350_5J_JZ_impl1.srs:fp:104652:107688:@XP_NAMES_GATE">View Worst Path in Analyst</a>
***********************


Path information for path number 1: 
      Requested Period:                      5.000
    - Setup time:                            0.054
    + Clock delay at ending point:           0.000 (ideal)
    = Required time:                         4.946

    - Propagation time:                      4.888
    - Clock delay at starting point:         0.000 (ideal)
    = Slack (critical) :                     0.058

    Number of logic level(s):                10
    Starting point:                          u_uart.U0.cnt[9] / Q
    Ending point:                            u_uart.U0.cnt[12] / D
    The start point is clocked by            global_clock|CLKOP_inferred_clock [rising] (rise=0.000 fall=2.500 period=5.000) on pin CK
    The end   point is clocked by            global_clock|CLKOP_inferred_clock [rising] (rise=0.000 fall=2.500 period=5.000) on pin CK

Instance / Net                           Pin      Pin               Arrival     No. of    
Name                        Type         Name     Dir     Delay     Time        Fan Out(s)
------------------------------------------------------------------------------------------
u_uart.U0.cnt[9]            FD1S3DX      Q        Out     0.955     0.955 r     -         
cnt[9]                      Net          -        -       -         -           3         
u_uart.U0.cnt9_i_RNO_0      ORCALUT4     A        In      0.000     0.955 r     -         
u_uart.U0.cnt9_i_RNO_0      ORCALUT4     Z        Out     0.606     1.561 f     -         
m16_1                       Net          -        -       -         -           1         
u_uart.U0.cnt9_i_RNO        ORCALUT4     D        In      0.000     1.561 f     -         
u_uart.U0.cnt9_i_RNO        ORCALUT4     Z        Out     0.606     2.167 f     -         
m16_3                       Net          -        -       -         -           1         
u_uart.U0.cnt9_i            ORCALUT4     D        In      0.000     2.167 f     -         
u_uart.U0.cnt9_i            ORCALUT4     Z        Out     0.819     2.986 r     -         
cnt                         Net          -        -       -         -           14        
u_uart.U0.cnt_cry_0[0]      CCU2C        A1       In      0.000     2.986 r     -         
u_uart.U0.cnt_cry_0[0]      CCU2C        COUT     Out     0.900     3.886 r     -         
cnt_cry[0]                  Net          -        -       -         -           1         
u_uart.U0.cnt_cry_0[1]      CCU2C        CIN      In      0.000     3.886 r     -         
u_uart.U0.cnt_cry_0[1]      CCU2C        COUT     Out     0.061     3.947 r     -         
cnt_cry[2]                  Net          -        -       -         -           1         
u_uart.U0.cnt_cry_0[3]      CCU2C        CIN      In      0.000     3.947 r     -         
u_uart.U0.cnt_cry_0[3]      CCU2C        COUT     Out     0.061     4.008 r     -         
cnt_cry[4]                  Net          -        -       -         -           1         
u_uart.U0.cnt_cry_0[5]      CCU2C        CIN      In      0.000     4.008 r     -         
u_uart.U0.cnt_cry_0[5]      CCU2C        COUT     Out     0.061     4.069 r     -         
cnt_cry[6]                  Net          -        -       -         -           1         
u_uart.U0.cnt_cry_0[7]      CCU2C        CIN      In      0.000     4.069 r     -         
u_uart.U0.cnt_cry_0[7]      CCU2C        COUT     Out     0.061     4.130 r     -         
cnt_cry[8]                  Net          -        -       -         -           1         
u_uart.U0.cnt_cry_0[9]      CCU2C        CIN      In      0.000     4.130 r     -         
u_uart.U0.cnt_cry_0[9]      CCU2C        COUT     Out     0.061     4.191 r     -         
cnt_cry[10]                 Net          -        -       -         -           1         
u_uart.U0.cnt_cry_0[11]     CCU2C        CIN      In      0.000     4.191 r     -         
u_uart.U0.cnt_cry_0[11]     CCU2C        S1       Out     0.698     4.888 r     -         
cnt_s[12]                   Net          -        -       -         -           1         
u_uart.U0.cnt[12]           FD1S3DX      D        In      0.000     4.888 r     -         
==========================================================================================




====================================
<a name=clockReport21></a>Detailed Report for Clock: global_clock|CLKOS_inferred_clock</a>
====================================



<a name=startingSlack22></a>Starting Points with Worst Slack</a>
********************************

                                         Starting                                                                     Arrival          
Instance                                 Reference                             Type        Pin     Net                Time        Slack
                                         Clock                                                                                         
---------------------------------------------------------------------------------------------------------------------------------------
signal_process.demodu.sample_sum[0]      global_clock|CLKOS_inferred_clock     FD1P3IX     Q       sample_sum[0]      0.985       0.717
signal_process.demodu.din_reg1[0]        global_clock|CLKOS_inferred_clock     FD1S3AX     Q       din_reg1[0]        0.907       0.794
signal_process.demodu.sample_sum[1]      global_clock|CLKOS_inferred_clock     FD1P3IX     Q       sample_sum[1]      0.955       0.807
signal_process.demodu.sample_sum[2]      global_clock|CLKOS_inferred_clock     FD1P3IX     Q       sample_sum[2]      0.955       0.807
signal_process.demodu.sample_sum[3]      global_clock|CLKOS_inferred_clock     FD1P3IX     Q       sample_sum[3]      0.955       0.869
signal_process.demodu.sample_sum[4]      global_clock|CLKOS_inferred_clock     FD1P3IX     Q       sample_sum[4]      0.955       0.869
signal_process.demodu.AD_validcnt[3]     global_clock|CLKOS_inferred_clock     FD1S3IX     Q       AD_validcnt[3]     0.955       0.896
signal_process.demodu.AD_validcnt[5]     global_clock|CLKOS_inferred_clock     FD1S3IX     Q       AD_validcnt[5]     0.955       0.896
signal_process.demodu.din_reg1[1]        global_clock|CLKOS_inferred_clock     FD1S3AX     Q       din_reg1[1]        0.853       0.909
signal_process.demodu.din_reg1[2]        global_clock|CLKOS_inferred_clock     FD1S3AX     Q       din_reg1[2]        0.853       0.909
=======================================================================================================================================


<a name=endingSlack23></a>Ending Points with Worst Slack</a>
******************************

                                         Starting                                                                                 Required          
Instance                                 Reference                             Type        Pin     Net                            Time         Slack
                                         Clock                                                                                                      
----------------------------------------------------------------------------------------------------------------------------------------------------
signal_process.demodu.sample_sum[55]     global_clock|CLKOS_inferred_clock     FD1P3IX     D       un3_sample_sum_s_55_0_S0       4.946        0.717
signal_process.demodu.sample_sum[53]     global_clock|CLKOS_inferred_clock     FD1P3IX     D       un3_sample_sum_cry_53_0_S0     4.946        0.777
signal_process.demodu.sample_sum[54]     global_clock|CLKOS_inferred_clock     FD1P3IX     D       un3_sample_sum_cry_53_0_S1     4.946        0.777
signal_process.demodu.sample_sum[51]     global_clock|CLKOS_inferred_clock     FD1P3IX     D       un3_sample_sum_cry_51_0_S0     4.946        0.839
signal_process.demodu.sample_sum[52]     global_clock|CLKOS_inferred_clock     FD1P3IX     D       un3_sample_sum_cry_51_0_S1     4.946        0.839
signal_process.demodu.AD_validcnt[7]     global_clock|CLKOS_inferred_clock     FD1S3IX     D       un1_AD_validcnt_1[7]           4.946        0.896
signal_process.demodu.sample_sum[49]     global_clock|CLKOS_inferred_clock     FD1P3IX     D       un3_sample_sum_cry_49_0_S0     4.946        0.899
signal_process.demodu.sample_sum[50]     global_clock|CLKOS_inferred_clock     FD1P3IX     D       un3_sample_sum_cry_49_0_S1     4.946        0.899
signal_process.demodu.AD_validcnt[5]     global_clock|CLKOS_inferred_clock     FD1S3IX     D       un1_AD_validcnt_1[5]           4.946        0.958
signal_process.demodu.AD_validcnt[6]     global_clock|CLKOS_inferred_clock     FD1S3IX     D       un1_AD_validcnt_1[6]           4.946        0.958
====================================================================================================================================================



<a name=worstPaths24></a>Worst Path Information</a>
<a href="D:\Project\TLH50_03J_JZ_20250925\impl1\INS350_5J_JZ_impl1.srr:srsfD:\Project\TLH50_03J_JZ_20250925\impl1\INS350_5J_JZ_impl1.srs:fp:113693:123863:@XP_NAMES_GATE">View Worst Path in Analyst</a>
***********************


Path information for path number 1: 
      Requested Period:                      5.000
    - Setup time:                            0.054
    + Clock delay at ending point:           0.000 (ideal)
    = Required time:                         4.946

    - Propagation time:                      4.229
    - Clock delay at starting point:         0.000 (ideal)
    = Slack (non-critical) :                 0.716

    Number of logic level(s):                29
    Starting point:                          signal_process.demodu.sample_sum[0] / Q
    Ending point:                            signal_process.demodu.sample_sum[55] / D
    The start point is clocked by            global_clock|CLKOS_inferred_clock [rising] (rise=0.000 fall=2.500 period=5.000) on pin CK
    The end   point is clocked by            global_clock|CLKOS_inferred_clock [rising] (rise=0.000 fall=2.500 period=5.000) on pin CK

Instance / Net                                                Pin      Pin               Arrival     No. of    
Name                                              Type        Name     Dir     Delay     Time        Fan Out(s)
---------------------------------------------------------------------------------------------------------------
signal_process.demodu.sample_sum[0]               FD1P3IX     Q        Out     0.985     0.985 r     -         
sample_sum[0]                                     Net         -        -       -         -           4         
signal_process.demodu.un3_sample_sum_cry_0_0      CCU2C       A1       In      0.000     0.985 r     -         
signal_process.demodu.un3_sample_sum_cry_0_0      CCU2C       COUT     Out     0.900     1.885 r     -         
un3_sample_sum_cry_0                              Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_1_0      CCU2C       CIN      In      0.000     1.885 r     -         
signal_process.demodu.un3_sample_sum_cry_1_0      CCU2C       COUT     Out     0.061     1.946 r     -         
un3_sample_sum_cry_2                              Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_3_0      CCU2C       CIN      In      0.000     1.946 r     -         
signal_process.demodu.un3_sample_sum_cry_3_0      CCU2C       COUT     Out     0.061     2.007 r     -         
un3_sample_sum_cry_4                              Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_5_0      CCU2C       CIN      In      0.000     2.007 r     -         
signal_process.demodu.un3_sample_sum_cry_5_0      CCU2C       COUT     Out     0.061     2.068 r     -         
un3_sample_sum_cry_6                              Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_7_0      CCU2C       CIN      In      0.000     2.068 r     -         
signal_process.demodu.un3_sample_sum_cry_7_0      CCU2C       COUT     Out     0.061     2.129 r     -         
un3_sample_sum_cry_8                              Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_9_0      CCU2C       CIN      In      0.000     2.129 r     -         
signal_process.demodu.un3_sample_sum_cry_9_0      CCU2C       COUT     Out     0.061     2.190 r     -         
un3_sample_sum_cry_10                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_11_0     CCU2C       CIN      In      0.000     2.190 r     -         
signal_process.demodu.un3_sample_sum_cry_11_0     CCU2C       COUT     Out     0.061     2.251 r     -         
un3_sample_sum_cry_12                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_13_0     CCU2C       CIN      In      0.000     2.251 r     -         
signal_process.demodu.un3_sample_sum_cry_13_0     CCU2C       COUT     Out     0.061     2.312 r     -         
un3_sample_sum_cry_14                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_15_0     CCU2C       CIN      In      0.000     2.312 r     -         
signal_process.demodu.un3_sample_sum_cry_15_0     CCU2C       COUT     Out     0.061     2.373 r     -         
un3_sample_sum_cry_16                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_17_0     CCU2C       CIN      In      0.000     2.373 r     -         
signal_process.demodu.un3_sample_sum_cry_17_0     CCU2C       COUT     Out     0.061     2.434 r     -         
un3_sample_sum_cry_18                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_19_0     CCU2C       CIN      In      0.000     2.434 r     -         
signal_process.demodu.un3_sample_sum_cry_19_0     CCU2C       COUT     Out     0.061     2.495 r     -         
un3_sample_sum_cry_20                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_21_0     CCU2C       CIN      In      0.000     2.495 r     -         
signal_process.demodu.un3_sample_sum_cry_21_0     CCU2C       COUT     Out     0.061     2.556 r     -         
un3_sample_sum_cry_22                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_23_0     CCU2C       CIN      In      0.000     2.556 r     -         
signal_process.demodu.un3_sample_sum_cry_23_0     CCU2C       COUT     Out     0.061     2.617 r     -         
un3_sample_sum_cry_24                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_25_0     CCU2C       CIN      In      0.000     2.617 r     -         
signal_process.demodu.un3_sample_sum_cry_25_0     CCU2C       COUT     Out     0.061     2.678 r     -         
un3_sample_sum_cry_26                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_27_0     CCU2C       CIN      In      0.000     2.678 r     -         
signal_process.demodu.un3_sample_sum_cry_27_0     CCU2C       COUT     Out     0.061     2.739 r     -         
un3_sample_sum_cry_28                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_29_0     CCU2C       CIN      In      0.000     2.739 r     -         
signal_process.demodu.un3_sample_sum_cry_29_0     CCU2C       COUT     Out     0.061     2.800 r     -         
un3_sample_sum_cry_30                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_31_0     CCU2C       CIN      In      0.000     2.800 r     -         
signal_process.demodu.un3_sample_sum_cry_31_0     CCU2C       COUT     Out     0.061     2.861 r     -         
un3_sample_sum_cry_32                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_33_0     CCU2C       CIN      In      0.000     2.861 r     -         
signal_process.demodu.un3_sample_sum_cry_33_0     CCU2C       COUT     Out     0.061     2.922 r     -         
un3_sample_sum_cry_34                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_35_0     CCU2C       CIN      In      0.000     2.922 r     -         
signal_process.demodu.un3_sample_sum_cry_35_0     CCU2C       COUT     Out     0.061     2.983 r     -         
un3_sample_sum_cry_36                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_37_0     CCU2C       CIN      In      0.000     2.983 r     -         
signal_process.demodu.un3_sample_sum_cry_37_0     CCU2C       COUT     Out     0.061     3.044 r     -         
un3_sample_sum_cry_38                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_39_0     CCU2C       CIN      In      0.000     3.044 r     -         
signal_process.demodu.un3_sample_sum_cry_39_0     CCU2C       COUT     Out     0.061     3.105 r     -         
un3_sample_sum_cry_40                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_41_0     CCU2C       CIN      In      0.000     3.105 r     -         
signal_process.demodu.un3_sample_sum_cry_41_0     CCU2C       COUT     Out     0.061     3.166 r     -         
un3_sample_sum_cry_42                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_43_0     CCU2C       CIN      In      0.000     3.166 r     -         
signal_process.demodu.un3_sample_sum_cry_43_0     CCU2C       COUT     Out     0.061     3.227 r     -         
un3_sample_sum_cry_44                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_45_0     CCU2C       CIN      In      0.000     3.227 r     -         
signal_process.demodu.un3_sample_sum_cry_45_0     CCU2C       COUT     Out     0.061     3.288 r     -         
un3_sample_sum_cry_46                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_47_0     CCU2C       CIN      In      0.000     3.288 r     -         
signal_process.demodu.un3_sample_sum_cry_47_0     CCU2C       COUT     Out     0.061     3.349 r     -         
un3_sample_sum_cry_48                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_49_0     CCU2C       CIN      In      0.000     3.349 r     -         
signal_process.demodu.un3_sample_sum_cry_49_0     CCU2C       COUT     Out     0.061     3.410 r     -         
un3_sample_sum_cry_50                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_51_0     CCU2C       CIN      In      0.000     3.410 r     -         
signal_process.demodu.un3_sample_sum_cry_51_0     CCU2C       COUT     Out     0.061     3.471 r     -         
un3_sample_sum_cry_52                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_cry_53_0     CCU2C       CIN      In      0.000     3.471 r     -         
signal_process.demodu.un3_sample_sum_cry_53_0     CCU2C       COUT     Out     0.061     3.532 r     -         
un3_sample_sum_cry_54                             Net         -        -       -         -           1         
signal_process.demodu.un3_sample_sum_s_55_0       CCU2C       CIN      In      0.000     3.532 r     -         
signal_process.demodu.un3_sample_sum_s_55_0       CCU2C       S0       Out     0.698     4.229 r     -         
un3_sample_sum_s_55_0_S0                          Net         -        -       -         -           1         
signal_process.demodu.sample_sum[55]              FD1P3IX     D        In      0.000     4.229 r     -         
===============================================================================================================




====================================
<a name=clockReport25></a>Detailed Report for Clock: System</a>
====================================



<a name=startingSlack26></a>Starting Points with Worst Slack</a>
********************************

                                             Starting                                                   Arrival          
Instance                                     Reference     Type           Pin     Net                   Time        Slack
                                             Clock                                                                       
-------------------------------------------------------------------------------------------------------------------------
signal_process.demodu.fifo.AND2_t4           System        AND2           Z       wren_i                0.000       2.317
signal_process.demodu.fifo.AND2_t3           System        AND2           Z       rden_i                0.000       2.559
signal_process.rs422.un3_p_sum[36:71]        System        MULT18X18D     P0      un3_p_sum_add         0.000       2.861
signal_process.rs422.un3_p_sum[36:71]        System        MULT18X18D     P0      un3_p_sum_add         0.000       2.861
signal_process.rs422.un3_p_sum_add[0:53]     System        ALU54B         R36     un3_p_sum_add[36]     0.000       2.861
signal_process.rs422.un3_p_sum[36:71]        System        MULT18X18D     P1      un3_p_sum_2[1]        0.000       2.921
signal_process.rs422.un3_p_sum[36:71]        System        MULT18X18D     P1      un3_p_sum_2[1]        0.000       2.921
signal_process.rs422.un3_p_sum[36:71]        System        MULT18X18D     P2      un3_p_sum_2[2]        0.000       2.921
signal_process.rs422.un3_p_sum[36:71]        System        MULT18X18D     P2      un3_p_sum_2[2]        0.000       2.921
signal_process.rs422.un3_p_sum_add[0:53]     System        ALU54B         R37     un3_p_sum_add[37]     0.000       2.921
=========================================================================================================================


<a name=endingSlack27></a>Ending Points with Worst Slack</a>
******************************

                                     Starting                                            Required          
Instance                             Reference     Type        Pin     Net               Time         Slack
                                     Clock                                                                 
-----------------------------------------------------------------------------------------------------------
signal_process.demodu.fifo.FF_18     System        FD1S3DX     D       full_d            4.946        2.317
signal_process.demodu.fifo.FF_19     System        FD1S3BX     D       empty_d           4.946        2.559
signal_process.rs422.p_sum[54]       System        FD1P3DX     D       un3_p_sum[53]     4.946        2.861
signal_process.rs422.p_sum[55]       System        FD1P3DX     D       un3_p_sum[54]     4.946        2.861
signal_process.rs422.p_sum[52]       System        FD1P3DX     D       un3_p_sum[51]     4.946        2.921
signal_process.rs422.p_sum[53]       System        FD1P3DX     D       un3_p_sum[52]     4.946        2.921
signal_process.rs422.p_sum[50]       System        FD1P3DX     D       un3_p_sum[49]     4.946        2.982
signal_process.rs422.p_sum[51]       System        FD1P3DX     D       un3_p_sum[50]     4.946        2.982
signal_process.rs422.p_sum[48]       System        FD1P3DX     D       un3_p_sum[47]     4.946        3.043
signal_process.rs422.p_sum[49]       System        FD1P3DX     D       un3_p_sum[48]     4.946        3.043
===========================================================================================================



<a name=worstPaths28></a>Worst Path Information</a>
<a href="D:\Project\TLH50_03J_JZ_20250925\impl1\INS350_5J_JZ_impl1.srr:srsfD:\Project\TLH50_03J_JZ_20250925\impl1\INS350_5J_JZ_impl1.srs:fp:128980:130525:@XP_NAMES_GATE">View Worst Path in Analyst</a>
***********************


Path information for path number 1: 
      Requested Period:                      5.000
    - Setup time:                            0.054
    + Clock delay at ending point:           0.000 (ideal)
    = Required time:                         4.946

    - Propagation time:                      2.630
    - Clock delay at starting point:         0.000 (ideal)
    - Estimated clock delay at start point:  -0.000
    = Slack (non-critical) :                 2.316

    Number of logic level(s):                4
    Starting point:                          signal_process.demodu.fifo.AND2_t4 / Z
    Ending point:                            signal_process.demodu.fifo.FF_18 / D
    The start point is clocked by            System [rising]
    The end   point is clocked by            global_clock|CLKOS_inferred_clock [rising] (rise=0.000 fall=2.500 period=5.000) on pin CK

Instance / Net                                      Pin      Pin               Arrival     No. of    
Name                                   Type         Name     Dir     Delay     Time        Fan Out(s)
-----------------------------------------------------------------------------------------------------
signal_process.demodu.fifo.AND2_t4     AND2         Z        Out     0.000     0.000 r     -         
wren_i                                 Net          -        -       -         -           21        
signal_process.demodu.fifo.INV_5       INV          A        In      0.000     0.000 r     -         
signal_process.demodu.fifo.INV_5       INV          Z        Out     0.426     0.426 f     -         
wren_i_inv                             Net          -        -       -         -           1         
signal_process.demodu.fifo.g_cmp_3     CCU2C        B1       In      0.000     0.426 f     -         
signal_process.demodu.fifo.g_cmp_3     CCU2C        COUT     Out     0.900     1.326 r     -         
cmp_ge_d1_c                            Net          -        -       -         -           1         
signal_process.demodu.fifo.a1          CCU2C        CIN      In      0.000     1.326 r     -         
signal_process.demodu.fifo.a1          CCU2C        S0       Out     0.698     2.023 r     -         
cmp_ge_d1                              Net          -        -       -         -           1         
signal_process.demodu.fifo.LUT4_2      ROM16X1A     AD2      In      0.000     2.023 r     -         
signal_process.demodu.fifo.LUT4_2      ROM16X1A     DO0      Out     0.606     2.630 r     -         
full_d                                 Net          -        -       -         -           1         
signal_process.demodu.fifo.FF_18       FD1S3DX      D        In      0.000     2.630 r     -         
=====================================================================================================



##### END OF TIMING REPORT #####]

Timing exceptions that could not be applied

Finished final timing analysis (Real Time elapsed 0h:00m:08s; CPU Time elapsed 0h:00m:07s; Memory used current: 213MB peak: 220MB)


Finished timing report (Real Time elapsed 0h:00m:08s; CPU Time elapsed 0h:00m:07s; Memory used current: 213MB peak: 220MB)

---------------------------------------
<a name=resourceUsage29></a>Resource Usage Report</a>
Part: lfe5u_25f-7

Register bits: 1044 of 24288 (4%)
PIC Latch:       0
I/O cells:       32
Block Rams : 2 of 56 (3%)

DSP primitives:       5 of 42 (11%)

Details:
ALU54B:         1
AND2:           3
BB:             1
CCU2C:          241
EHXPLLL:        1
FD1P3AX:        371
FD1P3BX:        3
FD1P3DX:        154
FD1P3IX:        111
FD1S3AX:        250
FD1S3BX:        3
FD1S3DX:        109
FD1S3IX:        9
FD1S3JX:        6
GSR:            1
IB:             13
IFS1P3DX:       13
INV:            9
MULT18X18D:     4
OB:             18
OFS1P3DX:       15
ORCALUT4:       281
PDPW16KD:       2
PFUMX:          3
PUR:            1
ROM16X1A:       2
VHI:            15
VLO:            15
XOR2:           1
Mapper successful!

At Mapper Exit (Real Time elapsed 0h:00m:08s; CPU Time elapsed 0h:00m:07s; Memory used current: 89MB peak: 220MB)

Process took 0h:00m:08s realtime, 0h:00m:08s cputime
# Thu Sep 25 10:29:35 2025

###########################################################]

</pre></samp></body></html>
