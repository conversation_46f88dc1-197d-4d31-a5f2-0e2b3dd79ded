D:\Software\lscc\diamond\3.12\synpbase\bin64\m_gen_lattice.exe  -prodtype  synplify_pro   -encrypt  -pro  -rundir  D:\Project\TLH50_03J_JZ_20250925\impl1   -part LFE5U_25F  -package BG256C  -grade -7    -maxfan 1000 -pipe -infer_seqShift -forcenogsr -fixgatedclocks 1 -fixgeneratedclocks 1 -RWCheckOnRam 1 -gcc_allow_lat_combloops 0 -gcc_disable_clock_latches 0 -gcc_convert_lats_to_regs 0 -use_rename_in_edif 1 -Write_declared_clocks_only 1 -enable_gcc_in_premap -seqshift_no_replicate 0 -summaryfile D:\Project\TLH50_03J_JZ_20250925\impl1\synlog\report\INS350_5J_JZ_impl1_premap.xml -merge_inferred_clocks 0  -top_level_module  INS350_5J_JZ  -implementation  impl1  -oedif  D:\Project\TLH50_03J_JZ_20250925\impl1\INS350_5J_JZ_impl1.edi  -conchk_prepass  D:\Project\TLH50_03J_JZ_20250925\impl1\INS350_5J_JZ_impl1_cck.rpt   -freq 200.000   D:\Project\TLH50_03J_JZ_20250925\impl1\synwork\INS350_5J_JZ_impl1_mult.srs  -flow prepass  -gcc_prepass  -osrd  D:\Project\TLH50_03J_JZ_20250925\impl1\synwork\INS350_5J_JZ_impl1_prem.srd  -devicelib  D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v  -devicelib  D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\pmi_def.v  -ologparam  D:\Project\TLH50_03J_JZ_20250925\impl1\syntmp\INS350_5J_JZ_impl1.plg  -osyn  D:\Project\TLH50_03J_JZ_20250925\impl1\synwork\INS350_5J_JZ_impl1_prem.srd  -prjdir  D:\Project\TLH50_03J_JZ_20250925\impl1\  -prjname  proj_1  -log  D:\Project\TLH50_03J_JZ_20250925\impl1\synlog\INS350_5J_JZ_impl1_premap.srr  -sn  2021.03  -jobname  "premap" 
relcom:..\..\..\..\Software\lscc\diamond\3.12\synpbase\bin64\m_gen_lattice.exe -prodtype synplify_pro -encrypt -pro -rundir ..\..\impl1 -part LFE5U_25F -package BG256C -grade -7 -maxfan 1000 -pipe -infer_seqShift -forcenogsr -fixgatedclocks 1 -fixgeneratedclocks 1 -RWCheckOnRam 1 -gcc_allow_lat_combloops 0 -gcc_disable_clock_latches 0 -gcc_convert_lats_to_regs 0 -use_rename_in_edif 1 -Write_declared_clocks_only 1 -enable_gcc_in_premap -seqshift_no_replicate 0 -summaryfile ..\synlog\report\INS350_5J_JZ_impl1_premap.xml -merge_inferred_clocks 0 -top_level_module INS350_5J_JZ -implementation impl1 -oedif ..\INS350_5J_JZ_impl1.edi -conchk_prepass ..\INS350_5J_JZ_impl1_cck.rpt -freq 200.000 ..\synwork\INS350_5J_JZ_impl1_mult.srs -flow prepass -gcc_prepass -osrd ..\synwork\INS350_5J_JZ_impl1_prem.srd -devicelib ..\..\..\..\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v -devicelib ..\..\..\..\Software\lscc\diamond\3.12\synpbase\lib\lucent\pmi_def.v -ologparam INS350_5J_JZ_impl1.plg -osyn ..\synwork\INS350_5J_JZ_impl1_prem.srd -prjdir ..\ -prjname proj_1 -log ..\synlog\INS350_5J_JZ_impl1_premap.srr -sn 2021.03 -jobname "premap"
rc:1 success:1 runtime:2
file:..\INS350_5J_JZ_impl1.edi|io:o|time:1758167085|size:1206438|exec:0|csum:
file:..\INS350_5J_JZ_impl1_cck.rpt|io:o|time:1758767366|size:4898|exec:0|csum:
file:..\synwork\INS350_5J_JZ_impl1_mult.srs|io:i|time:1758767364|size:5669|exec:0|csum:6D5BF93A43FFFA1226F6CA68EE82286A
file:..\synwork\INS350_5J_JZ_impl1_prem.srd|io:o|time:1758767366|size:79893|exec:0|csum:
file:..\..\..\..\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v|io:i|time:1640161406|size:89974|exec:0|csum:85BD439A14EE708460D98A7CDD285D98
file:..\..\..\..\Software\lscc\diamond\3.12\synpbase\lib\lucent\pmi_def.v|io:i|time:1640161408|size:40584|exec:0|csum:62845CEC2BB6FEBE1BA059B1E18015F4
file:INS350_5J_JZ_impl1.plg|io:o|time:1758767365|size:0|exec:0|csum:
file:..\synwork\INS350_5J_JZ_impl1_prem.srd|io:o|time:1758767366|size:79893|exec:0|csum:
file:..\synlog\INS350_5J_JZ_impl1_premap.srr|io:o|time:1758767366|size:20766|exec:0|csum:
file:..\..\..\..\Software\lscc\diamond\3.12\synpbase\bin64\m_gen_lattice.exe|io:i|time:1640163250|size:41904128|exec:1|csum:850A26577BE3A8A2CCBAB19E7B99C0D2
