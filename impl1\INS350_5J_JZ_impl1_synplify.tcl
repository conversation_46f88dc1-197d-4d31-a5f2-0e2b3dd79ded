#-- Lattice Semiconductor Corporation Ltd.
#-- Synplify OEM project file

#device options
set_option -technology ECP5U
set_option -part LFE5U_25F
set_option -package BG256C
set_option -speed_grade -7

#compilation/mapping options
set_option -symbolic_fsm_compiler true
set_option -resource_sharing true

#use verilog 2001 standard option
set_option -vlog_std v2001

#map options
set_option -frequency 200
set_option -maxfan 1000
set_option -auto_constrain_io 0
set_option -disable_io_insertion false
set_option -retiming false; set_option -pipe true
set_option -force_gsr false
set_option -compiler_compatible 0
set_option -dup false

set_option -default_enum_encoding default

#simulation options


#timing analysis options



#automatic place and route (vendor) options
set_option -write_apr_constraint 1

#synplifyPro options
set_option -fix_gated_and_generated_clocks 1
set_option -update_models_cp 0
set_option -resolve_multiple_driver 0


set_option -seqshift_no_replicate 0

#-- add_file options
set_option -include_path {D:/Project/TLH50_03J_JZ_20250925}
add_file -verilog -vlog_std v2001 {D:/Project/TLH50_03J_JZ_20250925/Src_al/INS350_5J_JZ.v}
add_file -verilog -vlog_std v2001 {D:/Project/TLH50_03J_JZ_20250925/Src_al/Src_al/SignalProcessing.v}
add_file -verilog -vlog_std v2001 {D:/Project/TLH50_03J_JZ_20250925/Src_al/Src_al/speed_select_Tx.v}
add_file -verilog -vlog_std v2001 {D:/Project/TLH50_03J_JZ_20250925/Src_al/Src_al/SquareWaveGenerator.v}
add_file -verilog -vlog_std v2001 {D:/Project/TLH50_03J_JZ_20250925/Src_al/Src_al/UART_Control.v}
add_file -verilog -vlog_std v2001 {D:/Project/TLH50_03J_JZ_20250925/Src_al/Src_al/uart_tx.v}
add_file -verilog -vlog_std v2001 {D:/Project/TLH50_03J_JZ_20250925/Src_al/Src_al/SignalGenerator.v}
add_file -verilog -vlog_std v2001 {D:/Project/TLH50_03J_JZ_20250925/Src_al/Src_al/Ctrl_Data.v}
add_file -verilog -vlog_std v2001 {D:/Project/TLH50_03J_JZ_20250925/Src_al/Src_al/DS18B20.v}
add_file -verilog -vlog_std v2001 {D:/Project/TLH50_03J_JZ_20250925/Src_al/Src_al/Integration.v}
add_file -verilog -vlog_std v2001 {D:/Project/TLH50_03J_JZ_20250925/Src_al/Src_al/Modulation.v}
add_file -verilog -vlog_std v2001 {D:/Project/TLH50_03J_JZ_20250925/Src_al/Src_al/Rs422Output.v}
add_file -verilog -vlog_std v2001 {D:/Project/TLH50_03J_JZ_20250925/Src_al/Demodulation.v}
add_file -verilog -vlog_std v2001 {D:/Project/TLH50_03J_JZ_20250925/IP_al/global_clock1/Asys_fifo56X16/Asys_fifo56X16.v}
add_file -verilog -vlog_std v2001 {D:/Project/TLH50_03J_JZ_20250925/IP_al/global_clock1/global_clock/global_clock.v}

#-- top module name
set_option -top_module INS350_5J_JZ

#-- set result format/file last
project -result_file {D:/Project/TLH50_03J_JZ_20250925/impl1/INS350_5J_JZ_impl1.edi}

#-- error message log file
project -log_file {INS350_5J_JZ_impl1.srf}

#-- set any command lines input by customer


#-- run Synplify with 'arrange HDL file'
project -run -clean
