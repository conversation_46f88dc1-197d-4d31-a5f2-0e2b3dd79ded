<?xml version="1.0" encoding="UTF-8"?>
<BuildStatus>
    <Strategy name="Strategy1">
        <Milestone name="Export" build_result="0" build_time="0">
            <Task name="IBIS" build_result="0" update_result="2" update_time="1758767404"/>
            <Task name="TimingSimFileVlg" build_result="0" update_result="2" update_time="1758767404"/>
            <Task name="TimingSimFileVHD" build_result="0" update_result="2" update_time="1758767404"/>
            <Task name="Bitgen" build_result="2" update_result="0" update_time="1758767410"/>
            <Task name="Promgen" build_result="0" update_result="2" update_time="1758767407"/>
        </Milestone>
        <Milestone name="Map" build_result="2" build_time="1758767379">
            <Task name="Map" build_result="2" update_result="0" update_time="1758767379"/>
            <Task name="MapTrace" build_result="2" update_result="0" update_time="1758767382"/>
            <Task name="MapVerilogSimFile" build_result="0" update_result="2" update_time="1758767379"/>
            <Task name="MapVHDLSimFile" build_result="0" update_result="2" update_time="1758767379"/>
        </Milestone>
        <Milestone name="PAR" build_result="2" build_time="1758767404">
            <Task name="PAR" build_result="2" update_result="0" update_time="1758767404"/>
            <Task name="PARTrace" build_result="2" update_result="0" update_time="1758767407"/>
            <Task name="IOTiming" build_result="0" update_result="2" update_time="1758767404"/>
        </Milestone>
        <Milestone name="Synthesis" build_result="2" build_time="1758767376">
            <Task name="Synplify_Synthesis" build_result="2" update_result="0" update_time="1758767376"/>
        </Milestone>
        <Milestone name="TOOL_Report" build_result="0" build_time="0">
            <Task name="HDLE" build_result="2" update_result="0" update_time="1758767106"/>
            <Task name="BKM" build_result="0" update_result="2" update_time="1758167054"/>
            <Task name="SSO" build_result="0" update_result="2" update_time="1758767378"/>
            <Task name="PIODRC" build_result="0" update_result="2" update_time="1758767378"/>
            <Task name="DEC" build_result="0" update_result="2" update_time="1758767378"/>
        </Milestone>
        <Milestone name="Translate" build_result="2" build_time="1758767378">
            <Task name="Translate" build_result="2" update_result="0" update_time="1758767378"/>
        </Milestone>
        <Report name=".vdbs/INS350_5J_JZ_impl1_map.vdb" last_build_time="1742287551" last_build_size="359381"/>
        <Report name="INS350_5J_JZ_impl1.bgn" last_build_time="1758767410" last_build_size="4487"/>
        <Report name="INS350_5J_JZ_impl1.bit" last_build_time="1758767410" last_build_size="582677"/>
        <Report name="INS350_5J_JZ_impl1.edi" last_build_time="1758767374" last_build_size="1206438"/>
        <Report name="INS350_5J_JZ_impl1.lsedata" last_build_time="1742284331" last_build_size="1417397"/>
        <Report name="INS350_5J_JZ_impl1.mcs" last_build_time="1742868162" last_build_size="1638935"/>
        <Report name="INS350_5J_JZ_impl1.ncd" last_build_time="1758767404" last_build_size="1376230"/>
        <Report name="INS350_5J_JZ_impl1.ngd" last_build_time="1758767378" last_build_size="1048697"/>
        <Report name="INS350_5J_JZ_impl1.tw1" last_build_time="1758767381" last_build_size="33140"/>
        <Report name="INS350_5J_JZ_impl1.twr" last_build_time="1758767407" last_build_size="237901"/>
        <Report name="INS350_5J_JZ_impl1_map.ncd" last_build_time="1758767379" last_build_size="1034073"/>
    </Strategy>
</BuildStatus>
                                       